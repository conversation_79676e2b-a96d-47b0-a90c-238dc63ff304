# Architecture Decision Record (ADR)

## 4. Metrics Definition

Date: 2022-05-18

## Status

OPEN

## Context

As one of the observability, metrics can be divided into operational and business index.

For operational metrics, it includes latency, traffic, errors and saturation.

For business metrics, it includes service orientated index.

## Decision

### Operational

#### Overall Request Metrics
Istio innately generates detailed telemetry within the mesh, it provides a set of service metrics based on latency, traffic, errors and saturation.

There are standard metrics exported by Istio for HTTP traffic:
* Request Count (istio_requests_total), COUNTER type
* Request Duration (istio_request_duration_milliseconds), DISTRIBUTION type
* Request Size (istio_request_bytes), DISTRIBUTION type

But the [dimensions](https://istio.io/latest/docs/concepts/observability/#service-level-metrics) of the metrics do not include the HTTP path and url, so Istio metrics cannot be used to build application specific dashboard. 

#### Endpoint Request Metrics
* Request count for timeseries
* Request duration for timeseries
* Error rate for timeseries

SpringBoot Actuator exposes prometheus endpoint and includes Micrometer, with Micrometer registry dependency which specifically enables Prometheus support, this allows the metrics collected by Micrometer to exposed in a Prometheus way.

SpringBoot autoconfigures for Micrometer, it enables the instrumentation of requests handled by SpringMVC.

By default, SpringMVC-related metrics are tagged with the following information:
* The request’s method.
* The request’s URI (templated if possible).
* The simple class name of any exception that was thrown while handling the request.
* The response’s status.

Based on above tags, we can build the application specific dashboard.

### Business

The business metrics depend on the business analysis, e.g. payment success rate.

Metrics will be collected in later stage once the business needed.

## Consequences


