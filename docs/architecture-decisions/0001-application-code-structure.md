# Architecture Decision Record (ADR)

## 1. Application code structure

Date: 2020-04-24

## Status

Accepted

## Context

There are some considerations about determining the practices that need to be followed when set up the application structure.  
Following are some materials that we referred:
- [Screaming Architecture](https://blog.cleancoder.com/uncle-bob/2011/09/30/Screaming-Architecture.html)
- [CQRS](https://martinfowler.com/bliki/CQRS.html) 
- [CQRS pattern](https://docs.microsoft.com/en-us/azure/architecture/patterns/cqrs)
- [Software Architecture](https://herbertograca.com/2017/11/16/explicit-architecture-01-ddd-hexagonal-onion-clean-cqrs-how-i-put-it-all-together/#:~:text=The%20Onion%20Architecture%20picks%20up,direction%20is%20towards%20the%20centre.)
- [Microservice Testing](https://martinfowler.com/articles/microservice-testing/)
- [Pact Contract Testing](https://docs.pact.io/)

## Decision

1. Put business friendly names for top level directories
2. Refer onion structure in domain layer
3. Refer CQRS in resource layer
4. Separate folders for different types of tests, including: unit tests, integration tests, component tests & contract tests.
5. Add ArchUnit tests to make sure the application structure rules are followed 

## Consequences

New codes need to follow the structure defined and enhance ArchUnit tests when there are new rules added or some cases not covered.
