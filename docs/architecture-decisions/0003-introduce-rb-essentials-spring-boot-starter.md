# Architecture Decision Record (ADR)

##  Introduce rb-essentials-spring-boot-starter

Date: 2022-07-19

## Status

Accepted

## Context

There is a RB essentials spring boot starter that helps to capture the defaults and recommendations for services built on top of Spring Boot in Ritchie Bros,
so teams can focus on delivering value to customers. [[Repo of this Package]](https://github.com/RBMarketplace/rb-essentials-spring-boot)

## Decision

Introduce the package com.rb.essentials:rb-web-spring-boot-starter for defaults and recommended practices to build services.

## Consequences

1. Leverage essential starter for following functionalities
    * Open Spring endpoints for k8s health check
    * Include OpenTelemetryDatadogLogAppender for integrate datadog with Open Telemetry
2. Upgrade version for new functionalities 