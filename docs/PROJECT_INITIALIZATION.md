# Project Initialization

## Create a new Project
#### 1. create `New Project`
![img.png](images/newrepository.png)

---
#### 2. setup repository from template `RBMarkplace/capability-base`
![img_1.png](images/repositoryfrombase.png)

#### 3. add access to teams
![img.png](images/access.png)
- add maintain role to your team
- add admin role to your privileged team
- add read role to `Marketplace Engineering - All` team

## Prerequisite before configure
#### 1. create database

Please follow the guide [here](https://github.com/RBMarketplace/di-documentation/blob/main/doc/how-to%2Fcreate-databases.md) to create database.

#### 2. create circleci api token

Have a single team member follow [this guide](https://circleci.com/docs/managing-api-tokens/#creating-a-personal-api-token)

## Configure the new project
1. Git clone and open your project
2. Replace placeholders `capability base`, `capability-base` to your project name
3. Replace placeholders `marketplace-excavator` with your team name
   a) Note that if your Vault namespace is not the same as your team name, you will have to update the `namespace` parameters in `secret/export-secret-to-env` CircleCI steps.
4. Replace placeholders `excavator-` (`{dev/qa/prod}`) with your Kubernetes namespaces
5. Rename `CapabilityBaseApplication.java` with your application name
6. Rename packages `exampledomain` and `examplesubdomain` to your business domain
7. Replace placeholders `account` to your project name in values-dev/qa/prod.yaml
8. Database settings:
   - set `database.host` in values-dev/qa/prod.yaml.
   - set `database.name` in values.yaml.  
   - set `APPLICATION_DB_BASE_NAME` in `local_deployment_ctl.sh` to `local_capability?currentSchema={your_db_name}_$DEVELOPER_USER`
   - enable flyway in `bootstrap.yml` after SQL DDL scripts created
9. Vault settings:
   - set launch darkly sdk_key in dev/qa/prod
   - set slack_access_token in secret/svc/delivery-pipeline/slack
   - set CC_REPORTER_ID in secret/svc/delivery-pipeline/code-climate/REPO_NAME
   - set personal_circleci_token in secret/svc/delivery-pipeline/circleci
