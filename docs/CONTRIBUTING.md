# Contributing doc
This doc contains technical stack, development setup, references to coding guidelines, etc.

---
### Environment Requirements
- Java 17
- Gradle 8.5

### Dev Tools Setup
- Run `sh ./scripts/mac-dev-tools.sh`
- In this way, all dependencies and pre-commit related hooks should be installed properly
- If hooks doesn't properly work, try to run `rm -rf .git/hooks` and reinstall them

### Pre-commit gitleaks
- In pre-commit hooks, there is a tool named gitleaks, which aims to figure out potential secrets in codes
- You can ignore files by adding fingerprints in .gitleaksignore. For example:
```
 application/src/main/java/com/rb/capability/SampleController.java:jwt:27
```

### How to deploy your own application version to Dev namespace effortless with awesome hot reload functionality.
#### Prerequisite:
- Install `Skaffold` and `helm`
```
  brew install skaffold helm
```
- Make sure you have access to `gcr.io` where <PERSON><PERSON> needs it to pull base images from.
- Follow instructions [here](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token) to create your PAT.
    - Make sure you PAT have `write:packages` access.
    - Login to `ghcr.io`.
      ```
      echo $PAT | docker login ghcr.io -u GITUB_USERNAME --password-stdin
      ```
- Setup database
  - Create own database schema if it is not exists.
    - set PgAdmin port-forward: `kubectl -n dozer-dev port-forward deploy/pgadmin4 18080:80`
    - visit PgAdmin UI in browser: http://localhost:18080/pgadmin4
    - login PgAdmin with username(`<EMAIL>`) and password(`admin`)
    - login servers with generated dynamic credential from [vault](https://marketplace-nonprod-vault-cluster.vault.a8ac8847-786e-4c3e-b97a-34e8978986f4.aws.hashicorp.cloud:8200/ui/)(you need access with `OIDC role` `marketplace-{TEAM_NAME}-oidc-role` to visit namespace `marketplace-{TEAM_NAME}` in vault)
    - create new schema with name {SERVICE_NAME}_{DEVELOPER_USERNAME} under `Service-LOCAL\local_capability\Schemas` in PgAdmin UI
#### Instructions
- Run `./scripts/local_deployment_ctl.sh {DEVELOPER_USERNAME} {APPLICATION_NAME}`
- It will deploy 2 local version our helm application into the dev namespace for `Skaffold` to use on hot-reloading.
- Visit `https://api.dev.marketplace.ritchiebros.com/{SERVICE_NAME}-{YOUR_DEVELOPER_USERNAME}-local/{YOUR_DEVELOPER_USERNAME}/actuator/health` to access your local deployed app.

#### Local remote debug
- Run `./scripts/local_deployment_ctl.sh {DEVELOPER_USERNAME} {APPLICATION_NAME} debug`
- This will automatically inject `JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,server=y,address=5005,suspend=n,quiet=y` into our targeted image.
- Add a new `remote debug` configuration and click `debug` in your IDE.
- Add break points and start your debug.

### How to config GitHub PAT
- To download packages from GitHub, the GitHub personal access token should be correctly configured. Add PAT as gradle environment variables, or add it as global environment variable in your system,
   for example, add it in ~/.zshrc file. The variable name should be "GITHUB_PACKAGE_TOKEN".

### How to add a property in application.yaml
1. Define the property in the `helm/templates/configmap.yaml`
2. Define the property in the `helm/values.yaml` or `helm/values-[env].yaml`

### Architecture Doc Record
- location is `docs/architecture-decisions`
- new adr `adr new some adr name`

### OpenAPI
- Swagger living doc location `https://{HOST_NAME}/{PROJECT_NAME}/{VERSION}/swagger-ui/index.html`

### GPG keys configuration
- [GPG Key generation and configuration](https://docs.github.com/en/authentication/managing-commit-signature-verification/generating-a-new-gpg-key)
- [Config local GPG key for signing commits](https://docs.github.com/en/authentication/managing-commit-signature-verification/telling-git-about-your-signing-key)
- [Signing commits](https://docs.github.com/en/authentication/managing-commit-signature-verification/signing-commits)
- [Signing tags](https://docs.github.com/en/authentication/managing-commit-signature-verification/signing-tags)

### Flyway
Flyway is a version control tool for the database world that keeps track of changes to the database.
- `Migration directory` db/migration
- `migration script name` V1649399129__asset_create.sql,begin with V and time stamp,double underline，end with file name
- `timestamp` input in the terminal:date +%s

### Commit Conventions
#### Commit message pattern:

```
[CC-1] this is a pretty commit message\
Co-authored-by: Elon Musk <<EMAIL>>
```

#### git-mob Setup Instructions
- Update hooks through `./scripts/mac-dev-tools.sh`
- Install git-mob `npm i -g git-mob`
- Use a JSON file called ~/.git-coauthors to keep track of potential co-authors  `cp ./.git-coauthors ~/.git-coauthors`

    <details>
      <summary>co-authors schema & example</summary>
    
    **schema**
    ```json
    {
      "coauthors": {
        "<initials>": {
          "name": "<name>",
          "email": "<email>"
        }
      }
    }
    ```
    **example**
    ```json
    {
      "coauthors": {
        "em": {
          "name": "Elon Musk",
          "email": "<EMAIL>"
        },
        "kd": {
          "name": "Kevin Durant",
          "email": "<EMAIL>"
        }
      }
    }
    ```    
    </details>

- Tell git-mob you're pairing with Elon by using his initials `git mob em`, set to solo with `git mob solo`

### test template Setup Instructions
- Preferences -> Editor -> Live Templates -> Add a live template
```text
@Test
void $NAME$() {
    // Arrange
    $END$
    // Act
    
    // Assert
}
```
- abbreviation named tdd, and set Define option to java
we should delete comments when we finish tests.

### Kafka CLI
#### Prerequisite
- Install `kcat`
```
brew install kcat
```
#### Consume record
- Configure broker connection into config file `~/.config/kcat.conf`, sample file:
```properties
bootstrap.servers=[replace broker address here]
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
sasl.username=[replace cluster key here]
sasl.password=[replace cluster token here]
group.id=[replace group id here]
```
- Consume record using avro schema from schema registry
```shell
kcat -K , -C -t [replace topic here] \
-o end -s value=avro \
-r https://[replace SR key here]:[replace SR secret here]@[replace SR url here(remeber to remove https)]
```
`-o end` param will make the console consumer to fetch record from the latest offset

*More about: [kcat](https://github.com/edenhill/kcat)*
