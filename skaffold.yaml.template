apiVersion: skaffold/v2beta28
kind: Config
build:
  artifacts:
    - image: {{ IMAGE }}
      jib:
        project: application
      hooks:
        after:
          - command:
            - sh
            - -c
            - ./scripts/sign-image.sh $(jq -r '.image + "@" + .imageDigest' ./application/build/jib-image.json)
deploy:
  helm:
    releases:
      - chartPath: {{ CHART_PATH }}
        name: {{ APPLICATION_FULL_NAME }}
        setValues:
          database.name: {{ DB_NAME }}
        valuesFiles:
          - {{ CHART_PATH }}/values.yaml
          - {{ CHART_PATH }}/values-local.yaml
        artifactOverrides:
          image: {{ IMAGE }}
        imageStrategy:
          helm: {}
        namespace: auction-management-dev
