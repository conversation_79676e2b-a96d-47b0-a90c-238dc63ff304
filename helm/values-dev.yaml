# Values for dev environment.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

env: dev
namespace: auction-management-dev

virtualService:
  http:
    - prefixUri: /actuator/
      rewriteUri: /actuator/
      version: v1
      port: 9090
    - prefixUri: /
      rewriteUri: /v1/
      version: v1
      port: 8080

database:
  host: dev-auction-management-postgres-v2.cluster-clrz8hnd2njo.us-west-2.rds.amazonaws.com

# Decide how many instances can be down at the same time for a short period due to a voluntary disruption.
podDisruptionBudget:
  # should be enabled on the environments that will be accessed by real users
  enabled: false

resources:
  limits:
    cpu: 2
    memory: 4Gi
  requests:
    cpu: 1
    memory: 4Gi

# automatically scale the number of pods regarding the target metrics
autoscaling:
  # should be enabled on the environments that will be accessed by real users
  enabled: true
  # MUST be greater than podDisruptionBudget/minAvailable, otherwise, the maintenance job would fail
  minReplicas: 3
  # equals or greater than minReplicas
  maxReplicas: 6
  # The CPU utilization is the average CPU usage of a all pods in a deployment across the last minute divided by the requested CPU of this deployment. If the mean of the pods' CPU utilization is higher than the target you defined, the your replicas will be adjusted.
  # refer to `resources.limits` and `resources.requests` in 4. resources above
  targetCPUUtilizationPercentage: 80
  # The memory utilization is the average memory usage of a all pods in a deployment across the last minute divided by the requested memory of this deployment. If the mean of the pods' memory utilization is higher than the target you defined, the your replicas will be adjusted.
  # refer to `resources.limits` and `resources.requests` in 4. resources above
  targetMemoryUtilizationPercentage: 80

internalHost: auction-management.auction-management-dev.svc.cluster.local

vaultInjector:
  enabled: true
  authPath: auth/kubernetes_nonprod
  role: auction-management-kubernetes-role
  creds:
    injectFile: vault.yaml
    injectSecrets:
      - vaultTemplate: |
          {{- with secret "auction-management/database/creds/dev-auction-management-postgres-v2-role" }}
          {{ printf "db_username:" }} {{ .Data.username }}
          {{ printf "db_password:" }} {{ .Data.password }}
          {{- end }}
      - objectName: ld_sdk_key
        secretPath: auction-management/secret/data/svc/dev/launch-darkly
        secretKey: sdk_key
      - objectName: enterprise_kafka_bootstrap_servers
        secretPath: auction-management/secret/data/svc/dev/enterprise-kafka
        secretKey: bootstrap_servers
      - objectName: enterprise_kafka_cluster_key
        secretPath: auction-management/secret/data/svc/dev/enterprise-kafka
        secretKey: cluster_key
      - objectName: enterprise_kafka_cluster_secret
        secretPath: auction-management/secret/data/svc/dev/enterprise-kafka
        secretKey: cluster_secret
      - objectName: enterprise_kafka_sr_url
        secretPath: auction-management/secret/data/svc/dev/enterprise-kafka
        secretKey: sr_url
      - objectName: enterprise_kafka_sr_key
        secretPath: auction-management/secret/data/svc/dev/enterprise-kafka
        secretKey: sr_key
      - objectName: enterprise_kafka_sr_secret
        secretPath: auction-management/secret/data/svc/dev/enterprise-kafka
        secretKey: sr_secret
      - objectName: salesforce_username
        secretPath: auction-management/secret/data/svc/dev/salesforce
        secretKey: username
      - objectName: salesforce_password
        secretPath: auction-management/secret/data/svc/dev/salesforce
        secretKey: password
      - objectName: salesforce_client_id
        secretPath: auction-management/secret/data/svc/dev/salesforce
        secretKey: client_id
      - objectName: salesforce_client_secret
        secretPath: auction-management/secret/data/svc/dev/salesforce
        secretKey: client_secret

application_yaml: |
  #spring (continued from configmap.yaml):
    kafka:
      bootstrap-servers: ${enterprise_kafka_bootstrap_servers}
      properties:
        basic:
          auth:
            user:
              info: ${enterprise_kafka_sr_key}:${enterprise_kafka_sr_secret}
        schema:
          registry:
            url: ${enterprise_kafka_sr_url}
        sasl:
          jaas:
            config: org.apache.kafka.common.security.plain.PlainLoginModule required username='${enterprise_kafka_cluster_key}' password='${enterprise_kafka_cluster_secret}';
      consumer:
        group-id:  marketplace_auction_management-susttest-am
        client-id: marketplace_auction_management-susttest-am-client
      producer:
        client-id: marketplace_auction_management-susttest-am-producer-client
  kafka:
    topics:
      enterprise_mars_events: susttest-enterprise.sale-events
      enterprise_auction: susttest-integration.capability-auction
  places_api:
    base_url: http://places.places-k8s-ns-dev.svc.cluster.local/v1
  legal_entity_api:
    base_url: https://api.dev.marketplace.ritchiebros.com/legal-entity
  salesforce:
    base_url: https://rb--full56.sandbox.my.salesforce.com/services
    username:  ${salesforce_username}
    password: ${salesforce_password}
    client_id: ${salesforce_client_id}
    client_secret: ${salesforce_client_secret}
    retry:
      max-attempts: 3
      initial-delay-ms: 60000
  auction:
    sync:
      bidding:
        flags:
          enabled: true
          processing:
            interval: 30000
            initialDelay: 30000
            lockAtLeastFor: PT1S
            lockAtMostFor: PT60S