{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "application.fullname" . }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
    - port: {{ .Values.service.actuatorPort }}
      targetPort: actuator
      protocol: TCP
      name: http-actuator
  selector:
    {{- include "application.selectorLabels" . | nindent 4 }}
{{ end }}
