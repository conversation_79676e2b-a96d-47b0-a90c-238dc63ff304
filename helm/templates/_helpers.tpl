{{/*
Expand the name of the chart.
*/}}
{{- define "application.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "application.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{- define "attribute.serviceName" -}}
{{- if .Values.attribute.serviceName }}
{{- .Values.attribute.serviceName }}
{{- else }}
{{- include "application.fullname" .}}
{{- end }}
{{- end }}

{{/*
Specify name of developer for local deployments to aid in distinguishing telemetry
*/}}
{{- define "deploymentOwner" -}}
{{- if .Values.deploymentOwnerOverride }}
{{- .Values.deploymentOwnerOverride }}
{{- else }}
{{- .Values.deploymentOwner }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "application.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "application.labels" -}}
helm.sh/chart: {{ include "application.chart" . }}
{{ include "application.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "application.selectorLabels" -}}
app.kubernetes.io/name: {{ include "application.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Version labels
TODO: Be mindful that canary versions is not directly supported here, for now we only pick up the first available's version
here so that we don't accidentally enable unexpected traffic, we need to change the 'version' label assignment here if
we decide to support canary later.
*/}}
{{- define "application.versionLabels" -}}
{{- if .Values.virtualService }}
{{- with (first .Values.virtualService.http) }}
version: {{ .version }}
{{- end }}
{{- else }}
version: default
{{- end }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "application.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "application.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{- define "virtual.host" -}}
{{- if eq .Values.env "prod" -}}
- 'api.marketplace.ritchiebros.com'
{{- else -}}
- 'api.{{ .Values.env }}.marketplace.ritchiebros.com'
{{- end }}
{{- end }}

{{- define "virtual.internalHost" -}}
{{- if eq .Values.env "prod" -}}
- 'internal-api.marketplace.ritchiebros.com'
{{- else -}}
- 'internal-api.{{ .Values.env }}.marketplace.ritchiebros.com'
{{- end }}
{{- end }}

{{- define "virtual.oldHost" -}}
{{- if eq .Values.env "prod" -}}
- 'api.rbmkt.dev'
{{- else -}}
- 'api.{{ .Values.env }}.rbmkt.dev'
{{- end }}
{{- end }}

{{- define "vaultInjector.podAnnotation" -}}
{{- if eq .Values.vaultInjector.enabled true -}}
traffic.sidecar.istio.io/excludeOutboundPorts: "8200"
vault.hashicorp.com/agent-inject: "true"
vault.hashicorp.com/agent-service-account-token-volume-name: service-account-token
vault.hashicorp.com/auth-path: "{{ .Values.vaultInjector.authPath }}"
{{- if .Values.vaultInjector.creds }}
vault.hashicorp.com/agent-inject-secret-{{ .Values.vaultInjector.creds.injectFile }}: ""
vault.hashicorp.com/agent-inject-template-{{ .Values.vaultInjector.creds.injectFile }}: |
{{- range .Values.vaultInjector.creds.injectSecrets }}
{{- if .vaultTemplate }}
{{ .vaultTemplate | indent 2 -}}
{{- else }}
{{`{{- with secret "` | indent 2}}{{ .secretPath }}{{`" }}`}}
{{ .objectName | default .secretKey | indent 2 }}: {{`{{ .Data.data.`}}{{ .secretKey }}{{` }}`}}
{{`{{- end }}` | indent 2 }}
{{- end }}
{{- end }}
{{- end }}
vault.hashicorp.com/agent-inject-status: "update"
vault.hashicorp.com/role: "{{ .Values.vaultInjector.role }}"
vault.hashicorp.com/namespace: "admin"
{{- if eq .Values.vaultInjector.debug true }}
vault.hashicorp.com/log-level: "trace"
{{- end -}}
{{- end -}}
{{- end }}

{{- define "vaultInjector.tokenVolume" -}}
{{- if eq .Values.vaultInjector.enabled true -}}
- name: service-account-token
  projected:
    defaultMode: 420
    sources:
    - serviceAccountToken:
        expirationSeconds: 3607
        path: token
{{- end }}
{{- end }}
