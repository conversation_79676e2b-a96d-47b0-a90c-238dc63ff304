apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "application.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
    tags.datadoghq.com/service: {{ include "application.fullname" . }}
    tags.datadoghq.com/env: {{ .Values.env }}
    tags.datadoghq.com/version: "{{ .Chart.AppVersion }}"
  annotations:
    github.com/repo-slug: "{{ .Values.repo.slug }}"
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "application.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        marketplace.ritchiebros.com/pod-spread-strategy: strict
      {{- include "vaultInjector.podAnnotation" . | nindent 8 }}
      labels:
        {{- include "application.selectorLabels" . | nindent 8 }}
        {{- include "application.versionLabels" . | nindent 8 }}
        date: "{{ now | unixEpoch }}"
        {{- if .Values.istioOPA.enabled }}
        opa-istio-envoyfilter: enabled
        {{- end }}
        tags.datadoghq.com/service: {{ include "application.fullname" . }}
        tags.datadoghq.com/env: {{ .Values.env }}
        tags.datadoghq.com/version: "{{ .Chart.AppVersion }}"
        service.istio.io/canonical-name: {{ include "attribute.serviceName" . }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "application.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          env:
            - name: DD_ENV
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/env']
            - name: DD_SERVICE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/service']
            - name: DD_VERSION
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/version']
            - name: db_name
              value: {{ .Values.database.name }}
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: "http://{{ .Values.otelCollectorHost }}:4319"
            - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
              value: "http://{{ .Values.otelCollectorHost }}:4319"
            - name: HONEYCOMB_API_ENDPOINT
              value: "http://{{ .Values.otelCollectorHost }}:4319"
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: service.name={{ include "attribute.serviceName" . }}, env={{ .Values.env }}, deployment.environment={{ .Values.env }}, deployment.owner={{ .Values.deploymentOwner }}, version={{ .Values.image.tag | default .Chart.AppVersion | quote }}
            - name: OTEL_JAVAAGENT_EXCLUDE_CLASSES
              value: "org.hibernate.query.spi.AbstractSelectionQuery"
            - name: _JAVA_OPTIONS
              value: "-XX:MaxRAMPercentage={{ .Values.java.maxRAMPercentage }}"
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{ if .Values.image.noTagOverride }}
          image: "{{ .Values.image.repository }}"
          {{ else }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          {{ end }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
            - name: actuator
              containerPort: 9090
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: app-yaml
              mountPath: /config
            - name: tomcat-logs-dir
              mountPath: /tmp
          readinessProbe:
            httpGet:
              port: 9090
              path: /actuator/health
          livenessProbe:
            httpGet:
              port: 9090
              path: /actuator/health
          startupProbe:
            httpGet:
              port: 9090
              path: /actuator/health
            failureThreshold: 180
            periodSeconds: 5
          lifecycle:
            preStop:
              exec:
                command: [ "sleep", "10" ]
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      volumes:
        - name: app-yaml
          configMap:
            name: {{ include "application.fullname" . }}
        {{- include "vaultInjector.tokenVolume" . | nindent 8 }}
        - name: tomcat-logs-dir
          emptyDir: { }
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
