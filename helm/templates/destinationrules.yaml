{{- if .Values.service.enabled }}
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: {{ include "application.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  trafficPolicy:
    loadBalancer:
      simple: LEAST_CONN
  {{- if .Values.virtualService.http }}
  host: {{ $.Values.internalHost }}
  {{- else }}
  host: {{ include "application.fullname" . }}.{{ .Values.namespace }}.svc.cluster.local
  {{- end }}
{{ end }}
