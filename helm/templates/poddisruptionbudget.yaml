{{- if .Values.podDisruptionBudget.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "application.fullname" . }}-pdb
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  maxUnavailable: {{ required "A valid .Values.podDisruptionBudget.maxUnavailable required!" .Values.podDisruptionBudget.maxUnavailable }}
  selector:
    matchLabels:
      {{- include "application.selectorLabels" . | nindent 6 }}
  {{- end }}