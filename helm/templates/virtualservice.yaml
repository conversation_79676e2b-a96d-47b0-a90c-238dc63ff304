{{- if .Values.service.enabled }}
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ include "application.name" . }}-mesh
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  gateways:
    - mesh
  hosts:
    - {{ include "application.name" . }}.{{ .Values.namespace }}.svc.cluster.local
  http:
    {{- if .Values.virtualService.http }}
    {{- range $rule := .Values.virtualService.http }}
    - name: "{{ include "application.name" $ }}-routes-internal"
      match:
        - uri:
            prefix: /{{ $rule.version }}{{ $rule.prefixUri }}
      rewrite:
        uri: {{ $rule.rewriteUri }}
      route:
        - destination:
            host: {{ $.Values.internalHost }}
            port:
              number: {{ $rule.port }}
          headers:
            request:
              remove:
                - X-Forwarded-Prefix
              add:
                X-Forwarded-Prefix: /{{ $rule.version }}{{ $rule.prefixUri }}
      retries:
        attempts: 3
        perTryTimeout: 30s
        retryOn: connect-failure
    {{- end }}
    {{- else }}
    - match:
        - uri:
            prefix: /v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: {{ include "application.fullname" . }}.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 80
      retries:
        attempts: 3
        perTryTimeout: 30s
        retryOn: connect-failure
    {{- end }}

{{- if or (.Values.virtualService.bindToPublic) (.Values.virtualService.bindToMtls) }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ include "application.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  gateways:
    {{- if .Values.virtualService.bindToPublic }}
    - istio-system/{{ .Values.env }}-wildcard-gateway
    {{- end }}
    {{- if .Values.virtualService.bindToMtls }}
    - istio-system/{{ .Values.env }}-internal-gateway
    {{- end }}
  hosts:
    {{- if .Values.virtualService.bindToPublic }}
    {{- include "virtual.oldHost" . | nindent 4}}
    {{- include "virtual.host" . | nindent 4}}
    {{- end }}
    {{- if .Values.virtualService.bindToMtls }}
    {{- include "virtual.internalHost" . | nindent 4}}
    {{- end }}  
  http:
    {{- if .Values.virtualService.http }}
    {{- range $rule := .Values.virtualService.http }}
    - name: "{{ include "application.name" $ }}-routes"
      match:
        - uri:
            prefix: /{{ include "application.name" $ }}/{{ $rule.version }}{{ $rule.prefixUri }}
      rewrite:
        uri: {{ $rule.rewriteUri }}
      route:
        - destination:
            host: {{ $.Values.internalHost }}
            port:
              number: {{ $rule.port }}
          headers:
            request:
              remove:
                - X-Forwarded-Prefix
              add:
                X-Forwarded-Prefix: /{{ include "application.name" $ }}/{{ $rule.version }}{{ $rule.prefixUri }}
      retries:
        attempts: 3
        perTryTimeout: 30s
        retryOn: connect-failure
  {{- end }}
  {{- else }}
    - match:
        - uri:
            prefix: /{{ include "application.fullname" . }}/v1
      rewrite:
        uri: /
      route:
        - destination:
            host: {{ include "application.fullname" . }}.{{ .Values.namespace }}.svc.cluster.local
            port:
              number: 80
      retries:
        attempts: 3
        perTryTimeout: 30s
        retryOn: connect-failure
  {{- end }}
{{ end }}
{{ end }}


