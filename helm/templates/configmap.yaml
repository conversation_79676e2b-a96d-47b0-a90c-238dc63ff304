apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "application.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    spring.cloud.kubernetes.config: "true"
data:
  application.yaml: |-
    internal-baseurl: http://{{ .Values.internalHost }}:{{ .Values.service.port }}/v1
    spring:
      application:
        name: {{ include "application.fullname" . }}
      datasource:
        url: jdbc:postgresql://{{ .Values.database.host }}:{{ .Values.database.port }}/{{ .Values.database.name }}

    # please leave spring property tree open so that it can continue in application_yaml in values files
      
      {{- .Values.application_yaml | nindent 4 }}