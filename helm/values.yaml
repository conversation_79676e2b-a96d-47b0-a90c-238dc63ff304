# Default values for application.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
deploymentOwner: pipeline

image:
  pullPolicy: IfNotPresent
  # <PERSON><PERSON><PERSON><PERSON> needs to leave tag empty, add some flexibility here
  noTagOverride: false

repo:
  slug: RBMarketplace/auction-management

imagePullSecrets:
  - name: registry-credential
nameOverride: ""
fullnameOverride: ""

attribute:
  serviceName: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  runAsUser: 1000

# Decide how many instances can be down at the same time for a short period due to a voluntary disruption.
podDisruptionBudget:
  # percent of pods that must still be available after the eviction
  maxUnavailable: 10%

securityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true

service:
  enabled: true
  type: ClusterIP
  port: 8080
  actuatorPort: 9090

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 2
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 512Mi

java:
  # The java/JVM heap settings can also be configured for non-Kubernetes deployments in the
  # build.gradle file. The settings defined here will override the settings in the build.gradle
  # file when deployed via helm into Kubernetes.
  # We prefer to use settings here.
  maxRAMPercentage: 80

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

environments:
  activeProfie: ""

database:
  name: auction_management
  port: 5432

# Provide details regarding the requests to your service.  These details only take effect if service.enabled == true
virtualService:
  # bind VS to gateway that handles ingress from the public internet (ie. api.marketplace.ritchiebros.com)
  bindToPublic: true
  # bind VS to gateway that handles mTLS ingress from the public internet(ie. internal-api.marketplace.ritchiebros.com)
  bindToMtls: true
  #  after adding such configurations, you could have access to endpoint, such as `https://{{virtual.host}}/${prefixVersion}/${prefixUri}`.
  http: [ ]
#  http:
#    - prefixVersion: <VERSION_NUMBER> # like /v1, /v2 ...
#      prefixUri: <APPLICATION_NAME> # like /di-application
#      rewriteUri: / # forward the request to the destination
#      portNumber: 80

vaultInjector:
  # whether to use database dynamic credentials
  enabled: true
  # whether to set log level to trace
  debug: false
  # the auth method name that used by Kubernetes to access Vault, Its value is probably like `auth/kubernetes_[nonprod|prod]`
  authPath: ""
  # the role name that used by Kubernetes to access Vault, Its value is probably like [YOUR_TEAM_NAME]-kubernetes-role
  role: ""
  # an array to load secrets from vault to local files in the folder /vault/secrets/
  creds: []
    # # the path of the Auth Role to enable database dynamic secrets
    # - vaultPath: [ YOUR_TEAM_NAME ]/database/creds/[YOUR_DATABASE_VAULT_ROLE_NAME]
  #   # default output path of the dynamic credential
  #   # if you need to change it, it needs to be changed in application.yaml: `spring.config.import`
  #   destPath: db-creds.properties
# Seconds to wait before moving from a TERM signal to the pod's main process to a KILL signal.
terminationGracePeriodSeconds: 45

otelCollectorHost: otel-collector-collector.open-telemetry.svc.cluster.local

istioOPA:
  enabled: true
