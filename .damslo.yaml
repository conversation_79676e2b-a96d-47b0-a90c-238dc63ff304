---
schema_version: 1

service: auction-management

recipients:
  prod:
    opsgenie:
      - recipient: "Auction Management"
    slack:
      - "#bot-event-management-alert-prod"
  qa:
    slack:
      - "#bot-event-management-alert"
  dev:
    slack:
      - "#bot-event-management-alert"

column_checks:
  - key_name: app.salesforce.retry.status_code
    type: "string"
    description: "app.salesforce.retry.status_code column field"
  - key_name: app.salesforce.validation_error
    type: "string"
    description: "app.salesforce.validation_error column field"

# SLO Indicators
objective_indicators:
  - type: tomcat 7.0
    path: /actuator/health
    method: get
    latency:
      target: 0.25

# SLO Target
objective:
  target: 99.9
  burn_alerts:
    threshold_days: [1,0]

alerts:
  - type: custom_query
    name: "Auction Management Error"
    description: "Alert when Auction Management Service has errors"   # Optional
    query_json_file: "damslo-config/auction-management-query.json"
    frequency: 300  # Default is 60
    threshold:
      op: ">="
      value: 1

  - type: custom_query
    name: "Salesforce Retry Error"
    description: "Alert when failed to create auction in salesforce"   # Optional
    query_json_file: "damslo-config/auction-management-salesforce-api.json"
    frequency: 300  # Default is 60
    threshold:
      op: ">="
      value: 1

  - type: custom_query
    name: "Salesforce Validation Error"
    description: "Alert when failed to validate request in salesforce"   # Optional
    query_json_file: "damslo-config/salesforce-validation-error.json"
    frequency: 300  # Default is 60
    threshold:
      op: ">="
      value: 1