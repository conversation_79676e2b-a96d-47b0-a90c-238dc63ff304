# Bidirectional Contract Testing with Pactflow

This document describes the bidirectional contract testing setup for the `/events/{event_guid}` endpoint using Pactflow.

## Overview

Bidirectional contract testing ensures that consumer and provider services can communicate correctly by:

1. **Consumer** defines contracts (expectations) and publishes them to Pactflow
2. **Provider** verifies against these contracts and publishes verification results
3. **Pactflow** ensures compatibility between consumer and provider versions

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Consumer      │    │    Pactflow     │    │    Provider     │
│   (Frontend/    │    │    Broker       │    │   (Auction      │
│    Client)      │    │                 │    │   Management    │
│                 │    │                 │    │   Service)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. Publish Contracts  │                       │
         ├──────────────────────►│                       │
         │                       │ 2. Fetch Contracts    │
         │                       │◄──────────────────────┤
         │                       │ 3. Verify & Publish   │
         │                       │◄──────────────────────┤
         │ 4. Check Compatibility│                       │
         ├──────────────────────►│                       │
```

## Setup

### Prerequisites

1. **Pactflow Account**: Sign up at [pactflow.io](https://pactflow.io)
2. **Docker**: For running Pact CLI commands
3. **Java 17+**: For running the application
4. **Gradle**: For building and testing

### Environment Variables

Set the following environment variables:

```bash
export PACT_BROKER_BASE_URL="https://your-account.pactflow.io"
export PACT_BROKER_TOKEN="your-pactflow-token"
```

## Running Contract Tests

### 1. Consumer Contract Tests

Generate and publish consumer contracts:

```bash
# Run consumer tests and generate pact files
./gradlew consumerContractTest

# Publish consumer contracts to Pactflow
./gradlew publishConsumerContracts
```

### 2. Provider Verification Tests

Verify provider against consumer contracts:

```bash
# Run provider verification tests
./gradlew providerContractTest

# Publish verification results
./gradlew publishProviderVerification
```

### 3. Complete Pipeline

Run the complete contract testing pipeline:

```bash
# Make script executable
chmod +x scripts/contract-testing-pipeline.sh

# Run complete pipeline
./scripts/contract-testing-pipeline.sh all
```

## Contract Details

### Consumer Contract

The consumer contract defines expectations for the `/events/{event_guid}` endpoint:

**Successful Response (200):**
```json
{
  "auction_id": "123e4567-e89b-12d3-a456-************",
  "auction_number": "AUC-2024-001",
  "name": "Industrial Equipment Auction",
  "auction_advertised_name": "Edmonton Industrial Equipment Sale",
  "brand": "RBA",
  "primaryClassification": "Industrial",
  "schedulingStatus": "Published",
  "operation_status": "Active",
  "location": {
    "locationId": "loc-123",
    "locationName": "Edmonton Yard",
    "address": "123 Industrial Ave, Edmonton, AB"
  },
  "business_unit": {
    "businessUnitId": "bu-789",
    "businessUnitName": "Western Canada"
  }
}
```

**Error Response (404):**
```json
{
  "title": "Not Found",
  "status": 404,
  "detail": "Event not found with GUID: 999e4567-e89b-12d3-a456-************"
}
```

### Provider States

The provider supports the following states:

- `an event exists with GUID 123e4567-e89b-12d3-a456-************`
- `no event exists with GUID 999e4567-e89b-12d3-a456-************`
- `event service is available`
- `an industrial event exists`
- `a published event exists`

## CI/CD Integration

### GitHub Actions

The contract testing workflow runs automatically on:

- Push to `main` or `develop` branches
- Pull requests to `main`
- Manual workflow dispatch

### Pipeline Steps

1. **Consumer Tests**: Generate and publish contracts
2. **Provider Verification**: Verify against published contracts
3. **Can I Deploy**: Check compatibility for deployment
4. **Record Deployment**: Record successful deployments

## Gradle Tasks

| Task | Description |
|------|-------------|
| `consumerContractTest` | Run consumer contract tests |
| `publishConsumerContracts` | Publish consumer contracts to Pactflow |
| `providerContractTest` | Run provider verification tests |
| `publishProviderVerification` | Publish verification results |

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify `PACT_BROKER_TOKEN` is correct
   - Check Pactflow account permissions

2. **Contract Verification Failures**
   - Review provider state setup
   - Check mock data matches contract expectations

3. **Network Issues**
   - Verify `PACT_BROKER_BASE_URL` is accessible
   - Check firewall/proxy settings

### Debug Commands

```bash
# Check Pactflow connectivity
curl -H "Authorization: Bearer $PACT_BROKER_TOKEN" "$PACT_BROKER_BASE_URL"

# List published contracts
docker run --rm \
  -e PACT_BROKER_BASE_URL="$PACT_BROKER_BASE_URL" \
  -e PACT_BROKER_TOKEN="$PACT_BROKER_TOKEN" \
  pactfoundation/pact-cli:latest \
  broker list-latest-pact-versions

# Check deployment safety
docker run --rm \
  -e PACT_BROKER_BASE_URL="$PACT_BROKER_BASE_URL" \
  -e PACT_BROKER_TOKEN="$PACT_BROKER_TOKEN" \
  pactfoundation/pact-cli:latest \
  broker can-i-deploy \
  --pacticipant "auction-management-service" \
  --latest
```

## Best Practices

1. **Version Management**: Use semantic versioning for contracts
2. **State Management**: Keep provider states simple and focused
3. **Contract Evolution**: Use backward-compatible changes when possible
4. **Testing Strategy**: Run contract tests in CI/CD pipeline
5. **Documentation**: Keep contracts well-documented and up-to-date

## Resources

- [Pact Documentation](https://docs.pact.io/)
- [Pactflow Documentation](https://docs.pactflow.io/)
- [Bidirectional Contract Testing](https://docs.pactflow.io/docs/bi-directional-contract-testing/)
- [Pact JVM](https://github.com/DiUS/pact-jvm)
