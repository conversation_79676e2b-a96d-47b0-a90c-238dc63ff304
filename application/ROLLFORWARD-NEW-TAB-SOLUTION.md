# Rollforward New Tab and Checkbox Solution

## Problem Description

The rollforward functionality was experiencing two main issues:
1. **New Tab Opening**: Rollforward opens in a new tab, causing context switching problems
2. **Checkbox Detection**: Unable to check checkboxes in the rollforward interface

## Root Cause Analysis

### Issue 1: New Tab Handling
- Rollforward functionality opens in a new browser tab
- Test continues in original tab while rollforward interface is in new tab
- Page context needs to be switched to the new tab

### Issue 2: Checkbox Detection
- Original selector was very specific: `//div[@data-row-index='-1' and @data-column-id='RetoolInternal-715c6648-82c3-442b-977b-9c48da1fb7a8-RowSelectionColumnId']`
- Dynamic IDs and complex DOM structure make single selectors fragile
- Different checkbox implementations require multiple interaction methods

## Enhanced Solution

### 1. New Tab Detection and Switching

**Enhanced `createNewAuctionRollOver()` Method:**
```java
void createNewAuctionRollOver() {
    // Store current page count before clicking
    int initialPageCount = page.context().pages().size();
    
    page.locator("xpath=//*[contains(text(),'Roll Forward')]").click();
    page.waitForTimeout(3000);
    
    // Check if new tab opened
    int newPageCount = page.context().pages().size();
    
    if (newPageCount > initialPageCount) {
        System.out.println("✅ New tab detected - switching to rollforward tab");
        handleRollforwardNewTab();
    }
}
```

**New Tab Handling:**
```java
private void handleRollforwardNewTab() {
    // Get all pages (tabs)
    var pages = page.context().pages();
    
    // Find the rollforward tab (usually the newest one)
    for (int i = pages.size() - 1; i >= 0; i--) {
        Page currentPage = pages.get(i);
        if (isRollforwardPage(currentPage)) {
            this.page = currentPage; // Switch context
            waitForRollforwardPageToLoad();
            break;
        }
    }
}
```

**Rollforward Page Detection:**
```java
private boolean isRollforwardPage(Page pageToCheck) {
    List<String> rollforwardIndicators = Arrays.asList(
        "xpath=//h1[contains(text(),'Roll Forward')]",
        "xpath=//div[contains(text(),'Roll Forward')]",
        "xpath=//table//th[contains(text(),'Select')]",
        "xpath=//div[@data-row-index]",
        "xpath=//input[@type='checkbox']"
    );
    
    for (String selector : rollforwardIndicators) {
        if (pageToCheck.locator(selector).count() > 0) {
            return true;
        }
    }
    return false;
}
```

### 2. Enhanced Checkbox Detection

**Multiple Selector Patterns:**
```java
private List<String> getRollforwardCheckboxSelectors() {
    return Arrays.asList(
        // Original specific selector
        "xpath=//div[@data-row-index='-1' and @data-column-id='RetoolInternal-715c6648-82c3-442b-977b-9c48da1fb7a8-RowSelectionColumnId']",
        
        // Generic header row selectors
        "xpath=//div[@data-row-index='-1']//input[@type='checkbox']",
        "xpath=//div[@data-row-index='-1']//input",
        "xpath=//div[@data-row-index='-1']",
        
        // Table header selectors
        "xpath=//thead//input[@type='checkbox']",
        "xpath=//th//input[@type='checkbox']",
        "xpath=//table//tr[1]//input[@type='checkbox']",
        
        // Generic checkbox selectors
        "xpath=//table//input[@type='checkbox'][1]",
        "xpath=//div[contains(@class,'table')]//input[@type='checkbox'][1]",
        "xpath=//input[@type='checkbox'][1]"
    );
}
```

**Multiple Interaction Methods:**
```java
private boolean tryCheckboxInteraction(Locator checkbox) {
    // Method 1: Standard click
    try {
        checkbox.click();
        if (isCheckboxChecked(checkbox)) return true;
    } catch (Exception e) { }
    
    // Method 2: Force click
    try {
        checkbox.click(new Locator.ClickOptions().setForce(true));
        if (isCheckboxChecked(checkbox)) return true;
    } catch (Exception e) { }
    
    // Method 3: Check method (for input elements)
    try {
        checkbox.check();
        if (isCheckboxChecked(checkbox)) return true;
    } catch (Exception e) { }
    
    // Method 4: JavaScript click
    try {
        checkbox.evaluate("element => element.click()");
        if (isCheckboxChecked(checkbox)) return true;
    } catch (Exception e) { }
    
    return false;
}
```

### 3. Comprehensive Debug Capabilities

**Debug Method:**
```java
protected void debugRollforwardCheckboxes() {
    // Shows current page URL and title
    // Analyzes table structures
    // Lists all checkboxes with attributes
    // Provides detailed element information
}
```

## Key Methods

### 1. Enhanced Rollforward Creation
```java
createNewAuctionRollOver(); // Now handles new tabs automatically
```

### 2. Enhanced Checkbox Checking
```java
checkboxChecked(); // Now uses multiple selectors and interaction methods
```

### 3. Debug Rollforward Structure
```java
debugRollforwardCheckboxes(); // Analyze rollforward page structure
```

### 4. Complete Rollforward with Debug
```java
rollforwardWithDebug(); // Full rollforward process with debugging
```

## Usage Examples

### Basic Usage (Enhanced)
```java
@Test
void rollForwardTest() {
    loginAuctionManagement();
    createNewAuctionRollOver(); // Handles new tab automatically
    checkboxChecked();          // Uses enhanced checkbox detection
    // ... continue with test
}
```

### Debug Usage
```java
@Test
void debugRollforwardTest() {
    loginAuctionManagement();
    rollforwardWithDebug(); // Complete process with debugging
}
```

### Manual Debug
```java
@Test
void analyzeRollforwardTest() {
    loginAuctionManagement();
    createNewAuctionRollOver();
    debugRollforwardCheckboxes(); // Just analyze structure
}
```

## Expected Console Output

### Successful New Tab Handling:
```
=== Starting Rollforward Auction Creation ===
Initial page count: 1
New page count: 2
✅ New tab detected - switching to rollforward tab
Found rollforward indicator: xpath=//input[@type='checkbox']
✅ Found rollforward page - switching context
✅ Rollforward page loaded
✅ Successfully switched to rollforward tab
```

### Successful Checkbox Selection:
```
=== Starting Rollforward Checkbox Selection ===
Waiting for rollforward table to load...
✅ Found rollforward table with selector: xpath=//table
Trying rollforward checkbox selector: xpath=//div[@data-row-index='-1']//input[@type='checkbox']
✅ Found visible rollforward checkbox with selector: xpath=//div[@data-row-index='-1']//input[@type='checkbox']
Rollforward checkbox current state: unchecked
✅ Checkbox selected with standard click
✅ Rollforward checkbox selection completed
```

### Debug Analysis Output:
```
=== DEBUG: Rollforward Checkbox Analysis ===
Current page URL: https://your-app.com/rollforward
Current page title: Roll Forward Auction
--- Table Structures ---
✅ Found 1 elements with: xpath=//table
✅ Found 5 elements with: xpath=//div[@data-row-index]
--- All Checkboxes ---
✅ Found 3 total checkboxes on page
   Checkbox 0:
     ID: none
     Class: checkbox-input
     Visible: true
     Enabled: true
   Checkbox 1:
     ID: select-all
     Class: header-checkbox
     Visible: true
     Enabled: true
=== DEBUG: End of Rollforward Analysis ===
```

## Troubleshooting

### If New Tab Detection Fails:

1. **Check Console Output** for page count changes
2. **Verify Tab Indicators** - look for rollforward-specific elements
3. **Manual Tab Switching** - check if rollforward opened in same tab

### If Checkbox Selection Fails:

1. **Run Debug Test:**
   ```bash
   ./gradlew test --tests "*debugRollforwardCheckboxTest"
   ```

2. **Check Debug Output** for:
   - Available table structures
   - Checkbox elements and their attributes
   - Visibility and enabled states

3. **Review Screenshots** in `screenshots/` directory

4. **Add Custom Selectors** based on debug output

## Benefits

- **🎯 Automatic Tab Handling**: Detects and switches to new tabs automatically
- **🔍 Multiple Fallbacks**: 15+ selector patterns for maximum compatibility
- **⚡ Smart Interaction**: 4 different checkbox interaction methods
- **🐛 Enhanced Debugging**: Comprehensive analysis and logging
- **🛡️ Error Handling**: Graceful degradation with detailed error messages
- **📊 State Management**: Intelligent checkbox state detection

## Integration

The enhanced rollforward methods are automatically available in all test classes that extend `AuctionManagementActions`. Simply call:

```java
// In your test methods
createNewAuctionRollOver();     // Enhanced with new tab handling
checkboxChecked();              // Enhanced with multiple selectors
debugRollforwardCheckboxes();   // Debug structure
rollforwardWithDebug();         // Complete process with debugging
```

This solution should resolve both the new tab opening issues and checkbox detection problems in the rollforward functionality!
