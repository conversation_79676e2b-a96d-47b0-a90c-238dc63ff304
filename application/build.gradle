plugins {
    id 'org.springframework.boot' version '3.4.1'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'org.unbroken-dome.test-sets' version '4.1.0'
    id 'com.google.cloud.tools.jib' version '3.3.2'
    id 'com.ryandens.javaagent-jib' version '0.5.1'
    id 'jacoco'
    id "org.flywaydb.flyway" version "10.4.1"
    id 'idea'
    id "com.gorylenko.gradle-git-properties" version "2.4.1"
    id "org.springdoc.openapi-gradle-plugin" version "1.9.0"
}

description = "Spring application module contains the public facing API, application service layer and " +
        "database repository method implementations."

static def getVersion() {
    return 'git rev-parse --short HEAD'.execute().text.trim()
}

def version_tag = getVersion()

testSets {
    integrationTest {
        dirName = "integration-test"
    }

    componentTest {
        dirName = "component-test"
    }

    contractTest {
        dirName = "contract-test"
    }
    uiTest {
        dirName = "ui-test"
    }
}

def opentelemetry_version = "1.32.0"
ext['logback.version'] = '1.5.16'

dependencyManagement {
    imports {
        mavenBom("io.zonky.test.postgres:embedded-postgres-binaries-bom:14.2.0")
        mavenBom("io.opentelemetry.instrumentation:opentelemetry-instrumentation-bom-alpha:${opentelemetry_version}-alpha")
    }

    dependencies {
        dependency 'org.bitbucket.b_c:jose4j:0.9.3'
    }
}

dependencies {
    //spring boot version managed by plugin
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap:4.2.0'
    implementation 'org.springframework.cloud:spring-cloud-starter-kubernetes-client-config:3.2.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'
    implementation 'org.postgresql:postgresql:42.6.1'
    implementation 'org.flywaydb:flyway-core'
    implementation 'org.flywaydb:flyway-database-postgresql'
    implementation 'org.mapstruct:mapstruct:1.5.2.Final'
    implementation 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
    implementation 'org.zalando:problem-spring-web:0.29.1'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
    implementation 'io.opentelemetry:opentelemetry-sdk'
    implementation 'io.opentelemetry:opentelemetry-api'
    implementation 'io.opentelemetry:opentelemetry-sdk-extension-autoconfigure'
    implementation 'io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0'
    implementation 'io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:1.29.0'
    implementation 'com.google.protobuf:protobuf-java:3.19.6'
    implementation 'com.google.guava:guava:31.1-jre'
    implementation 'com.launchdarkly:launchdarkly-java-server-sdk:6.1.0'
    implementation 'com.squareup.okhttp3:okhttp'
    implementation 'net.javacrumbs.shedlock:shedlock-spring:5.10.2'
    implementation 'net.javacrumbs.shedlock:shedlock-provider-jdbc-template:5.10.2'

// Add direct dependencies for security fixes
    implementation 'org.apache.tomcat.embed:tomcat-embed-core:10.1.42'
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.4.3'
    implementation 'io.netty:netty-handler:4.1.118.Final'
    implementation 'org.springframework.security:spring-security-crypto:6.4.4'


    // Add this configuration block after the dependencies block
    configurations.all {
        resolutionStrategy {
            force(
                    'org.apache.tomcat.embed:tomcat-embed-core:10.1.42',
                    'org.apache.httpcomponents.client5:httpclient5:5.4.3',
                    'io.netty:netty-handler:4.1.118.Final',
                    'org.springframework.security:spring-security-crypto:6.4.4'
            )

            // Force specific versions for all related dependencies
            eachDependency { DependencyResolveDetails details ->
                if (details.requested.group == 'org.springframework.security') {
                    details.useVersion '6.4.4'
                }
                if (details.requested.group == 'org.apache.httpcomponents.client5'
                        && details.requested.name == 'httpclient5') {
                    details.useVersion '5.4.3'
                }
            }
        }
    }
    implementation('com.rb.essentials:rb-web-spring-boot-starter:0.0.23') {
        exclude group: 'io.opentelemetry.instrumentation'
        exclude group: 'io.opentelemetry'
    }
    implementation('com.rb.essentials:rb-capability-starter:0.1.17') {
        exclude group: 'org.springframework.boot'
        exclude group: 'com.rb.essentials', module: 'rb-capability-retry'
    }
    implementation('org.springframework.kafka:spring-kafka') {
        exclude group: 'org.apache.kafka', module: 'kafka-clients'
    }
    implementation 'org.apache.kafka:kafka-clients:3.9.1'
    implementation 'org.apache.avro:avro:1.11.4'
    implementation 'com.rbauction.enterprise:enterprise-events:2.394.0'
    implementation('io.confluent:kafka-avro-serializer:7.4.0') {
        exclude group: 'io.swagger.core.v3', module: 'swagger-annotations'
    }
    javaagent("io.opentelemetry.javaagent:opentelemetry-javaagent:${opentelemetry_version}")

    compileOnly 'org.projectlombok:lombok'

    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.2.Final'

    //test related
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.junit.jupiter:junit-jupiter-api'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine'
    testImplementation 'org.hamcrest:hamcrest'
    testImplementation 'org.mockito:mockito-inline:4.5.1'
    testImplementation 'io.rest-assured:rest-assured'
    testImplementation 'io.rest-assured:xml-path'
    testImplementation 'io.rest-assured:json-path'
    testImplementation 'io.zonky.test:embedded-postgres:2.1.0'
    testImplementation 'io.zonky.test:embedded-database-spring-test:2.6.0'
    // Pact Provider Testing
    testImplementation 'au.com.dius.pact.provider:junit5:4.3.6'

    // Pact Consumer Testing
    contractTestImplementation 'au.com.dius.pact.consumer:junit5:4.3.6'
    contractTestImplementation 'au.com.dius.pact.consumer:java8:4.3.6'
    testImplementation 'com.tngtech.archunit:archunit-junit5:0.23.1'
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.9.3'
    testImplementation 'com.microsoft.playwright:playwright:1.35.0'

    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-params'
    testRuntimeOnly "org.junit.vintage:junit-vintage-engine"

    integrationTestImplementation sourceSets.test.output
    componentTestImplementation sourceSets.test.output
    contractTestImplementation sourceSets.test.output
}

componentTest {
    testLogging.showStandardStreams = false
    useJUnitPlatform()

    testLogging {
        events "passed", "skipped", "failed"
    }
}

integrationTest {
    testLogging.showStandardStreams = false
    useJUnitPlatform()

    testLogging {
        events "passed", "skipped", "failed"
    }
}

contractTest {
    testLogging.showStandardStreams = false
    useJUnitPlatform()

    testLogging {
        events "passed", "skipped", "failed"
    }

    systemProperty 'pact.verifier.publishResults', 'true'
    systemProperty 'pact.provider.version', "${version_tag}"
    systemProperty 'pact.pact_url', System.getenv('PACT_BROKER_BASE_URL')
    systemProperty 'pact.pact_token', System.getenv('PACT_BROKER_TOKEN')
}

test {
    testLogging.showStandardStreams = false
    useJUnitPlatform()

    testLogging {
        events "passed", "skipped", "failed"
    }
}

uiTest{
    testLogging.showStandardStreams = false
    useJUnitPlatform()
    doFirst {
        if (project.hasProperty('dev')) {
            systemProperties['dev'] = project.dev
        }
        if (project.hasProperty('qa')) {
            systemProperties['qa'] = project.qa
        }
    }
    testLogging {
        events "passed", "skipped", "failed"
    }
}
tasks.register('allTest') {
    finalizedBy jacocoTestReport
}

jacoco {
    toolVersion = "0.8.11"
}

def jacoco_exclude_files = [
        'com/rb/capability/**/*Application.class',
        '**/*MapperImpl.class',
        'com/rb/capability/**/common/*',
        'com/rb/capability/auctionmanagement/infra/kafka/config/*',
        'com/rb/capability/auctionmanagement/client/config/*'
]

jacocoTestReport {
    dependsOn test, componentTest, integrationTest
    executionData fileTree(project.buildDir.absolutePath).include("jacoco/*.exec")
    reports {
        xml.required = true
        html.required = true
    }
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: jacoco_exclude_files)
        }))
    }
    finalizedBy jacocoTestCoverageVerification
}

jacocoTestCoverageVerification {
    executionData fileTree(project.buildDir.absolutePath).include("jacoco/*.exec")
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: jacoco_exclude_files)
        }))
    }
    violationRules {
        rule {
            limit {
                minimum = 0.9
            }
        }
    }
}

jib {
    from {
        image = 'ghcr.io/rbmarketplace/java21-debian12:0.0.1'
        auth {
            username = 'USERNAME'
            password = System.getenv('GITHUB_PACKAGE_TOKEN') ?: ''
        }
    }
    to {
        image = "ghcr.io/rbmarketplace/auction-management"
        tags = ["${version_tag}", 'latest']
        auth {
            username = 'USERNAME'
            password = System.getenv('GITHUB_PACKAGE_TOKEN') ?: ''
        }
    }
    container {
        // The JVM initial and max heap size can be configured here using the -Xms and -Xmx flags, respectively.
        // These settings are honored for non-Kubernetes deployments. These settings will be overridden when this
        // container is deployed to Kubernetes. For Kubernetes please configured the JVM heap size in the helm chart,
        // see the helm/values.yaml file.
        jvmFlags = []
        creationTime = 'USE_CURRENT_TIMESTAMP'
    }
    skaffold {
        watch {
            excludes = ["build/"]
        }
    }
}

gitProperties {
    keys = ['git.branch', 'git.commit.id', 'git.commit.id.abbrev']
    gitPropertiesResourceDir = file("${project.rootDir}/application/src/main/resources")
    processResources() {
        duplicatesStrategy = 'EXCLUDE'
    }
}

processResources.dependsOn(generateGitProperties)

repositories {
    maven {
        url = uri("https://maven.pkg.github.com/RBMarketplace/rb-essentials-spring-boot")
        credentials {
            username = 'USERNAME'
            password = System.getenv("GITHUB_PACKAGE_TOKEN")
        }
    }
    maven {
        url 'https://packages.confluent.io/maven'
    }
}

tasks.register('installLocalGitHook', Copy) {
    mustRunAfter("generateGitProperties")
    from new File(rootProject.rootDir, 'scripts/pre-push')
    into { new File(rootProject.rootDir, '.git/hooks') }
    fileMode 0775
}

build.dependsOn installLocalGitHook, jacocoTestReport
installLocalGitHook.dependsOn(generateGitProperties)

openApi {
    apiDocsUrl.set("http://localhost:8080/v1/openapi")
    waitTimeInSeconds.set(60)
    customBootRun {
        args.set(["--spring.profiles.active=local"])
    }
}