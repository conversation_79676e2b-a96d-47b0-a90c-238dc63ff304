# Pactflow Configuration for Bidirectional Contract Testing
# This file defines the configuration for publishing and verifying contracts

# Pactflow Broker Configuration
broker:
  base_url: ${PACT_BROKER_BASE_URL}
  token: ${PACT_BROKER_TOKEN}

# Consumer Configuration
consumer:
  name: "auction-management-consumer"
  version: ${CONSUMER_VERSION}
  tags:
    - main
    - dev
    - ${BRANCH_NAME}

# Provider Configuration  
provider:
  name: "auction-management-service"
  version: ${PROVIDER_VERSION}
  tags:
    - main
    - dev
    - ${BRANCH_NAME}

# Contract Publishing Configuration
publish:
  consumer_contracts: true
  provider_verification_results: true
  
# Verification Configuration
verification:
  publish_results: true
  provider_version_tags:
    - main
    - dev
  consumer_version_selectors:
    - tag: main
      latest: true
    - tag: dev
      latest: true
    - deployed_or_released: true

# Webhook Configuration for CI/CD Integration
webhooks:
  contract_changed:
    enabled: true
    events:
      - contract_published
      - provider_verification_published
  
# Can I Deploy Configuration
can_i_deploy:
  retry:
    max_attempts: 3
    interval: 10
  to_environment: production
  
# Environment Configuration
environments:
  - name: development
    production: false
  - name: staging  
    production: false
  - name: production
    production: true
