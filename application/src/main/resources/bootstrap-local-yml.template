spring:
  datasource:
    url: ***************************************************
    username: postgres
    password: postgres
  jpa:
    show-sql: true

  kafka:
    bootstrap-servers: pkc-qjdk7.us-west-2.aws.confluent.cloud:9092
    properties:
      auto:
        register:
          schemas: false
        offset:
          reset: latest
      basic:
        auth:
          user:
            info: "xx:xx"
      sasl:
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username='xx' password='xx';
      schema:
        registry:
          url: https://psrc-nx65v.us-east-2.aws.confluent.cloud
    consumer:
      group-id: marketplace_auction_management-projdev-dipali
      client-id: marketplace_auction_management-projdev-local-client
    producer:
      client-id: marketplace_auction_management-projdev-producer-local-client

kafka:
  topics:
    enterprise_mars_events: projdev-enterprise.sale-events
    enterprise_auction: projdev-integration.capability-auction


accounts-service:
  url: http://localhost:8080
places_api:
  base_url: http://localhost:8082/v1
legal_entity_api:
    base_url: https://api.dev.marketplace.ritchiebros.com/legal-entity
server:
  port: 8083
salesforce:
  base_url: https://rb--full56.sandbox.my.salesforce.com/services
  username: xx
  password: xx
  client_id: xx
  client_secret: 3xx
