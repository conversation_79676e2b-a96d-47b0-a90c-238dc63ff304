springdoc:
  api-docs:
    path: /openapi
  use-fqn: true

spring:
  config:
    import: optional:file:/vault/secrets/vault.yaml
  datasource:
    username: ${db_username}
    password: ${db_password}
    hikari:
      connection-timeout: 60000
  cloud:
    discovery:
      client:
        composite-indicator:
          enabled: false
  kafka:
    properties:
      value:
        subject:
          name:
            strategy: io.confluent.kafka.serializers.subject.RecordNameStrategy
      basic:
        auth:
          credentials:
            source: USER_INFO
      auto:
        offset:
          reset: latest
        register:
          schemas: false
      sasl:
        mechanism: PLAIN
      request.timeout.ms: 20000
      retry.backoff.ms: 500
      ssl:
        endpoint:
          identification:
            algorithm: https
      request:
        timeout:
          ms: 20000
    producer:
      key-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
    consumer:
      key-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      properties:
        specific:
          avro:
            reader: true
        spring:
          deserializer:
            value:
              delegate:
                class: io.confluent.kafka.serializers.KafkaAvroDeserializer
    security:
      protocol: SASL_SSL

  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  jackson:
    default-property-inclusion: non_null
    deserialization:
      accept-float-as-int: false
      fail-on-unknown-properties: false
    generator:
      write-bigdecimal-as-plain: true
    property-naming-strategy: SNAKE_CASE
  flyway:
    # enable flyway after init new repository
    enabled: true

management:
  health:
    defaults:
      enabled: false
    db:
      enabled: true
    diskspace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: prometheus, health, info
  endpoint:
    health:
      show-details: always
      group:
        liveness:
          include:
            - livenessState
  metrics:
    web:
      server:
        request:
          autotime:
            enabled: true
  server:
    port: 9090

server:
  forward-headers-strategy: framework
  servlet:
    context-path: /v1

ld_sdk_key:
kafkaSourceSystem: