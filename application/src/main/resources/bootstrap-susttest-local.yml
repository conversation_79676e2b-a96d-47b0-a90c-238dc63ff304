spring:
  datasource:
    url: ***************************************************
    username: postgres
    password: postgres
  jpa:
    show-sql: true

  kafka:
    bootstrap-servers: pkc-qjdk7.us-west-2.aws.confluent.cloud:9092
    properties:
      auto:
        register:
          schemas: false
        offset:
          reset: latest
      basic:
        auth:
          user:
            info: "42UCIXEJ5AA52I2J:n11I3tI++s56gnH1KU/kkJo6qPAqFPL2a1raSZ8rhFd1ZNaSe/RL6fpLThZ09Cow"
      sasl:
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username='K77VP7KKJTWORN63' password='Obb2jlFDocJ2voBJSM/+kZ+QuaUzJ0bUKfUm8mtl+WyYd43t/CgnMu8ASTYbeyV9';
      schema:
        registry:
          url: https://psrc-nx65v.us-east-2.aws.confluent.cloud
    consumer:
      group-id: marketplace_auction_management-susttest-dipali
      client-id: marketplace_auction_management-susttest-local-client
    producer:
      client-id: marketplace_auction_management-susttest-producer-local-client

kafka:
  topics:
    enterprise_mars_events: susttest-enterprise.sale-events
    enterprise_auction: susttest-integration.capability-auction


accounts-service:
  url: http://localhost:8080
places_api:
  base_url: http://localhost:8082/v1
server:
  port: 8083