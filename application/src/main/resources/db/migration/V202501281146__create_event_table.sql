CREATE TABLE IF NOT EXISTS event
(
    guid                        VARCHAR(50) UNIQUE PRIMARY KEY,
    sale_number                 VARCHAR(20) NOT NULL,
    event_advertised_name       VARCHAR(255),
    brand                       VARCHAR(50),
    timezone                    VARCHAR(255),
    event_start_date            TIMESTAMP WITH TIME ZONE,
    event_end_date              TIMESTAMP WITH TIME ZONE,
    currency                    VARCHAR(3) NOT NULL DEFAULT 'USD',
    primary_classification      VARCHAR(30),
    scheduling_status           VARCHAR(30),
    selling_formats             VARCHAR(30),
    ways_to_participate         VARCHAR(30),
    is_registration_open        BOOLEAN,
    registration_auto_open_date TIMESTAMP WITH TIME ZONE,
    is_tal_bid_open             BOOLEAN,
    tal_bid_auto_open_date      TIMESTAMP WITH TIME ZONE,
    is_priority_bid_open        BOOLEAN,
    priority_bid_auto_open_date TIMESTAMP WITH TIME ZONE,
    is_olr_enabled              BOOLEAN,
    created_date                TIMES<PERSON><PERSON> WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modifier_name          VARCHAR(100),
    last_modified_date          TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);