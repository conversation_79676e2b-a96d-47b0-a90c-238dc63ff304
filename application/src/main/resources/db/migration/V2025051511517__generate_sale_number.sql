
-- Next Year (2027) Non Agriculture Non Charity (101-501)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2027000 + number AS sale_number,
    false AS in_use,
    2027 AS year,
    'Non Agriculture Non Charity' AS type
FROM generate_series(101, 501) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2027) Agriculture (502-900)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2027000 + number AS sale_number,
    false AS in_use,
    2027 AS year,
    'Agriculture' AS type
FROM generate_series(502, 900) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2027) Charity (901-999)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2027000 + number AS sale_number,
    false AS in_use,
    2027 AS year,
    'Charity' AS type
FROM generate_series(901, 999) AS number
ON CONFLICT (sale_number) DO NOTHING;



-- Next Year (2028) Non Agriculture Non Charity (101-501)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2028000 + number AS sale_number,
    false AS in_use,
    2028 AS year,
  'Non Agriculture Non Charity' AS type
FROM generate_series(101, 501) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2028) Agriculture (502-900)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2028000 + number AS sale_number,
    false AS in_use,
    2028 AS year,
  'Agriculture' AS type
FROM generate_series(502, 900) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2028) Charity (901-999)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2028000 + number AS sale_number,
    false AS in_use,
    2028 AS year,
  'Charity' AS type
FROM generate_series(901, 999) AS number
ON CONFLICT (sale_number) DO NOTHING;




-- Next Year (2029) Non Agriculture Non Charity (101-501)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2029000 + number AS sale_number,
    false AS in_use,
    2029 AS year,
  'Non Agriculture Non Charity' AS type
FROM generate_series(101, 501) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2029) Agriculture (502-900)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2029000 + number AS sale_number,
    false AS in_use,
    2029 AS year,
  'Agriculture' AS type
FROM generate_series(502, 900) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2029) Charity (901-999)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2029000 + number AS sale_number,
    false AS in_use,
    2029 AS year,
  'Charity' AS type
FROM generate_series(901, 999) AS number
ON CONFLICT (sale_number) DO NOTHING;



-- Next Year (2030) Non Agriculture Non Charity (101-501)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2030000 + number AS sale_number,
    false AS in_use,
    2030 AS year,
  'Non Agriculture Non Charity' AS type
FROM generate_series(101, 501) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2030) Agriculture (502-900)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2030000 + number AS sale_number,
    false AS in_use,
    2030 AS year,
  'Agriculture' AS type
FROM generate_series(502, 900) AS number
ON CONFLICT (sale_number) DO NOTHING;

-- Next Year (2030) Charity (901-999)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2030000 + number AS sale_number,
    false AS in_use,
    2030 AS year,
  'Charity' AS type
FROM generate_series(901, 999) AS number
ON CONFLICT (sale_number) DO NOTHING;