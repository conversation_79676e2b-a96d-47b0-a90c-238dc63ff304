-- Remove the primary key constraint from guid
ALTER TABLE event DROP CONSTRAINT event_pkey;

-- Add id column as primary key
ALTER TABLE event ADD COLUMN id SERIAL;
ALTER TABLE event ADD PRIMARY KEY (id);

-- Add location_id(guid) and site_id(guid) columns
ALTER TABLE event ADD COLUMN location_id VARCHAR(50);
ALTER TABLE event ADD COLUMN site_id VARCHAR(50);

-- indexes for event table
CREATE INDEX idx_event_guid ON event(guid);
CREATE INDEX idx_event_location_id ON event(location_id);
CREATE INDEX idx_event_site_id ON event(site_id);

-- Add unique constraint back to guid
ALTER TABLE event ADD CONSTRAINT event_guid_unique UNIQUE (guid);