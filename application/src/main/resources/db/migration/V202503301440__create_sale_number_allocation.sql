CREATE TABLE sale_number_allocation (
     id SERIAL PRIMARY KEY,
     sale_number INTEGER NOT NULL UNIQUE,
     in_use BOOLEAN NOT NULL DEFAULT false,
     year INTEGER NOT NULL,
     type VARCHAR(50),
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_sale_number_allocation_year ON sale_number_allocation(year);
CREATE INDEX idx_sale_number_allocation_type ON sale_number_allocation(type);

-- Current Year (2025) Non Agriculture Non Charity (101-501)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2025000 + number AS sale_number,
    false AS in_use,
    2025 AS year,
  'Non Agriculture Non Charity' AS type
FROM generate_series(101, 501) AS number;

-- Current Year (2025) Agriculture (502-900)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2025000 + number AS sale_number,
    false AS in_use,
    2025 AS year,
  'Agriculture' AS type
FROM generate_series(502, 900) AS number;

-- Current Year (2025) Charity (901-999)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2025000 + number AS sale_number,
    false AS in_use,
    2025 AS year,
  'Charity' AS type
FROM generate_series(901, 999) AS number;

-- Next Year (2026) Non Agriculture Non Charity (101-501)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2026000 + number AS sale_number,
    false AS in_use,
    2026 AS year,
  'Non Agriculture Non Charity' AS type
FROM generate_series(101, 501) AS number;

-- Next Year (2026) Agriculture (502-900)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2026000 + number AS sale_number,
    false AS in_use,
    2026 AS year,
  'Agriculture' AS type
FROM generate_series(502, 900) AS number;

-- Next Year (2026) Charity (901-999)
INSERT INTO sale_number_allocation (sale_number, in_use, year, type)
SELECT
    2026000 + number AS sale_number,
    false AS in_use,
    2026 AS year,
  'Charity' AS type
FROM generate_series(901, 999) AS number;