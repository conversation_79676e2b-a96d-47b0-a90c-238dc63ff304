package com.rb.capability.auctionmanagement.model;

import com.rb.capability.auctionmanagement.client.LocationType;
import com.rb.capability.auctionmanagement.client.Status;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.Builder;

@Builder(toBuilder = true)
public record Location(

        @Size(max = 200, message = "name must be at most 200 characters")
        String name,
        @Size(max = 100, message = "timezone must be at most 100 characters")
        String timeZone,
        @Size(max = 100, message = "type must be at most 100 characters")
        LocationType type,
        @Size(max = 100, message = "status must be at most 100 characters")
        Status status,
        @Valid
        AddressDto address,
        @Valid
        ContactDto locationContact,
        String pickupInstruction,
        @Size(max = 10, message = "openTime must be at most 10 characters")
        String openTime,
        @Size(max = 10, message = "closeTime must be at most 10 characters")
        String closeTime,
        @Size(max = 10, message = "lunchStartTime must be at most 10 characters")
        String lunchStartTime,
        @Size(max = 10, message = "lunchEndTime must be at most 10 characters")
        String lunchEndTime,
        Boolean hasLoadingDock,
        Boolean hasForklift,
        Boolean hasRamp,
        String ownerId) {
}
