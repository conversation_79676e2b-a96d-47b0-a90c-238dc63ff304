package com.rb.capability.auctionmanagement.client;

import com.rb.capability.auctionmanagement.client.exception.ControllerErrorResponseHandler;
import com.rb.capability.auctionmanagement.client.model.LegalEntitiesResponse;
import com.rb.capability.auctionmanagement.client.model.LegalEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;

import static java.util.Collections.emptyList;
import static net.logstash.logback.argument.StructuredArguments.kv;

@Slf4j
@Component
public class LegalEntityServiceClient {
    private final WebClient legalEntityWebClient;
    private final String baseUrl;
    private List<LegalEntity> cachedLegalEntities = emptyList();
    private static final Duration CACHE_DURATION = Duration.ofMinutes(240);

    public LegalEntityServiceClient(@Qualifier("legalEntityWebClient") WebClient legalEntityWebClient,
                                    @Value("${legal_entity_api.base_url}") String baseUrl) {
        this.legalEntityWebClient = legalEntityWebClient;
        this.baseUrl = baseUrl;
    }

    public List<LegalEntity> getLegalEntities() {
        log.info("Fetching legal entities from endpoint: {}", kv("url", baseUrl));
        return getCachedLegalEntities();
    }

    private List<LegalEntity> getCachedLegalEntities() {
        if (cachedLegalEntities.isEmpty()) {
            String endpoint = baseUrl + "/v1/legal-entities";
            cachedLegalEntities = legalEntityWebClient.get()
                    .uri(endpoint)
                    .accept(MediaType.APPLICATION_JSON)
                    .acceptCharset(StandardCharsets.UTF_8)
                    .retrieve()
                    .bodyToMono(LegalEntitiesResponse.class)
                    .map(LegalEntitiesResponse::legalEntities)
                    .cache(
                            success -> CACHE_DURATION,
                            error -> Duration.ZERO,
                            () -> Duration.ZERO
                    )
                    .cacheInvalidateIf(cache -> cache == null || cache.isEmpty())
                    .onErrorResume(Exception.class, e -> {
                        log.error("Failed to fetch legal entities: {}", kv("error", e));
                        if (e instanceof WebClientResponseException) {
                            throw new ResponseStatusException(
                                    ((WebClientResponseException) e).getStatusCode(),
                                    ControllerErrorResponseHandler.extractErrorDetails((WebClientResponseException) e)
                            );
                        }
                        throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR);
                    })
                    .blockOptional()
                    .orElse(emptyList());
        }
        return cachedLegalEntities;
    }
}