package com.rb.capability.auctionmanagement.domain.enums;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Represents the different ways users can participate in an auction event.
 */
public enum ParticipationMethod {
    ONSITE("ONSITE"),
    ONLINE("ONLINE");

    public final String enterpriseParticipationMethod;
    
    ParticipationMethod(String enterpriseParticipationMethod) {
        this.enterpriseParticipationMethod = enterpriseParticipationMethod;
    }

    /**
     * Converts enterprise participation method string to our internal format.
     * Handles special cases like ONLINE_LIVE_ONSITE which maps to both ONLINE and ONSITE.
     */
    public static String getWaysToParticipateFromEnterpriseMessage(String enterpriseWaysToParticipate) {
        if (enterpriseWaysToParticipate == null || enterpriseWaysToParticipate.isEmpty()) {
            return null;
        }

        return switch (enterpriseWaysToParticipate) {
            case "ONLINE_LIVE_ONSITE" -> "ONLINE,ONSITE";
            case "LIVE_ONSITE" -> "ONSITE";
            case "ONLINE" -> "ONLINE";
            default -> null;
        };
    }


    /**
     * Converts a comma-separated string from the database to a list of participation methods.
     */
    public static List<ParticipationMethod> fromDbString(String methodsString) {
        if (methodsString == null || methodsString.isEmpty()) {
            return Collections.emptyList();
        }

        return Arrays.stream(methodsString.split(","))
                .map(ParticipationMethod::valueOf)
                .collect(Collectors.toList());
    }
}
