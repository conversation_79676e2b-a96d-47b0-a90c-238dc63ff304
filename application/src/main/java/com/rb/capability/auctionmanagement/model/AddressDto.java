package com.rb.capability.auctionmanagement.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Size;
import lombok.Builder;

@Builder(toBuilder = true)
public record AddressDto(

        @JsonProperty("address")
        @Size(max = 200, message = "address must be at most 200 characters")
        String address,

        @JsonProperty("city")
        @Size(max = 200, message = "city must be at most 200 characters")
        String city,

        @JsonProperty("province_state_code")
        @Size(max = 10, message = "provinceStateCode must be at most 10 characters")
        String provinceStateCode,

        @JsonProperty("province_state")
        @Size(max = 200, message = "provinceState must be at most 200 characters")
        String provinceState,

        @JsonProperty("country_code")
        @Size(max = 3, message = "countryCode must be at most 3 characters")
        String countryCode,

        @JsonProperty("country")
        @Size(max = 200, message = "country must be at most 200 characters")
        String country,

        @JsonProperty("postal_code")
        @Size(max = 20, message = "postalCode must be at most 20 characters")
        String postalCode,

        @DecimalMin(value = "-90.0", message = "Latitude must be greater than or equal to -90.0")
        @DecimalMax(value = "90.0", message = "Latitude must be less than or equal to 90.0")
        @Digits(integer = 2, fraction = 8, message = "Latitude must have at most 2 digits before the decimal point and 8 digits after")
        Double latitude,

        @DecimalMin(value = "-180.0", message = "Longitude must be greater than or equal to -180.0")
        @DecimalMax(value = "180.0", message = "Longitude must be less than or equal to 180.0")
        @Digits(integer = 3, fraction = 8, message = "Longitude must have at most 3 digits before the decimal point and 8 digits after")
        Double longitude) {

}
