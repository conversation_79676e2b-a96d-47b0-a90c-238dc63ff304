package com.rb.capability.auctionmanagement.audit;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import jakarta.persistence.Transient;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Utility class for audit-related operations.
 */
@Slf4j
@UtilityClass
public class AuditHelper {

    private static final Set<String> TIMESTAMP_FIELDS = Set.of(
            "eventStartDate", "eventEndDate", "registrationAutoOpenDate",
            "talBidAutoOpenDate", "priorityBidAutoOpenDate", "catalogExportDate"
    );

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Checks if a field is a timestamp field that needs timezone conversion.
     *
     * @param fieldName The name of the field to check
     * @return true if the field is a timestamp field, false otherwise
     */
    public static boolean isTimestampField(String fieldName) {
        return TIMESTAMP_FIELDS.contains(fieldName);
    }

    /**
     * Extracts all auditable field values from an entity.
     *
     * @param entity The entity to extract values from
     * @return Map of field names to their values
     */
    public static Map<String, Object> extractFieldValues(EventEntity entity) {
        Map<String, Object> values = new HashMap<>();
        String timezone = entity.getTimezone();

        for (Field field : EventEntity.class.getDeclaredFields()) {
            if (field.isAnnotationPresent(Transient.class) || field.isAnnotationPresent(NoAudit.class)) {
                continue;
            }

            field.setAccessible(true);
            try {
                Object value = field.get(entity);

                // Store original timestamp for comparison
                if (value instanceof Timestamp && TIMESTAMP_FIELDS.contains(field.getName())) {
                    values.put(field.getName() + "_original", value);

                    // Convert timestamp fields to event's local timezone if timezone is available
                    if (timezone != null) {
                        value = convertTimestampToLocalTime((Timestamp) value, timezone);
                    }
                }

                values.put(field.getName(), value);
            } catch (IllegalAccessException e) {
                log.error("Unable to read field {}", field.getName(), e);
                throw new RuntimeException("Unable to read field " + field.getName(), e);
            }
        }
        return values;
    }

    /**
     * Converts a UTC timestamp to the event's local timezone string representation.
     *
     * @param timestamp The UTC timestamp to convert
     * @param timezone The event's timezone
     * @return Formatted timestamp string in local timezone
     */
    public static String convertTimestampToLocalTime(Timestamp timestamp, String timezone) {
        if (timestamp == null) {
            return null;
        }

        ZonedDateTime utcTime = timestamp.toInstant().atZone(ZoneId.of("UTC"));
        ZonedDateTime localTime = utcTime.withZoneSameInstant(ZoneId.of(timezone));
        return localTime.format(FORMATTER);
    }
}
