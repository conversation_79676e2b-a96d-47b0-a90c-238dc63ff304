package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.auctionmanagement.resource.request.EventStatusRequest;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.resource.response.EventResponse;
import com.rb.capability.auctionmanagement.resource.response.EventSearchResponse;
import com.rb.capability.auctionmanagement.service.EventService;
import com.rb.capability.common.BasicUtils;
import com.rb.essentials.capability.exception.BusinessException;
import com.rb.essentials.log.ApplicationTier;
import com.rb.essentials.log.LoggableEvent;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.internal.util.JsonUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.zalando.problem.Problem;

import java.util.List;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;

@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Create New Event API - Success"),
    @ApiResponse(responseCode = "400", description = "Bad Request",
        content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
            schema = @Schema(implementation = MethodArgumentNotValidException.class),
            examples = {@ExampleObject(value = """
                  {
                    "title": "Bad Request",
                    "status": 400
                  }
                    """)})),
    @ApiResponse(responseCode = "401", description = "Not Authorized",
        content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
            schema = @Schema(implementation = Problem.class),
            examples = {@ExampleObject(value = """
                  {
                    "title": "Not Authorized",
                    "status": 401,
                    "detail": "The request is not authorized."
                  }
                    """)})),
    @ApiResponse(responseCode = "404", description = "Resource Not Found",
        content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
            schema = @Schema(implementation = Problem.class),
            examples = {@ExampleObject(value = """
                {
                  "title": "Not Found",
                  "status": 404,
                  "detail": "The requested resource could not be found."
                }
                    """)})),
    @ApiResponse(responseCode = "500", description = "Internal Server Error",
        content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
            schema = @Schema(implementation = Problem.class),
            examples = {@ExampleObject(value = """
                {
                  "title": "Internal Server Error",
                  "status": 500
                }
                    """)}))
})

public class EventController {
    private final EventService eventService;

    @PostMapping(path = "events", produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("create-event")
    @Operation(tags = {"Events"}, summary = "Create new event",
        description = "Creates a new auction event")
    public ResponseEntity<EventResponse> createEvent(@Valid @RequestBody EventRequest eventRequest) {
        log.info("AuctionManagement: createEvent endpoint called");

        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_REQUEST, BasicUtils.convertObjectToJsonString(eventRequest));
        try {
            EventResponse eventResponse = eventService.createEvent(eventRequest);
            currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(eventResponse));
            return ResponseEntity.ok(eventResponse);
        } catch (Exception e) {
            log.error("Error creating event", e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @GetMapping(value = "/events/{event_guid}", produces = MediaType.APPLICATION_JSON_VALUE)
    @LoggableEvent(applicationTire = ApplicationTier.RESOURCE, action = "Get auction event details")
    @Operation(tags = {"Events"}, summary = "Get event details", description = "Retrieves auction event details by GUID")
    @WithSpan("get-event-details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OK",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = EventDetailsResponse.class)
            )),
        @ApiResponse(responseCode = "404", description = "Event not found",
            content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                schema = @Schema(implementation = String.class),
                examples = {@ExampleObject(value = """
                    {
                      "title": "Not Found",
                      "status": 404,
                      "detail": "Event not found with GUID: {eventGuid}"
                    }
                        """)})),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
            content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                schema = @Schema(implementation = String.class),
                examples = {@ExampleObject(value = """
                    {
                      "title": "Internal Server Error",
                      "status": 500,
                      "detail": "Error while fetching event details"
                    }
                        """)}))
    })
    public ResponseEntity<EventDetailsResponse> getEventDetails(@PathVariable("event_guid") String guid) {
        Span currentSpan = Span.current();
        log.info("AuctionManagement: getEventDetails endpoint called for GUID: " + guid);
        EventDetailsResponse response = eventService.getEventDetails(guid);
        currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/events", produces = MediaType.APPLICATION_JSON_VALUE)
    @LoggableEvent(applicationTire = ApplicationTier.RESOURCE, action = "Get list of events with filters")
    @Operation(tags = {"Events"}, summary = "Get list of events with filters",
            description = """
                    Retrieves a list of events based on provided filters.
                    filters start date and end date are mandatory and scheduling status is optional - if events not found with filters, returns empty list.
                    """
    )
    @WithSpan("get-event-list-with-filters")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved filtered events",
                    content = @Content(
                            mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = EventDetailsResponse.class))
                    )),
        @ApiResponse(responseCode = "400", description = "Bad Request",
                    content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                            schema = @Schema(implementation = Problem.class),
                            examples = {@ExampleObject(value = """
                                    {
                                      "title": "Bad Request",
                                      "status": 400
                                    }
                                    """)})),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                    content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                            schema = @Schema(implementation = String.class),
                            examples = {@ExampleObject(value = """
                                    {
                                      "title": "Internal Server Error",
                                      "status": 500,
                                      "detail": "Error while fetching event list"
                                    }
                                        """)}))
    })
    public ResponseEntity<List<EventDetailsResponse>> getEvents(
            @Parameter(description = "From date", example = "2024-05-01T00:00:01Z")
            @RequestParam("from") String fromDate,

            @Parameter(description = "To date", example = "2024-05-03T00:00:01-07:00")
            @RequestParam("to") String toDate,

            @Parameter(description = "Scheduling Status", example = "Published,Confirmed")
            @RequestParam(name = "scheduling_status", required = false) String... schedulingStatus) {
        Span currentSpan = Span.current();
        try {
            log.info("AuctionManagement: getEventList endpoint called for From date: {} " + fromDate + " and To Date {}" + toDate + "and scheduling staus {}" + schedulingStatus);
            List<EventDetailsResponse> response = eventService.findEventsWithFilters(fromDate, toDate, schedulingStatus);
            currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Unexpected error while fetching event list  for From date: {}" + fromDate + " and To Date: {}" + toDate + "and scheduling staus {}" + schedulingStatus, e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @PutMapping(path = "events/{auction_id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("update-event")
    @Operation(tags = {"Events"}, summary = "Update event",
            description = "updates auction event")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Update Event API - Success"),
        @ApiResponse(responseCode = "400", description = "Bad Request",
                    content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                            schema = @Schema(implementation = MethodArgumentNotValidException.class),
                            examples = {@ExampleObject(value = """
                                    {
                                      "title": "Bad Request",
                                      "status": 400
                                    }
                                      """)})),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                    content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                            schema = @Schema(implementation = Problem.class),
                            examples = {@ExampleObject(value = """
                                    {
                                      "title": "Internal Server Error",
                                      "status": 500
                                    }
                                        """)}))
    })
    public ResponseEntity<EventResponse> updateEvent(@PathVariable("auction_id") String auctionId, @Valid @RequestBody EventRequest eventRequest) {
        log.info("AuctionManagement: updateEvent endpoint called");
        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_REQUEST, BasicUtils.convertObjectToJsonString(eventRequest));
        EventResponse eventResponse = eventService.updateEvent(auctionId, eventRequest);
        currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(eventResponse));
        return ResponseEntity.ok(eventResponse);

    }

    @PatchMapping(path = "events/{auction_id}/status",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("update-event-status")
    @Operation(tags = {"Events"}, summary = "Update event status",
            description = "Updates auction event status when notified by SAS")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Update Event Status API - Success"),
        @ApiResponse(responseCode = "400", description = "Bad Request",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class))),
        @ApiResponse(responseCode = "404", description = "Event not found",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class))),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class)))
    })
    public ResponseEntity<EventResponse> updateEventStatus(
            @PathVariable("auction_id") String auctionId,
            @RequestBody EventStatusRequest request) {

        log.info("UpdateEventStatus endpoint called from SAS for auctionId {} with request {}", auctionId, JsonUtils.toJson(request));

        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_REQUEST, String.format("auctionId: %s, status: %s", auctionId, request.status()));

        EventResponse eventResponse = eventService.updateEventStatus(auctionId, request);
        currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(eventResponse));
        return ResponseEntity.ok(eventResponse);

    }

    @GetMapping(value = "/events/auction/{auction_number}", produces = MediaType.APPLICATION_JSON_VALUE)
    @LoggableEvent(applicationTire = ApplicationTier.RESOURCE, action = "Get auction event details by auction number")
    @Operation(tags = {"Events"}, summary = "Get event details by auction number", description = "Retrieves auction event details by auction number")
    @WithSpan("get-event-details-by-auction-number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OK",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = EventDetailsResponse.class)
            )),
        @ApiResponse(responseCode = "404", description = "Event not found",
            content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                schema = @Schema(implementation = String.class),
                examples = {@ExampleObject(value = """
                    {
                      "title": "Not Found",
                      "status": 404,
                      "detail": "Event not found with auction number: {auctionNumber}"
                    }
                        """)})),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
            content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                schema = @Schema(implementation = String.class),
                examples = {@ExampleObject(value = """
                    {
                      "title": "Internal Server Error",
                      "status": 500,
                      "detail": "Error while fetching event details"
                    }
                        """)}))
    })
    public ResponseEntity<EventDetailsResponse> getEventDetailsByAuctionNumber(@PathVariable("auction_number") String auctionNumber) {
        Span currentSpan = Span.current();
        log.info("AuctionManagement: getEventDetailsByAuctionNumber endpoint called for auction number: " + auctionNumber);
        EventDetailsResponse response = eventService.getEventDetailsByAuctionNumber(auctionNumber);
        currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "/events/search", produces = MediaType.APPLICATION_JSON_VALUE)
    @LoggableEvent(applicationTire = ApplicationTier.RESOURCE, action = "Returns a list of auctions with partial auction number,name and advertised name")
    @Operation(tags = {"Events"}, summary = "Search auctions by partial auction number,name and advertised name",
            description = """
                    Returns a list of auctions where the auction number contains the given partial number,name and advertised name.
                    """
    )
    @WithSpan("get-event-list-with-auction-number-name-and-advertised-name")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved search events",
                content = @Content(
                        mediaType = "application/json",
                        array = @ArraySchema(schema = @Schema(implementation = EventDetailsResponse.class))
                )),
        @ApiResponse(responseCode = "400", description = "Bad Request",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class),
                        examples = {@ExampleObject(value = """
                                {
                                  "title": "Bad Request",
                                  "status": 400
                                }
                                """)})),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = String.class),
                        examples = {@ExampleObject(value = """
                                {
                                  "title": "Internal Server Error",
                                  "status": 500,
                                  "detail": "Error while fetching search event list"
                                }
                                    """)}))
    })
    public ResponseEntity<List<EventSearchResponse>> findEvents(
            @Parameter(description = "Search Param", example = "2025 Edmonton")
            @RequestParam("search_param") String searchParam) {
        Span currentSpan = Span.current();
        try {
            log.info("AuctionManagement: findEvents endpoint called for find event with partial auction number, name and advertised name: {} " + searchParam);
            List<EventSearchResponse> response = eventService.findEvents(searchParam);
            currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Unexpected error while fetching event list partial auction number, name and advertised name: {}" + searchParam, e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

}
