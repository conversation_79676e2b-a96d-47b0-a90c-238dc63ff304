package com.rb.capability.auctionmanagement.resource.response;

import com.rb.capability.auctionmanagement.resource.response.ContactDetailsResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;

@Builder
public record LocationResponse(
    @Schema(description = "Location Id", example = "123e4567-e89b-12d3-a456-426614174000")
    String id,
    @Schema(description = "type", example = "PERMANENT_SITE")
    String type,
    @Schema(description = "Name", example = "ABC Location")
    String name,
    @Schema(description = "Street Address 1", example = "123 Main St")
    String streetAddress1,
    @Schema(description = "City", example = "Salt Lake City")
    String city,
    @Schema(description = "State Province Code", example = "UT")
    String stateProvinceCode,
    @Schema(description = "State Province", example = "Utah")
    String stateProvince,
    @Schema(description = "Country Code", example = "USA")
    String countryCode,
    @Schema(description = "Country", example = "United States")
    String country,
    @Schema(description = "Postal Code", example = "84101")
    String postalCode,
    @Schema(description = "Latitude", example = "40.7128")
    BigDecimal latitude,
    @Schema(description = "Longitude", example = "-74.0060")
    BigDecimal longitude,
    @Schema(description = "Contact")
    List<ContactDetailsResponse> contacts
) {}