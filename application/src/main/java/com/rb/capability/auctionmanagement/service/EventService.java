package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.exception.DataNotFoundException;
import com.rb.capability.auctionmanagement.domain.enums.*;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.domain.enums.Reason;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.mapper.EventMapper;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.auctionmanagement.resource.request.EventStatusRequest;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.resource.response.EventResponse;
import com.rb.capability.auctionmanagement.resource.response.EventSearchResponse;
import com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import static com.rb.capability.auctionmanagement.audit.AuditHelper.extractFieldValues;
import static com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus.Confirmed;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventService {

    private final EventRepository eventRepository;
    private final PlacesServiceClient placesServiceClient;
    private final SaleNumberAllocationService saleNumberAllocationService;
    private final AuditService auditService;
    private final AsyncEventService asyncEventService;
    private static final String DO_NOT_USE = "DO NOT USE";

    @Transactional
    @WithSpan("create-event")
    public EventResponse createEvent(EventRequest request) {
        try {
            duplicateAuctionsValidation(request.startDateTime(), request.siteId(), request.timezone());
            int year = LocalDate.parse(request.startDateTime()).getYear();
            Integer saleNumber = request.auctionNumber() != null ? request.auctionNumber() :
                    saleNumberAllocationService.generateSaleNumber(year, request);
            EventEntity eventEntity = getEventEntityToSave(request, saleNumber);
            EventEntity savedEvent = eventRepository.save(eventEntity);
            saleNumberAllocationService.markSaleNumberInUse(savedEvent);
            auditService.auditInsert(savedEvent);
            asyncEventService.processEventAsync(savedEvent);

            Span.current().setAttribute("app.auction.auction_number", savedEvent.getSaleNumber());
            return EventResponse.success(savedEvent.getGuid(), savedEvent.getSaleNumber());
        } catch (Exception e) {
            log.error("Error creating event", e);
            throw new BusinessException(e.getMessage());
        }
    }

    @NotNull
    private static EventEntity getEventEntityToSave(EventRequest request, Integer saleNumber) {
        EventEntity eventEntity = EventMapper.INSTANCE.toEntity(request);
        eventEntity.setStatus(AuctionEventStatus.New.name());
        eventEntity.setSchedulingStatus(SchedulingStatus.valueOf(request.schedulingStatus()));
        if (SchedulingStatus.valueOf(request.schedulingStatus()) == Confirmed) {
            eventEntity.setInitialAuctionSyncDate(new Timestamp(System.currentTimeMillis()));
        }
        eventEntity.setIsRegistrationOpen(false);
        eventEntity.setIsOlrEnabled(false);
        eventEntity.setSaleNumber(String.valueOf(saleNumber));

        // Set source auction number if provided
        if (request.sourceAuctionNumber() != null && !request.sourceAuctionNumber().isEmpty()) {
            eventEntity.setSourceAuctionNumber(request.sourceAuctionNumber());
        }

        return eventEntity;
    }

    @Transactional(readOnly = true)
    public EventDetailsResponse getEventDetails(String eventGuid) {
        Span.current().setAttribute("app.auction.method", "inside getEventDetails");
        EventEntity eventEntity = eventRepository.findByGuid(eventGuid)
                .orElseThrow(() -> new DataNotFoundException(Reason.NOT_FOUND, String.format("Event not found with GUID: %s", eventGuid)));
        Span.current().setAttribute("app.auction.entity", eventEntity.toString());
        EventDetailsResponse response = EventDetailsResponse.fromEntity(eventEntity, placesServiceClient);
        if (response == null) {
            throw new BusinessException("Error while fetching event details");
        }
        return response;

    }

    /**
     * Find events based on provided filters.
     *
     * @param fromDate   from (mandatory)
     * @param toDate to (mandatory)
     * @param schedulingStatus scheduling_status (Optional)
     * @param eventTypes event_type (Optional)
     * @param locationTypes location_type (Optional)
     * @param rolledOver rolled_over (Optional)
     * @return List of events matching the provided filters
     */
    public List<EventDetailsResponse> findEventsWithFilters(String fromDate, String toDate, String[] schedulingStatus,
                                                            String[] eventTypes, String[] locationTypes, Boolean rolledOver, Boolean... includeDoNotUse) {
        try {
            Timestamp startDate = Timestamp.from(ZonedDateTime.parse(fromDate).toInstant());
            Timestamp endDate = Timestamp.from(ZonedDateTime.parse(toDate).toInstant());
            List<SchedulingStatus> schedulingStatusList = Optional.ofNullable(schedulingStatus).map(status ->
                    Arrays.stream(status).map(SchedulingStatus::valueOf).toList()).orElse(null);

            List<String> primaryTypeList = Optional.ofNullable(eventTypes).map(list ->
                    Arrays.stream(list).map(String::toLowerCase).toList()).orElse(null);

            List<EventLocationType> locationTypeList = Optional.ofNullable(locationTypes).map(list ->
                    Arrays.stream(list).map(EventLocationType::valueOf).toList()).orElse(null);

            List<EventEntity> allEvents = eventRepository.findEventsWithFilters(startDate, endDate);
            boolean shouldIncludeDoNotUse = includeDoNotUse != null && includeDoNotUse.length > 0 && includeDoNotUse[0] != null && includeDoNotUse[0];
            return EventDetailsResponse.fromEntityListBatch(allEvents.stream().filter(event -> schedulingStatusList == null
                            || (event.getSchedulingStatus() != null && schedulingStatusList.contains(event.getSchedulingStatus())))
                    .filter(event -> primaryTypeList == null || (event.getPrimaryClassification() != null
                            && primaryTypeList.contains(event.getPrimaryClassification().toLowerCase())))
                    .filter(event -> locationTypeList == null || (event.getLocationType() != null
                            && locationTypeList.contains(event.getLocationType())))
                    .filter(event -> rolledOver == null
                            || Boolean.TRUE.equals(event.getIsRolledOver()) == rolledOver)
                    .filter(event -> shouldIncludeDoNotUse
                            || event.getEventAdvertisedName() == null
                            || !event.getEventAdvertisedName().toUpperCase().startsWith(DO_NOT_USE))
                    .toList(), placesServiceClient);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Transactional(readOnly = true)
    public Optional<EventEntity> findEventByGuid(String eventGuid) {
        return eventRepository.findByGuid(eventGuid);
    }

    public void saveEvent(EventEntity eventEntity) {
        eventRepository.save(eventEntity);
        Span.current().setAttribute(SpanAttributeKeys.EVENT_ID, eventEntity.getGuid());
    }

    @Transactional
    public EventResponse updateEvent(String auctionId, EventRequest request) {

        return eventRepository.findByGuid(auctionId)
                .map(eventEntity -> {
                    Map<String, Object> oldValues = extractFieldValues(eventEntity);
                    eventEntity.setOldValues(oldValues);
                    SchedulingStatus previousStatus = eventEntity.getSchedulingStatus();
                    SchedulingStatus newSchedulingStatus = SchedulingStatus.valueOf(request.schedulingStatus());
                    EventMapper.INSTANCE.updateEntityFromRequest(request, eventEntity);
                    setInitialAuctionSyncDate(eventEntity, previousStatus);
                    setTalBidOpenDate(eventEntity, previousStatus, newSchedulingStatus);
                    try {
                        EventEntity updatedEvent = eventRepository.save(eventEntity);
                        auditService.auditUpdate(updatedEvent);
                        asyncEventService.processEventAsync(updatedEvent);
                        Span.current().setAttribute("app.auction.auction_number", updatedEvent.getSaleNumber());
                        return EventResponse.success(updatedEvent.getGuid(), updatedEvent.getSaleNumber());
                    } catch (Exception e) {
                        Span.current().setAttribute("app.am.error", e.getMessage());
                        throw new DataNotFoundException(Reason.NOT_FOUND, "EVENT_UPDATE_FAILED: " + e.getMessage());
                    }
                })
                .orElseThrow(() -> new DataNotFoundException(Reason.NOT_FOUND, String.format("Event not found with GUID: %s", auctionId)));

    }

    public Map<String, Object> getAuditValues(EventEntity entity) {
        return extractFieldValues(entity);
    }

    private void setInitialAuctionSyncDate(EventEntity eventEntity, SchedulingStatus oldSchedulingStatus) {
        if (oldSchedulingStatus == SchedulingStatus.Proposed
                && (eventEntity.getSchedulingStatus() == Confirmed
                || eventEntity.getSchedulingStatus() == SchedulingStatus.Published
                || eventEntity.getSchedulingStatus() == SchedulingStatus.Cancelled)) {
            eventEntity.setInitialAuctionSyncDate(new Timestamp(System.currentTimeMillis()));
        }
    }

    @Transactional
    public EventResponse updateEventStatus(String auctionId, EventStatusRequest request) {
        try {
            return eventRepository.findByGuid(auctionId)
                    .map(eventEntity -> {
                        Map<String, Object> oldValues = extractFieldValues(eventEntity);
                        eventEntity.setOldValues(oldValues);

                        if (request.status() != null) {
                            eventEntity.setStatus(request.status());
                            eventEntity.setLastModifiedDate(new Timestamp(System.currentTimeMillis()));
                        }

                        switch (AuctionEventStatus.findByStatusName(request.status())) {
                            case LotNumbering -> eventEntity.setCatalogExportDate(null);
                            case Finalized -> {
                                eventEntity.setCatalogExportDate(request.catalogExportDate() != null
                                        ? Timestamp.from(ZonedDateTime.parse(request.catalogExportDate()).toInstant())
                                        : new Timestamp(System.currentTimeMillis()));
                                eventEntity.setIsOlrEnabled(true);
                            }
                            default -> {
                                eventEntity.setCatalogExportDate(null);
                            }
                        }

                        EventEntity updatedEvent = eventRepository.save(eventEntity);
                        auditService.auditUpdate(updatedEvent);

                        Span.current().setAttribute("app.auction.auction_number", updatedEvent.getSaleNumber());
                        return EventResponse.success(updatedEvent.getGuid(), updatedEvent.getSaleNumber());
                    })
                    .orElseThrow(() -> new DataNotFoundException(Reason.NOT_FOUND,
                            String.format("Event not found with GUID: %s", auctionId)));
        } catch (Exception e) {
            log.error("Error updating event status for auction: {}", auctionId, e);
            Span.current().setAttribute("app.external.exception", e.getMessage());
            throw new BusinessException("Failed to update auction status: " + e.getMessage());
        }
    }

    private static void setTalBidOpenDate(EventEntity eventEntity, SchedulingStatus previousStatus, SchedulingStatus newSchedulingStatus) {
        // Set TAL Auto Open date for Agriculture events with On the Farm location type
        boolean isStatusTransitionToPublished =
                previousStatus != SchedulingStatus.Published
                        && newSchedulingStatus == SchedulingStatus.Published;

        boolean isTalFormat = SellingFormat.TAL.name().equalsIgnoreCase(eventEntity.getSellingFormats());

        if (isStatusTransitionToPublished
                && AuctionEventClassification.isAgriculture(eventEntity.getPrimaryClassification())
                && eventEntity.getLocationType() == EventLocationType.ON_THE_FARM
                && isTalFormat
                && eventEntity.getTalBidAutoOpenDate() == null) {

            LocalDate startDate = eventEntity.getEventStartDate().toInstant()
                    .atZone(ZoneId.of(eventEntity.getTimezone()))
                    .toLocalDate();
            // Calculate 5 days before start date at 12:00 PM
            ZonedDateTime talOpenDateTime = startDate.minusDays(5)
                    .atTime(12, 0)
                    .atZone(ZoneId.of(eventEntity.getTimezone()));

            eventEntity.setTalBidAutoOpenDate(Timestamp.from(talOpenDateTime.toInstant()));
        }
    }

    /**
     * Find events based on provided auction number,name and advertised name.
     *
     * @param searchParam search
     * @return List of events matching the provided auction number,name and advertised name
     */
    public List<EventSearchResponse> findEvents(String searchParam) {
        try {
            List<EventSearchResponse> allEvents = eventRepository.findEvents(searchParam);
            return allEvents;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Transactional
    public void saveEvents(List<EventEntity> events) {
        eventRepository.saveAll(events);
    }

    @Transactional(readOnly = true)
    @WithSpan("get-event-details-by-auction-number")
    public EventDetailsResponse getEventDetailsByAuctionNumber(String auctionNumber) {
        Span.current().setAttribute("app.auction.method", "inside getEventDetailsByAuctionNumber");
        EventEntity eventEntity = eventRepository.findBySaleNumber(auctionNumber)
                .orElseThrow(() -> new DataNotFoundException(Reason.NOT_FOUND,
                        String.format("Event not found with auction number: %s", auctionNumber)));

        Span.current().setAttribute("app.auction.entity", eventEntity.toString());
        EventDetailsResponse response = EventDetailsResponse.fromEntity(eventEntity, placesServiceClient);
        if (response == null) {
            throw new BusinessException("Error while fetching event details");
        }
        return response;
    }

    public void duplicateAuctionsValidation(String startDate, String siteId, String timezone) {
        Span.current().setAttribute("app.auction.startDate", startDate);
        Timestamp utcTimestamp = EventMapper.INSTANCE.convertToStartTimeInUtc(startDate, timezone);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String utcDateString = sdf.format(utcTimestamp);
        Span.current().setAttribute("app.auction.utcDateString", utcDateString);
        Optional<String> saleNumberOpt = eventRepository.checkAuctionWithStartDateAndSiteId(utcDateString, siteId);
        if (saleNumberOpt.isPresent()) {
            String message = "Cannot create the auction. Oracle will reject the auction as duplicate because auction"
                    + " [" + saleNumberOpt.get() + "] has the same Host Site and Start Date. Please change the start date of"
                    + " auction [" + saleNumberOpt.get() + "] and then try again.";
            throw new BusinessException(message);
        }
    }


}
