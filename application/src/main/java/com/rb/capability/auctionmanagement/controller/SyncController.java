package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.service.AsyncEventService;
import com.rb.capability.auctionmanagement.service.EventService;
import com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys;
import io.opentelemetry.api.trace.Span;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Sync", description = "Sync APIs for retrying failed operations")
public class SyncController {

    private final EventService eventService;
    private final AsyncEventService asyncEventService;
    private final PlacesServiceClient placesServiceClient;

    @PostMapping("/retry/salesforce/{auction_id}")
    @Operation(summary = "Retrigger Salesforce API call for an event",
            description = "Retriggers the Salesforce API call for a specific event using its GUID")
    @ApiResponse(responseCode = "200", description = "Successfully retriggered Salesforce API call")
    @ApiResponse(responseCode = "404", description = "Event not found")
    @ApiResponse(responseCode = "500", description = "Internal server error during processing")
    public ResponseEntity<Map<String, String>> retriggerSalesforceCall(@PathVariable("auction_id") String auctionId) {
        log.info("Retriggering Salesforce API call for event {}", StringUtils.replaceAll(auctionId, "[\n\r]", ""));
        
        Span currentSpan = Span.current();
        currentSpan.setAttribute(SpanAttributeKeys.EVENT_ID, auctionId);
        currentSpan.setAttribute(SpanAttributeKeys.APP_ACTION_NAME, "retrigger-salesforce-call");

        try {
            return eventService.findEventByGuid(auctionId)
                .map(event -> {
                    EventDetailsResponse response = EventDetailsResponse.fromEntity(event, placesServiceClient);
                    asyncEventService.callSalesforceAsync(response);
                    currentSpan.setAttribute(SpanAttributeKeys.APP_STATUS, "success");
                    return ResponseEntity.ok(Map.of(
                        "message", "Salesforce API call retriggered successfully for event: " + auctionId
                    ));
                })
                .orElseGet(() -> {
                    currentSpan.setAttribute(SpanAttributeKeys.APP_STATUS, "not_found");
                    return ResponseEntity.notFound().build();
                });
        } catch (Exception e) {
            log.error("Failed to retrigger Salesforce API call for event: {}", auctionId, e);
            currentSpan.setAttribute(SpanAttributeKeys.APP_STATUS, "error");
            currentSpan.setAttribute(SpanAttributeKeys.APP_ERROR, e.getMessage());
            currentSpan.recordException(e);
            throw e;
        }
    }

    @PostMapping("/retry/publish/{auction_id}")
    @Operation(summary = "Retrigger Kafka event publishing",
            description = "Retriggers the Kafka event publishing for a specific event using its GUID")
    @ApiResponse(responseCode = "200", description = "Successfully retriggered Kafka event publishing")
    @ApiResponse(responseCode = "404", description = "Event not found")
    @ApiResponse(responseCode = "500", description = "Internal server error during processing")
    public ResponseEntity<Map<String, String>> retriggerKafkaPublish(@PathVariable("auction_id") String auctionId) {
        log.info("Retriggering Kafka event publishing for event {}", StringUtils.replaceAll(auctionId, "[\n\r]", ""));

        Span currentSpan = Span.current();
        currentSpan.setAttribute(SpanAttributeKeys.EVENT_ID, auctionId);
        currentSpan.setAttribute(SpanAttributeKeys.APP_ACTION_NAME, "retrigger-kafka-publish");

        try {
            return eventService.findEventByGuid(auctionId)
                .map(event -> {
                    EventDetailsResponse response = EventDetailsResponse.fromEntity(event, placesServiceClient);
                    asyncEventService.publishAuctionEventAsync(response);
                    currentSpan.setAttribute(SpanAttributeKeys.APP_STATUS, "success");
                    return ResponseEntity.ok(Map.of(
                        "message", "Kafka event publishing retriggered successfully for event: " + auctionId
                    ));
                })
                .orElseGet(() -> {
                    currentSpan.setAttribute(SpanAttributeKeys.APP_STATUS, "not_found");
                    return ResponseEntity.notFound().build();
                });
        } catch (Exception e) {
            log.error("Failed to retrigger Kafka event publishing for event: {}", auctionId, e);
            currentSpan.setAttribute(SpanAttributeKeys.APP_STATUS, "error");
            currentSpan.setAttribute(SpanAttributeKeys.APP_ERROR, e.getMessage());
            currentSpan.recordException(e);
            throw e;
        }
    }
}