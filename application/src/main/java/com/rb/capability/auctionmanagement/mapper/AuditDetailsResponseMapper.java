package com.rb.capability.auctionmanagement.mapper;


import com.rb.capability.auctionmanagement.infra.jpa.entity.AuditEntity;
import com.rb.capability.auctionmanagement.resource.response.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AuditDetailsResponseMapper {
    AuditDetailsResponseMapper INSTANCE = Mappers.getMapper(AuditDetailsResponseMapper.class);

    AuditDetailsResponse toResponse(AuditEntity entity);



}