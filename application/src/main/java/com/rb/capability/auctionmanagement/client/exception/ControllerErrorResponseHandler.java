package com.rb.capability.auctionmanagement.client.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.StatusType;
import org.zalando.problem.ThrowableProblem;
import org.zalando.problem.spring.common.AdviceTraits;
import org.zalando.problem.spring.web.advice.ProblemHandling;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import static org.zalando.problem.Status.*;

/***
 * <p>
 * Auction Management Application's REST API response error handler
 * The error customizes the response into Problem defined by the <a href="https://www.rfc-editor.org/rfc/rfc7807.html">application/problem+json</a> schema.
 * Problem Spring Web (zalando) is the library used to easily produce the schema @see <a href="https://github.com/zalando/problem-spring-web">Problem Spring Web</a>
 * Stack Traces and Causal chain are disabled by default.
 * For more details @see <a href="https://github.com/RBMarketplace/marketplace-documentation/blob/main/content/api-standards.md#status-reporting">Status Reporting</a>
 * <ul>
 *     <li>The white label error page is disabled by excluding Error MVC Auto Configuration</li>
 *     <li>Application Properties include the following
 *          <ul>
 *              <li>server.servlet.encoding.force: true</li>#Forces encoding to configured charset, usually UTF-8
 *              <li>spring.mvc.throw-exception-if-no-handler-found: true</li>#This throws NoHandlerFoundException
 *              <li>spring.web.resources.add-mappings: false</li>#disables default resource handling
 *          </ul>
 *     </li>
 * </ul>
 * </p>
 */
@ControllerAdvice
@Slf4j
public class ControllerErrorResponseHandler implements ProblemHandling {

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Problem> handleIllegalArgumentException(
            final IllegalArgumentException exception,
            final NativeWebRequest request) {
        var msg = exception.getMessage();
        var title = exception.getMessage();
        return createErrorResponse(exception, request, BAD_REQUEST, msg, title);
    }

    @ExceptionHandler
    public ResponseEntity<Problem> handleHttpServerErrorException(
            final HttpServerErrorException exception,
            final NativeWebRequest request) {
        Status status = valueOf(exception.getStatusCode().value());
        String title = exception.getStatusText();
        return createErrorResponse(exception, request, status, title);
    }

    @Override
    public void log(
            @NonNull final Throwable throwable,
            @NonNull @SuppressWarnings("UnusedParameters") final Problem problem,
            @NonNull @SuppressWarnings("UnusedParameters") final NativeWebRequest request,
            @NonNull final HttpStatus status) {
        MDC.put("status", String.valueOf(status.value()));
        AdviceTraits.log(throwable, status);
    }


    public ThrowableProblem buildProblem(StatusType status, String detail, String title) {
        return Problem.builder()
                .withTitle(StringUtils.isNotEmpty(title) ? title : status.getReasonPhrase())
                .withStatus(status)
                .withDetail(detail)
                .build();
    }

    private ResponseEntity<Problem> createErrorResponse(
            final Throwable throwable,
            final NativeWebRequest request,
            StatusType status,
            String title) {
        return createErrorResponse(throwable, request, status, status.getReasonPhrase(), title);
    }

    private ResponseEntity<Problem> createErrorResponse(
            final Throwable throwable,
            final NativeWebRequest request,
            StatusType status,
            String detail,
            String title) {
        var problem = buildProblem(status, detail, title);
        return create(throwable, problem, request);
    }

    public static String extractErrorDetails(WebClientResponseException e) {
        String errorDetails = e.getMessage();
        try {
            Map<String, Object> errorMessageBody =
                    new ObjectMapper().readValue(e.getResponseBodyAsString(), new TypeReference<HashMap<String, Object>>() {});
            if (errorMessageBody.get("detail") != null && StringUtils.isNotBlank(errorMessageBody.get("detail").toString())) {
                errorDetails = errorMessageBody.get("detail").toString();
            }
        } catch (JsonProcessingException ex) {
            log.debug("No Json response body. Fetch message from exception message");
        }
        return errorDetails;
    }

    @ExceptionHandler
    public ResponseEntity<Problem> dataNotFoundException(
            DataNotFoundException exception,
            NativeWebRequest request) {
        return createErrorResponse(exception, request, NOT_FOUND, exception.getMessage());
    }


    @ExceptionHandler
    public ResponseEntity<Problem> handleSalesforceAuthenticationException(
            SalesforceAuthenticationException exception,
            NativeWebRequest request) {
        return createErrorResponse(exception, request, UNAUTHORIZED, exception.getDetail());
    }

    @Override
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Problem> handleMethodArgumentNotValid(
            @NonNull final MethodArgumentNotValidException ex,
            @NonNull final NativeWebRequest request) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error
                        -> {
            try {
                errors.put(camelCaseToSnakeCase(((FieldError) error).getField()),
                        camelCaseToSnakeCase(((FieldError) error).getField() + ": '" + error.getDefaultMessage() + "'"));
            } catch (Exception e) {
                errors.put(error.getCode(), error.getDefaultMessage());
            }
        }
        );
        return createErrorResponseWithErrorList(ex, request, BAD_REQUEST, errors.values());
    }

    private ResponseEntity<Problem> createErrorResponseWithErrorList(
            final Throwable throwable,
            final NativeWebRequest request,
            StatusType status,
            Collection<String> errors) {
        Span.current().recordException(throwable);
        Problem problem = Problem.builder()
                .withTitle(status.getReasonPhrase())
                .withStatus(status)
                .withDetail(status.getReasonPhrase())
                .with("errors", errors)
                .build();
        return create(throwable, problem, request);
    }

    private String camelCaseToSnakeCase(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char ch = camelCase.charAt(i);
            if (i > 0 && Character.isUpperCase(ch) && Character.isLowerCase(camelCase.charAt(i - 1))) {
                result.append('_');
                result.append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }
}

