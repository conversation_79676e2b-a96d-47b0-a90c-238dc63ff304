package com.rb.capability.auctionmanagement.resource.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rb.capability.common.ExcludeFromJacocoGeneratedReport;
import lombok.Builder;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@ExcludeFromJacocoGeneratedReport
public record CreateEventResponse(
        @JsonProperty("requestId")
        String requestId,
        
        @JsonProperty("clonedEvents")
        List<ClonedEvents> clonedEvents
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    @ExcludeFromJacocoGeneratedReport
    public record ClonedEvents(
            @JsonProperty("saleNumber")
            String saleNumber,
            
            @JsonProperty("id")
            String id,
            
            @JsonProperty("gid")
            String gid,
            
            @JsonProperty("eventStartDate")
            String eventStartDate,
            
            @JsonProperty("eventEndDate")
            String eventEndDate
    ) {
    }
}