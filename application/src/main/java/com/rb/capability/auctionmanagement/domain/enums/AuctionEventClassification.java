package com.rb.capability.auctionmanagement.domain.enums;


import java.util.List;

public enum AuctionEventClassification {
    Industrial("Industrial", EnterpriseEventClassification.INDUSTRIAL),
    Agriculture("Agriculture", EnterpriseEventClassification.AGRICULTURE),
    Charity("Charity", EnterpriseEventClassification.CHARITY),
    RealEstate("Real Estate", EnterpriseEventClassification.REAL_ESTATE),
    Marine("Marine", EnterpriseEventClassification.MARINE),
    Aircraft("Aircraft", EnterpriseEventClassification.AIRCRAFT),
    Online("Online", EnterpriseEventClassification.ONLINE),
    Transportation("Transportation", EnterpriseEventClassification.TRANSPORTATION),
    Dealer<PERSON><PERSON>antler("Dealer Dismantler", EnterpriseEventClassification.DEALER_DISMANTLER)
    ;

    public final String description;
    public final EnterpriseEventClassification enterpriseEventClassification;

    AuctionEventClassification(String description, EnterpriseEventClassification enterpriseEventClassification) {
        this.description = description;
        this.enterpriseEventClassification = enterpriseEventClassification;
    }

    public static String getByEnterpriseEventClassification(String enterpriseEventClassification) {
        for (AuctionEventClassification classification : values()) {
            if (classification.enterpriseEventClassification.name().equalsIgnoreCase(enterpriseEventClassification)) {
                return classification.description;
            }
        }
        return null;
    }

    public static boolean isAgriculture(List<String> eventClassifications) {
        return eventClassifications.contains(AuctionEventClassification.Agriculture.description);
    }

    public static boolean isAgriculture(String classification) {
        return Agriculture.description.equalsIgnoreCase(classification);
    }

    public static boolean isCharity(List<String> eventClassifications) {
        return eventClassifications.contains(AuctionEventClassification.Charity.description);
    }
}
