package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload;
import com.rb.capability.auctionmanagement.resource.response.EventRolloverResponse;
import com.rb.capability.auctionmanagement.resource.response.ValidationResponse;
import com.rb.capability.auctionmanagement.service.EventRollOverService;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;

@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class EventRollOverController {
    private final EventRollOverService eventRollOverService;

    @PostMapping(path = "events/rollover/confirm-rollover", produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("batch-insert")
    @Operation(tags = {"Events"}, summary = "validate sale numbers to number of events for rollover to the next year",
            description = "validate sale numbers to number of events for rollover to the next year")
    public ResponseEntity<ValidationResponse> confirmRollOver(@RequestBody EventRolloverBatchPayload rollOverAuctions) {
        log.info("AuctionManagement: batchRollOver endpoint called");
        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_REQUEST, "AuctionManagement: batchRollOver endpoint called");
        try {
            ValidationResponse response = eventRollOverService.validateRolloverBatch(rollOverAuctions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error creating event", e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @PostMapping(path = "events/rollover/batch-rollover", produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("batch-insert")
    @Operation(tags = {"Events"}, summary = "Copy events for rollover to the next year",
            description = "Copy events for rollover to the next year")
    public ResponseEntity<EventRolloverResponse> batchRollOver(@RequestBody EventRolloverBatchPayload rollOverAuctions) {
        log.info("AuctionManagement: batchRollOver endpoint called");
        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_REQUEST, "AuctionManagement: batchRollOver endpoint called");
        try {
            EventRolloverResponse response  = eventRollOverService.prepareEventRollOver(rollOverAuctions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error creating event", e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

}
