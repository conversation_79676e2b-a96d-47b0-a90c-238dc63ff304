package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload;
import com.rb.capability.auctionmanagement.resource.response.EventRolloverResponse;
import com.rb.capability.auctionmanagement.resource.response.ValidationResponse;
import com.rb.capability.auctionmanagement.service.EventRollOverService;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.zalando.problem.Problem;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;

@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class EventRollOverController {
    private final EventRollOverService eventRollOverService;

    @PostMapping(path = "events/rollover/confirm-rollover", produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("validate-rollover")
    @Operation(tags = {"Events"}, summary = "Validate sale numbers to number of events for rollover to the next year",
            description = "validate sale numbers to number of events for rollover to the next year")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Confirm RollOver - Success"),
        @ApiResponse(responseCode = "400", description = "Bad Request",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class))),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class)))
    })
    public ResponseEntity<ValidationResponse> confirmRollOver(@Valid @RequestBody EventRolloverBatchPayload rollOverAuctions) {
        log.info("AuctionManagement: validate rollover endpoint called");
        Span currentSpan = Span.current();
        currentSpan.setAttribute(LOG_INFO, "rollOverAuctions");
        try {
            ValidationResponse response = eventRollOverService.validateRolloverBatch(rollOverAuctions);
            currentSpan.setAttribute(LOG_INFO, "rollOverAuctions");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error validating rollover batch", e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @PostMapping(path = "events/rollover/batch-rollover", produces = MediaType.APPLICATION_JSON_VALUE)
    @WithSpan("batch-insert")
    @Operation(tags = {"Events"}, summary = "Copy events for rollover to the next year",
            description = "Copy events for rollover to the next year")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Batch RollOver - Success"),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                        schema = @Schema(implementation = Problem.class)))
    })
    public ResponseEntity<EventRolloverResponse> batchRollOver(@RequestBody EventRolloverBatchPayload rollOverAuctions) {
        log.info("AuctionManagement: batchRollOver endpoint called");
        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_REQUEST, "AuctionManagement: batchRollOver endpoint called");
        try {
            EventRolloverResponse response  = eventRollOverService.prepareEventRollOver(rollOverAuctions);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error Occured in batch rollover", e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

}
