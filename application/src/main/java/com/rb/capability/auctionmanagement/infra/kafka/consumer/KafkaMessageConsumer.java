package com.rb.capability.auctionmanagement.infra.kafka.consumer;

import com.rb.capability.auctionmanagement.infra.kafka.processor.EnterpriseEventMessageProcessor;
import com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys;
import com.rb.capability.common.launchdarkly.FeatureToggleClient;
import com.rb.essentials.capability.exception.BusinessException;
import com.rbauction.enterprise.events.models.sale.SaleEvent;
import io.opentelemetry.api.trace.Span;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import static com.rb.capability.common.config.LaunchDarklyFeature.consumeAuctionFromEnterpriseSaleEventTopic;

@Component
@Slf4j
@RequiredArgsConstructor
public class KafkaMessageConsumer {
    private final EnterpriseEventMessageProcessor<ConsumerRecord<String, SaleEvent>> enterpriseEventMessageProcessor;
    private final FeatureToggleClient featureToggleClient;

    @KafkaListener(groupId = "${spring.kafka.consumer.group-id}",
            topics = "${kafka.topics.enterprise_mars_events}")
    public void listenMarsEvents(ConsumerRecord<String, SaleEvent> consumerRecord) {
        try {
            if (!featureToggleClient.isFeatureFlagEnabled(consumeAuctionFromEnterpriseSaleEventTopic.key)) {
                log.info("Auction Management is enabled. skipping processing enterprise sale event");
                Span.current().setAttribute("app.skip.process-sale-event", consumerRecord.value().getSaleEventGUID());
                return;
            }

            Span.current().setAttribute(SpanAttributeKeys.LOG_INFO, "Received enterprise_mars_events message: " + consumerRecord.value());
            Span.current().setAttribute(SpanAttributeKeys.EVENT_ID, consumerRecord.value().getSaleEventGUID());
            enterpriseEventMessageProcessor.processRecord(consumerRecord);
        } catch (Exception e) {
            Span.current().setAttribute("app.error.process-kafka-message", "enterprise-mars-event");
            Span.current().recordException(e);
            log.error("error in consume event on enterprise sale event {}", consumerRecord);
            throw new BusinessException("Error while  consuming enterprise sale event " + consumerRecord);
        }
    }
}
