package com.rb.capability.auctionmanagement.domain.enums;

/**
 * Represents the different formats in which items can be sold at an auction event.
 */
public enum SellingFormat {
    TAL,
    OLR,
    ONLINE_AUCTION,
    MARKETPLACE;

    public static String getSellingFormatFromEnterpriseMessage(String format) {
        if (format == null || format.isEmpty()) {
            return null;
        }
        return switch (format) {
            case "LIVE_TAL_ONLY" -> "TAL";
            case "LIVE_OLR_TAL" -> "TAL,OLR";
            case "LIVE_AUCTION" -> "OLR";
            case "ONLINE_AUCTION" -> "ONLINE_AUCTION";
            case "MARKETPLACE" -> "MARKETPLACE";
            default -> null;
        };
    }
}