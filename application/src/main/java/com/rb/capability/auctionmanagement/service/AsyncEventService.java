package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.SalesforceServiceClient;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.kafka.producer.EventProducer;
import com.rb.capability.auctionmanagement.resource.request.SalesforceEventRequest;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.resource.response.SalesforceEventResponse;
import com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys;
import com.rb.capability.common.BasicUtils;
import com.rb.capability.common.launchdarkly.FeatureToggleClient;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus.Proposed;
import static com.rb.capability.common.config.LaunchDarklyFeature.createAuctionInSalesforce;

@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncEventService {

    private final EventProducer eventProducer;
    private final SalesforceServiceClient salesforceServiceClient;
    private final PlacesServiceClient placesServiceClient;
    private final FeatureToggleClient featureToggleClient;
    private final LegalEntityService legalEntityService;

    @Async("eventTaskExecutor")
    @WithSpan("process-event-async")
    public void processEventAsync(EventEntity savedEvent) {

        if (Proposed.equals(savedEvent.getSchedulingStatus())) {
            Span.current().setAttribute("app.skip.process-event", savedEvent.getGuid());
            return;
        }
        // Create a new span for this async operation
        Tracer tracer = GlobalOpenTelemetry.getTracer("com.rb.capability.auctionmanagement");
        Span span = tracer.spanBuilder("process-event-async")
                .setAttribute(SpanAttributeKeys.EVENT_ID, savedEvent.getGuid())
                .startSpan();

        try (Scope scope = span.makeCurrent()) {
            EventDetailsResponse response = EventDetailsResponse.fromEntity(savedEvent, placesServiceClient);
            CompletableFuture.allOf(
                publishAuctionEventAsync(response),
                callSalesforceAsync(response)
            ).exceptionally(throwable -> {
                log.error("Error in async operations for event: {}", savedEvent.getGuid(), throwable);
                span.recordException(throwable);
                return null;
            });
        } finally {
            span.end();
        }
    }

    @Async("eventTaskExecutor")
    public CompletableFuture<Void> publishAuctionEventAsync(EventDetailsResponse response) {
        // Create a new span for this async operation
        Tracer tracer = GlobalOpenTelemetry.getTracer("com.rb.capability.auctionmanagement");
        Span span = tracer.spanBuilder("publish-auction-event-async")
                .setAttribute(SpanAttributeKeys.EVENT_ID, response.eventGuid())
                .startSpan();

        try (Scope scope = span.makeCurrent()) {
            try {
                eventProducer.publishAuctionEvent(response);
                span.setAttribute("app.event.publish_success", "true");
            } catch (Exception e) {
                log.error("Failed to publish auction event asynchronously: {}", response.eventGuid(), e);
                span.setAttribute("app.event.publish_error", "true");
                span.recordException(e);
            }
            return CompletableFuture.completedFuture(null);
        } finally {
            span.end();
        }
    }

    @Async("eventTaskExecutor")
    public CompletableFuture<Void> callSalesforceAsync(EventDetailsResponse response) {
        // Create a new span for this async operation
        Tracer tracer = GlobalOpenTelemetry.getTracer("com.rb.capability.auctionmanagement");
        Span span = tracer.spanBuilder("call-salesforce-async")
                .setAttribute(SpanAttributeKeys.EVENT_ID, response.eventGuid())
                .startSpan();

        try (Scope scope = span.makeCurrent()) {
            try {
                if (featureToggleClient.isFeatureFlagEnabled(createAuctionInSalesforce.key)) {
                    callSalesforceWithRetry(response);
                    span.setAttribute("app.salesforce.call_success", "true");
                }
            } catch (Exception e) {

                log.error("Error calling Salesforce for event: {}", response.eventGuid(), e);
                span.setAttribute("app.salesforce.call_error", "true");
                span.recordException(e);
            }
            return CompletableFuture.completedFuture(null);
        } finally {
            span.end();
        }
    }

    public void callSalesforceWithRetry(EventDetailsResponse response) {
        log.info("Attempting to call Salesforce for event: {}", response.eventGuid());
        callSalesforce(response);
    }

    public void callSalesforce(EventDetailsResponse eventResponse) {
        if (eventResponse != null) {
            String legalEntityName = legalEntityService.getBusinessNameFromLegalEntityId(eventResponse.legalEntityId());
            SalesforceEventRequest salesforceEventRequest = SalesforceEventRequest.builder()
                    .allowIpInspection(eventResponse.allowIpInspection())
                    .sourceSystem("AUCTION_MANAGEMENT")
                    .createdTime(eventResponse.createdDate().getTime())
                    .auctionNumber(eventResponse.saleNumber())
                    .businessUnit(SalesforceEventRequest.BusinessUnit.builder()
                            .code(eventResponse.businessUnit().code())
                            .name(eventResponse.businessUnit().name())
                            .build())
                    .timezone(eventResponse.timezone())
                    .advertisedName(eventResponse.eventAdvertisedName())
                    .currencyCode(eventResponse.currency())
                    .primaryClassification(eventResponse.primaryClassification())
                    .schedulingStatus(eventResponse.schedulingStatus())
                    .endDateTime(eventResponse.eventEndDate().getTime())
                    .auctionId(eventResponse.eventGuid())
                    .additionalClassifications(eventResponse.additionalClassification())
                    .site(SalesforceEventRequest.Site.builder()
                            .id(eventResponse.siteId())
                            .advertisedName(eventResponse.site().advertisedName())
                            .build())
                    .startDateTime(eventResponse.eventStartDate().getTime())
                    .name(eventResponse.name())
                    .brand(eventResponse.brand())
                    .legalEntityName(legalEntityName)
                    .location(SalesforceEventRequest.Location.builder()
                            .id(eventResponse.locationId())
                            .name(eventResponse.location().name())
                            .address(eventResponse.location().streetAddress1())
                            .city(eventResponse.location().city())
                            .stateProvince(eventResponse.location().stateProvince())
                            .countryCode(eventResponse.location().countryCode() == null && eventResponse.location().country() != null
                                    ? eventResponse.location().country() : eventResponse.location().countryCode())
                            .stateProvinceCode(eventResponse.location().stateProvinceCode() == null && eventResponse.location().stateProvince() != null
                                    ? eventResponse.location().stateProvince() : eventResponse.location().stateProvinceCode())
                            .latitude(eventResponse.location().latitude().doubleValue())
                            .longitude(eventResponse.location().longitude().doubleValue())
                            .streetAddress1(eventResponse.location().streetAddress1())
                            .country(eventResponse.location().country())
                            .postalCode(eventResponse.location().postalCode())
                            .type(eventResponse.locationType() != null ? eventResponse.locationType().description : null)
                            .contacts(eventResponse.location().contacts())
                            .build())
                    .rbMarketplaceAuction(eventResponse.isRbMarketplaceSale())
                    .marsEvent(true)
                    .eventSiteConfiguration(eventResponse.siteConfiguration())
                    .privateTreaty(eventResponse.privateTreaty() != null && eventResponse.privateTreaty())
                    .initialAuctionSyncDate(eventResponse.initialAuctionSyncDate() != null
                            ? eventResponse.initialAuctionSyncDate().getTime() : null)
                    .notes(eventResponse.notes())
                    .build();

            log.info("Calling Salesforce with request: {}", BasicUtils.convertObjectToJsonString(salesforceEventRequest));
            Span.current().setAttribute("app.salesforce.request", Objects.requireNonNull(BasicUtils.convertObjectToJsonString(salesforceEventRequest)));
            SalesforceEventResponse salesforceEventResponse = salesforceServiceClient.createEvent(salesforceEventRequest);
            Span.current().setAttribute("app.salesforce.response.id", salesforceEventResponse.getId());
        }
    }
}