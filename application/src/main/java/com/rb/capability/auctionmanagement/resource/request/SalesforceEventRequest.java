package com.rb.capability.auctionmanagement.resource.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.rb.capability.auctionmanagement.resource.response.ContactDetailsResponse;
import com.rb.capability.common.ExcludeFromJacocoGeneratedReport;
import lombok.Builder;

import java.util.List;

@Builder(toBuilder = true)
@ExcludeFromJacocoGeneratedReport
public record SalesforceEventRequest(

        @JsonProperty("allowIpInspection")
        Boolean allowIpInspection,

        @JsonProperty("brand")
        String brand,

        @JsonProperty("businessUnit")
        BusinessUnit businessUnit,

        @JsonProperty("timezone")
        String timezone,

        @JsonProperty("legalEntityName")
        String legalEntityName,

        @JsonProperty("advertisedName")
        String advertisedName,

        @JsonProperty("currencyCode")
        List<String> currencyCode,

        @JsonProperty("primaryClassification")
        String primaryClassification,

        @JsonProperty("schedulingStatus")
        String schedulingStatus,

        @JsonProperty("endDateTime")
        Long endDateTime,

        @JsonProperty("auctionId")
        String auctionId,

        @JsonProperty("additionalClassifications")
        List<String> additionalClassifications,

        @JsonProperty("site")
        Site site,

        @JsonProperty("startDateTime")
        Long startDateTime,

        @JsonProperty("name")
        String name,

        @JsonProperty("location")
        Location location,

        @JsonProperty("marsEvent")
        Boolean marsEvent,

        @JsonProperty("serverName")
        String serverName,

        @JsonProperty("previousYearEvent")
        String previousYearEvent,

        @JsonProperty("rbMarketplaceAuction")
        Boolean rbMarketplaceAuction,

        @JsonProperty("notes")
        String notes,

        @JsonProperty("sourceSystem")
        String sourceSystem,

        @JsonProperty("createdTime")
        Long createdTime,

        @JsonProperty("auctionNumber")
        String auctionNumber,

        @JsonProperty("eventSiteConfiguration")
        String eventSiteConfiguration,

        @JsonProperty("privateTreaty")
        boolean privateTreaty,

        @JsonProperty("initialAuctionSyncDate")
        Long initialAuctionSyncDate

) {
    @ExcludeFromJacocoGeneratedReport
    @Builder(toBuilder = true)
    public record BusinessUnit(
            @JsonProperty("code")
            String code,

            @JsonProperty("name")
            String name
    ) {
    }

    @ExcludeFromJacocoGeneratedReport
    @Builder(toBuilder = true)
    public record Site(
            @JsonProperty("advertisedName")
            String advertisedName,

            @JsonProperty("id")
            String id
    ) {
    }

    @ExcludeFromJacocoGeneratedReport
    @Builder(toBuilder = true)
    public record Location(
            @JsonProperty("city")
            String city,

            @JsonProperty("countryCode")
            String countryCode,
            @JsonProperty("country")
            String country,
            @JsonProperty("name")
            String name,
            @JsonProperty("stateProvince")
            String stateProvince,
            @JsonProperty("address")
            String address,
            @JsonProperty("postalCode")
            String postalCode,
            @JsonProperty("latitude")
            Double latitude,
            @JsonProperty("longitude")
            Double longitude,
            @JsonProperty("streetAddress1")
            String streetAddress1,
            @JsonProperty("streetAddress2")
            String streetAddress2,

            @JsonProperty("id")
            String id,

            @JsonProperty("type")
            String type,

            @JsonProperty("contacts")
            List<ContactDetailsResponse> contacts,

            @JsonProperty("stateProvinceCode")
            String stateProvinceCode
    ) {
    }
}
