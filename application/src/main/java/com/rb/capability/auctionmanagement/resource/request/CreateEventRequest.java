package com.rb.capability.auctionmanagement.resource.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.rb.capability.common.ExcludeFromJacocoGeneratedReport;
import lombok.Builder;

import java.util.List;

@Builder
@ExcludeFromJacocoGeneratedReport
public record CreateEventRequest(
        @JsonProperty("requestId")
        String requestId,
        
        @JsonProperty("eventParameters")
        List<EventParameters> eventParameters
) {
    @Builder
    @ExcludeFromJacocoGeneratedReport
    public record EventParameters(
            @JsonProperty("eventStartDate")
            String eventStartDate,
            
            @JsonProperty("saleNumber")
            String saleNumber,
            
            @JsonProperty("newSaleNumber")
            String newSaleNumber,
            
            @JsonProperty("eventEndDate")
            String eventEndDate
    ) {
    }
}