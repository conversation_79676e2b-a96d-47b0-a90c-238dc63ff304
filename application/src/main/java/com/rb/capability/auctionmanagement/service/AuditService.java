package com.rb.capability.auctionmanagement.service;


import com.rb.capability.auctionmanagement.audit.AuditDisplayName;
import com.rb.capability.auctionmanagement.audit.AuditHelper;
import com.rb.capability.auctionmanagement.audit.NoAudit;
import com.rb.capability.auctionmanagement.infra.jpa.entity.AuditEntity;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.AuditRepository;
import com.rb.capability.auctionmanagement.resource.response.AuditDetailsResponse;
import com.rb.essentials.capability.exception.BusinessException;
import jakarta.persistence.Transient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditService {

    private final AuditRepository auditRepository;

    @Transactional(readOnly = true)
    public List<AuditDetailsResponse> getAuditDetails(String eventGuid) {
        try {
            List<AuditEntity> auditEntity = auditRepository.findByGuidOrderByChangedAtDesc(eventGuid);
            return AuditDetailsResponse.fromEntityList(auditEntity);

        } catch (Exception e) {
            log.error("Error fetching audit details for GUID: {}", eventGuid, e);
            throw new BusinessException(e.getMessage());
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void auditInsert(EventEntity eventEntity) {
        List<AuditEntity> auditEntries = new ArrayList<>();
        String timezone = eventEntity.getTimezone();

        for (Field field : EventEntity.class.getDeclaredFields()) {
            if (field.isAnnotationPresent(Transient.class) || field.isAnnotationPresent(NoAudit.class)) {
                continue;
            }

            field.setAccessible(true);
            try {
                Object newValue = field.get(eventEntity);
                if (newValue != null) {
                    // Special handling for timestamp fields to convert to event timezone
                    if (newValue instanceof Timestamp && AuditHelper.isTimestampField(field.getName()) && timezone != null) {
                        // Convert the timestamp to the event's timezone for audit display
                        newValue = AuditHelper.convertTimestampToLocalTime((Timestamp) newValue, timezone);
                    }

                    String displayName = field.isAnnotationPresent(AuditDisplayName.class)
                            ? field.getAnnotation(AuditDisplayName.class).value()
                            : field.getName();

                    AuditEntity log = new AuditEntity();
                    log.setFieldName(displayName);
                    log.setGuid(eventEntity.getGuid());
                    log.setOldValue(null);
                    log.setNewValue(newValue.toString());
                    log.setChangedBy(eventEntity.getCreatedBy());
                    log.setChangedAt(new Date());
                    auditEntries.add(log);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Error accessing field values", e);
            }
        }

        if (!auditEntries.isEmpty()) {
            auditRepository.saveAll(auditEntries);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void auditUpdate(EventEntity eventEntity) {
        Map<String, Object> oldValues = eventEntity.getOldValues();
        if (oldValues == null) {
            return;
        }

        List<AuditEntity> auditEntries = new ArrayList<>();
        String timezone = eventEntity.getTimezone();

        for (Field field : EventEntity.class.getDeclaredFields()) {
            if (field.isAnnotationPresent(Transient.class) || field.isAnnotationPresent(NoAudit.class)) {
                continue;
            }

            field.setAccessible(true);
            try {
                Object oldValue = oldValues.get(field.getName());
                Object newValue = field.get(eventEntity);

                // Special handling for timestamp fields to convert to event timezone
                if (newValue instanceof Timestamp && AuditHelper.isTimestampField(field.getName()) && timezone != null) {
                    // Convert the timestamp to the event's timezone for audit display
                    newValue = AuditHelper.convertTimestampToLocalTime((Timestamp) newValue, timezone);

                    // Skip comparison if the field wasn't actually changed
                    if (oldValues.containsKey(field.getName() + "_original")) {
                        Timestamp oldTimestamp = (Timestamp) oldValues.get(field.getName() + "_original");
                        Timestamp newTimestamp = (Timestamp) field.get(eventEntity);

                        if (oldTimestamp != null && newTimestamp != null && oldTimestamp.equals(newTimestamp)) {
                            continue; // Skip this field as it hasn't actually changed
                        }
                    }
                }

                if (!Objects.equals(oldValue, newValue)) {
                    String displayName = field.isAnnotationPresent(AuditDisplayName.class)
                            ? field.getAnnotation(AuditDisplayName.class).value()
                            : field.getName();

                    AuditEntity log = new AuditEntity();
                    log.setFieldName(displayName);
                    log.setGuid(eventEntity.getGuid());
                    log.setOldValue(oldValue != null ? oldValue.toString() : null);
                    log.setNewValue(newValue != null ? newValue.toString() : null);
                    log.setChangedBy(eventEntity.getLastModifierName());
                    log.setChangedAt(new Date());
                    auditEntries.add(log);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Error accessing field values", e);
            }
        }

        if (!auditEntries.isEmpty()) {
            auditRepository.saveAll(auditEntries);
        }
    }
}



