package com.rb.capability.auctionmanagement.client;

import com.rb.capability.auctionmanagement.client.exception.SalesforceAuthenticationException;
import com.rb.capability.auctionmanagement.domain.enums.Reason;
import com.rb.capability.auctionmanagement.model.SalesforceToken;
import com.rb.capability.auctionmanagement.resource.request.SalesforceEventRequest;
import com.rb.capability.auctionmanagement.resource.response.SalesforceEventResponse;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class SalesforceServiceClient {
    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String CREATE_EVENT_ENDPOINT = "/apexrest/v1/EventHub/Event";
    private static final String AUTH_ENDPOINT = "/oauth2/token";

    private final String baseUrl;
    private final String userName;
    private final String password;
    private final String clientId;
    private final String clientSecret;

    private final WebClient salesforceWebClient;
    private SalesforceToken token;

    private static final int RETRY_MAX_ATTEMPTS = 2;
    private final Map<String, AtomicInteger> retryStatusCounts = new ConcurrentHashMap<>();

    public SalesforceServiceClient(@Qualifier("salesforceWebClient") WebClient salesforceWebClient,
                                 @Value("${salesforce.base_url}") String baseUrl,
                                 @Value("${salesforce.username}") String userName,
                                 @Value("${salesforce.password}") String password,
                                 @Value("${salesforce.client_id}") String clientId,
                                 @Value("${salesforce.client_secret}") String clientSecret) {
        this.salesforceWebClient = salesforceWebClient;
        this.baseUrl = baseUrl;
        this.userName = userName;
        this.password = password;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
    }

    public String getSfdcToken() {
        if (Objects.isNull(token) || token.needRefresh()) {
            String endpoint = baseUrl + AUTH_ENDPOINT;
            token = salesforceWebClient.post()
                .uri(endpoint, uri -> uri.queryParam("username", userName)
                    .queryParam("password", password)
                    .queryParam("client_id", clientId)
                    .queryParam("client_secret", clientSecret)
                    .queryParam("grant_type", "password").build()
                )
                .retrieve()
                .bodyToMono(SalesforceToken.class)
                .doOnError(exception -> log.error("Can not get Salesforce token", exception))
                .block();
        }
        return "Bearer " + token.accessToken();
    }

    public SalesforceEventResponse createEvent(SalesforceEventRequest event) {
        String endpoint = baseUrl + CREATE_EVENT_ENDPOINT;
        return salesforceWebClient.post()
                .uri(endpoint)
                .header(AUTHORIZATION_HEADER, getSfdcToken())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(event)
                .retrieve()
                .bodyToMono(SalesforceEventResponse.class)
                .doOnError(exception -> {
                    Span currentSpan = Span.current();
                    if (exception instanceof WebClientResponseException webEx) {
                        handleWebClientResponseException(webEx, currentSpan);
                    } else {
                        handleGenericException(exception, currentSpan);
                    }
                    currentSpan.recordException(exception);
                })
                .retryWhen(retryWhen5xxServerError())
                .block();
    }

    private void handleWebClientResponseException(WebClientResponseException webEx, Span span) {
        int statusCode = webEx.getStatusCode().value();
        String errorDetails = webEx.getResponseBodyAsString();

        span.setAttribute("app.salesforce.status_code", statusCode);
        span.setAttribute("app.salesforce.error_details", errorDetails);

        if (statusCode == HttpStatus.FORBIDDEN.value()) {
            log.error("Authentication failed with Salesforce. Status: {}, Details: {}", HttpStatus.FORBIDDEN, errorDetails);
            span.setAttribute("app.salesforce.auth_error", "true");
            throw new SalesforceAuthenticationException(Reason.UNAUTHENTICATED, webEx.getMessage());
        } else if (statusCode == HttpStatus.BAD_REQUEST.value()) {
            log.error("Invalid request to Salesforce. Status: {}, Details: {}", statusCode, errorDetails);
            span.setAttribute("app.salesforce.validation_error", errorDetails);
            throw new BusinessException("Invalid Salesforce request: " + errorDetails);
        } else if (statusCode >= HttpStatus.INTERNAL_SERVER_ERROR.value()) {
            log.error("Salesforce server error. Status: {}, Details: {}", statusCode, errorDetails);
            span.setAttribute("app.salesforce.server_error", errorDetails);
            throw new WebClientResponseException(
                statusCode,
                "Salesforce server error",
                webEx.getHeaders(),
                webEx.getResponseBodyAsByteArray(),
                null
            );
        }

        log.error("Cannot create event in Salesforce. Error details: {}", errorDetails, webEx);
        span.setAttribute("app.salesforce.unhandled_error", "true");
    }

    private void handleGenericException(Throwable exception, Span span) {
        log.error("Cannot create event in Salesforce", exception);
        span.setAttribute("app.salesforce.unexpected_error", "true");
        throw new BusinessException("Cannot create event in Salesforce", exception);
    }

    private Retry retryWhen5xxServerError() {
        return Retry.fixedDelay(RETRY_MAX_ATTEMPTS, Duration.ofSeconds(3))
                .filter(throwable -> {
                    if (throwable instanceof WebClientResponseException webEx) {
                        boolean is5xxError = webEx.getStatusCode().is5xxServerError();
                        if (is5xxError) {
                            String statusCode = String.valueOf(webEx.getStatusCode().value());
                            retryStatusCounts.computeIfAbsent(statusCode, k -> new AtomicInteger(0))
                                    .incrementAndGet();
                            log.warn("Retrying Salesforce request due to 5xx error: {}", webEx.getStatusCode());
                        }
                        return is5xxError;
                    }
                    log.info("Not retrying non-WebClientResponseException: {}", throwable.getClass().getSimpleName());
                    return false;
                })
                .doBeforeRetry(retrySignal -> {
                    WebClientResponseException webEx = (WebClientResponseException) retrySignal.failure();

                    // Use 3s for first retry, 6s for second retry
                    long delaySeconds = retrySignal.totalRetries() == 0 ? 3 : 6;

                    log.warn("Retrying Salesforce request. Attempt {}/{}, Status Code: {}, Delay: {}s",
                            retrySignal.totalRetries() + 1,
                            RETRY_MAX_ATTEMPTS,
                            webEx.getStatusCode(),
                            delaySeconds);

                    Span currentSpan = Span.current();
                    currentSpan.setAttribute("app.salesforce.retry.attempt", retrySignal.totalRetries() + 1);
                    currentSpan.setAttribute("app.salesforce.retry.status_code", webEx.getStatusCode().value());
                    currentSpan.setAttribute("app.salesforce.retry.delay_seconds", delaySeconds);
                })
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    String errorMessage = String.format("Failed to process Salesforce request after %d attempts. Last error: %s",
                            RETRY_MAX_ATTEMPTS,
                            retrySignal.failure().getMessage());
                    log.error(errorMessage);
                    return new BusinessException(errorMessage, retrySignal.failure());
                });
    }
}




