package com.rb.capability.auctionmanagement.infra.jpa.entity;

import com.rb.capability.auctionmanagement.audit.AuditDisplayName;
import com.rb.capability.auctionmanagement.audit.NoAudit;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Map;

@Entity
@Table(name = "event")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @NoAudit
    private Long id;
    @Column(unique = true)
    @NoAudit
    private String guid;
    @AuditDisplayName("Location Id")
    private String locationId;
    @AuditDisplayName("Site Id")
    private String siteId;
    @AuditDisplayName("Sale Number")
    private String saleNumber;
    @AuditDisplayName("Source Auction Number")
    private String sourceAuctionNumber;
    @AuditDisplayName("Event Advertised Name")
    private String eventAdvertisedName;
    private String brand;
    @AuditDisplayName("Timezone")
    private String timezone;
    @AuditDisplayName("Event Start Date")
    private Timestamp eventStartDate;
    @AuditDisplayName("Event End Date")
    private Timestamp eventEndDate;
    @AuditDisplayName("Currency")
    private String currency;
    @AuditDisplayName("Primary Classification")
    private String primaryClassification;
    @Enumerated(EnumType.STRING)
    @AuditDisplayName("Scheduling Status")
    private SchedulingStatus schedulingStatus;
    @AuditDisplayName("Auction Format")
    private String sellingFormats;
    @AuditDisplayName("Ways To Participate")
    private String waysToParticipate;

    @Builder.Default
    @AuditDisplayName("Registration Open")
    private Boolean isRegistrationOpen = false;
    @AuditDisplayName("Registration Auto Open Date")
    private Timestamp registrationAutoOpenDate;

    @Builder.Default
    @AuditDisplayName("TAL Bid Open")
    private Boolean isTalBidOpen = false;
    @AuditDisplayName("TAL Bid Auto Open Date")
    private Timestamp talBidAutoOpenDate;

    @Builder.Default
    @AuditDisplayName("Priority Bid Open")
    private Boolean isPriorityBidOpen = false;
    @AuditDisplayName("Priority Bid Auto Open Date")
    private Timestamp priorityBidAutoOpenDate;

    @Builder.Default
    @AuditDisplayName("Olr Enabled")
    private Boolean isOlrEnabled = false;

    @CreationTimestamp
    @NoAudit
    private Timestamp createdDate;
    @NoAudit
    private String lastModifierName;
    @UpdateTimestamp
    @NoAudit
    private Timestamp lastModifiedDate;

    @Builder.Default
    @AuditDisplayName("Rb Marketplace Sale")
    private Boolean isRbMarketplaceSale = false;
    @AuditDisplayName("Name")
    private String name;
    @AuditDisplayName("Status")
    private String status;
    @AuditDisplayName("Business Unit Code")
    private String businessUnitCode;
    @AuditDisplayName("Business Unit Name")
    private String businessUnitName;
    @AuditDisplayName("Oracle Project Id")
    private Integer oracleProjectId;
    @AuditDisplayName("Legal Entity Id")
    private String legalEntityId;
    @AuditDisplayName("Allow Ip Inspection")
    private Boolean allowIpInspection;
    @AuditDisplayName("Additional Classification")
    private String additionalClassification;
    @Enumerated(EnumType.STRING)
    @AuditDisplayName("Location Type")
    private EventLocationType locationType;
    @NoAudit
    private String createdBy;
    @AuditDisplayName("Private Treaty")
    private Boolean isPrivateTreaty;
    @AuditDisplayName("Catalog Export Date")
    private Timestamp catalogExportDate;
    @AuditDisplayName("Event Site Configuration")
    private String siteConfiguration;
    private String notes;
    @Transient
    private Map<String, Object> oldValues;
    private Timestamp initialAuctionSyncDate;
}
