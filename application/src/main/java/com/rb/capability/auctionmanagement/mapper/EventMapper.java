package com.rb.capability.auctionmanagement.mapper;

import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Mapper(imports = {UUID.class, Timestamp.class, System.class, ZoneId.class, ZonedDateTime.class, LocalDate.class, LocalTime.class})
public interface EventMapper {
    EventMapper INSTANCE = Mappers.getMapper(EventMapper.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "saleNumber", ignore = true)
    @Mapping(source = "registrationOpen", target = "isRegistrationOpen")
    @Mapping(source = "olrEnabled", target = "isOlrEnabled")
    @Mapping(source = "waysToParticipate", target = "waysToParticipate", qualifiedByName = "participationMethodsToString")
    @Mapping(source = "auctionFormats", target = "sellingFormats", qualifiedByName = "sellingFormatsToString")
    @Mapping(source = "currency", target = "currency", qualifiedByName = "currencyListToString")
    @Mapping(source = "name", target = "name")
    @Mapping(target = "eventStartDate", expression = "java(convertToStartTimeInUtc(request.startDateTime(), request.timezone()))")
    @Mapping(target = "eventEndDate", expression = "java(convertToEndTimeInUtc(request.endDateTime(), request.timezone()))")
    @Mapping(source = "additionalClassification", target = "additionalClassification", qualifiedByName = "additionalClassificationsToString")
    @Mapping(source = "privateTreaty", target = "isPrivateTreaty")
    @Mapping(source = "legalEntityId", target = "legalEntityId")
    @Mapping(source = "businessUnit.code", target = "businessUnitCode")
    @Mapping(source = "businessUnit.name", target = "businessUnitName")
    @Mapping(source = "auctionAdvertisedName", target = "eventAdvertisedName")
    @Mapping(source = "brand", target = "brand")
    @Mapping(source = "allowIpInspection", target = "allowIpInspection")
    @Mapping(source = "rbMarketplaceAuction", target = "isRbMarketplaceSale")
    @Mapping(source = "sourceAuctionNumber", target = "sourceAuctionNumber")
    @Mapping(source = "doNotUse", target = "doNotUse")
    EventEntity mapCommonFields(EventRequest request);


    @Mapping(target = "guid", expression = "java(UUID.randomUUID().toString().toUpperCase())")
    @Mapping(target = "createdDate", expression = "java(new Timestamp(System.currentTimeMillis()))")
    @Mapping(target = "eventStartDate", expression = "java(convertToStartTimeInUtc(request.startDateTime(), request.timezone()))")
    @Mapping(target = "eventEndDate", expression = "java(convertToEndTimeInUtc(request.endDateTime(), request.timezone()))")
    @Mapping(target = "lastModifierName", ignore = true)
    @Mapping(target = "lastModifiedDate", ignore = true)
    @InheritConfiguration(name = "mapCommonFields")
    EventEntity toEntity(EventRequest request);


    @Mapping(target = "guid", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "sourceAuctionNumber", ignore = true)
    @Mapping(source = "talBidOpen", target = "isTalBidOpen")
    @Mapping(source = "priorityBidOpen", target = "isPriorityBidOpen")
    @Mapping(target = "lastModifiedDate", expression = "java(new Timestamp(System.currentTimeMillis()))")
    @Mapping(target = "priorityBidAutoOpenDate", expression = "java(convertToCurrentTime(request.priorityBidAutoOpenDate(), request.priorityBidOpen(), request.timezone()))")
    @Mapping(target = "talBidAutoOpenDate", expression = "java(convertToCurrentTime(request.talBidAutoOpenDate(), request.talBidOpen(), request.timezone()))")
    @Mapping(target = "registrationAutoOpenDate", expression = "java(convertToCurrentTime(request.registrationAutoOpenDate(), request.registrationOpen(), request.timezone()))")
    @InheritConfiguration(name = "mapCommonFields")
    void updateEntityFromRequest(EventRequest request,  @MappingTarget EventEntity entity);

    @Named("participationMethodsToString")
    default String participationMethodsToString(List<ParticipationMethod> methods) {
        if (methods == null || methods.isEmpty()) {
            return null;
        }
        return methods.stream()
                .map(ParticipationMethod::name)
                .collect(Collectors.joining(","));
    }

    @Named("sellingFormatsToString")
    default String sellingFormatsToString(List<SellingFormat> formats) {
        if (formats == null || formats.isEmpty()) {
            return null;
        }
        return formats.stream()
                .map(SellingFormat::name)
                .collect(Collectors.joining(","));
    }

    @Named("currencyListToString")
    default String currencyListToString(List<String> currencies) {
        if (currencies == null || currencies.isEmpty()) {
            return null;
        }
        return String.join(",", currencies);
    }

    default Timestamp convertToStartTimeInUtc(String dateStr, String timezone) {
        if (dateStr == null || timezone == null) {
            return null;
        }
        try {
            LocalDate localDate = LocalDate.parse(dateStr);
            ZonedDateTime startTimeInTimezone = localDate.atTime(LocalTime.of(8, 0))
                    .atZone(ZoneId.of(timezone))
                    .withNano(0); // Truncate to seconds, removing milliseconds
            return Timestamp.from(startTimeInTimezone.toInstant());
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date format: " + dateStr, e);
        }
    }

    default Timestamp convertToEndTimeInUtc(String dateStr, String timezone) {
        if (dateStr == null || timezone == null) {
            return null;
        }

        try {
            LocalDate localDate = LocalDate.parse(dateStr);
            ZonedDateTime endTimeInTimezone = localDate.atTime(LocalTime.of(19, 0))
                    .atZone(ZoneId.of(timezone))
                    .withNano(0); // Truncate to seconds, removing milliseconds
            return Timestamp.from(endTimeInTimezone.toInstant());
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date format: " + dateStr, e);
        }
    }

    @Named("additionalClassificationsToString")
    default String additionalClassificationsToString(List<String> classifications) {
        if (classifications == null || classifications.isEmpty()) {
            return null;
        }
        return String.join(",", classifications);
    }

    default Timestamp convertToCurrentTime(Timestamp dateStr, Boolean flag, String timezone) {
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(timezone))
                .withNano(0); // Truncate to seconds, removing milliseconds
        Timestamp timestamp = Timestamp.from(zonedDateTime.toInstant());
        if (flag != null && flag
                && (dateStr == null || dateStr.getTime() == 0)) {
            return timestamp;
        } else {
            return dateStr != null ? dateStr : null;
        }
    }
}
