package com.rb.capability.auctionmanagement.domain.enums;

public enum SchedulingStatus {
    Proposed("PROPOSED"),
    Confirmed("CONFIRMED"),
    Published("PUBLISHED"),
    Cancelled("CANCELLED"),
    Closed("CLOSED");
    public final String enterpriseSchedulingStatus;

    SchedulingStatus(String enterpriseSchedulingStatus) {
        this.enterpriseSchedulingStatus = enterpriseSchedulingStatus;
    }


    public static SchedulingStatus getSchedulingStatusFromEnterpriseMessage(String status) {
        if (status == null || status.isEmpty()) {
            return null;
        }
        return switch (status) {
            case "PROPOSED" -> Proposed;
            case "CONFIRMED" -> Confirmed;
            case "PUBLISHED" -> Published;
            case "CANCELLED" -> Cancelled;
            case "CLOSED" -> Closed;
            default -> null;
        };
    }
}
