package com.rb.capability.auctionmanagement.mapper;

import com.rb.capability.auctionmanagement.resource.response.LocationResponse;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.resource.response.SiteResponse;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.resource.response.BusinessUnit;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.common.utils.EventDateUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface EventDetailsResponseMapper {
    EventDetailsResponseMapper INSTANCE = Mappers.getMapper(EventDetailsResponseMapper.class);

    @Mapping(source = "guid", target = "eventGuid")
    @Mapping(source = "isRegistrationOpen", target = "registrationOpen")
    @Mapping(source = "isTalBidOpen", target = "talBidOpen")
    @Mapping(source = "isPriorityBidOpen", target = "priorityBidOpen")
    @Mapping(source = "isOlrEnabled", target = "olrEnabled")
    @Mapping(source = "currency", target = "currency", qualifiedByName = "stringToCurrencyList")
    @Mapping(source = "sellingFormats", target = "sellingFormats", qualifiedByName = "stringToSellingFormats")
    @Mapping(source = "waysToParticipate", target = "waysToParticipate", qualifiedByName = "stringToParticipationMethods")
    @Mapping(source = "locationId", target = "location", qualifiedByName = "mapLocation")
    @Mapping(source = "siteId", target = "site", qualifiedByName = "mapSite")
    @Mapping(source = "entity", target = "businessUnit", qualifiedByName = "mapBusinessUnit")
    @Mapping(source = "entity", target = "numberOfDays", qualifiedByName = "mapNumberOfDays")
    @Mapping(source = "additionalClassification", target = "additionalClassification", qualifiedByName = "stringToAdditionalClassification")
    @Mapping(source = "isPrivateTreaty", target = "privateTreaty")
    @Mapping(source = "catalogExportDate", target = "eventFinalizedDate")
    EventDetailsResponse toResponse(EventEntity entity, @Context PlacesServiceClient placesServiceClient);

    // for batch processing
    @Mapping(source = "guid", target = "eventGuid")
    @Mapping(source = "isRegistrationOpen", target = "registrationOpen")
    @Mapping(source = "isTalBidOpen", target = "talBidOpen")
    @Mapping(source = "isPriorityBidOpen", target = "priorityBidOpen")
    @Mapping(source = "isOlrEnabled", target = "olrEnabled")
    @Mapping(source = "currency", target = "currency", qualifiedByName = "stringToCurrencyList")
    @Mapping(source = "sellingFormats", target = "sellingFormats", qualifiedByName = "stringToSellingFormats")
    @Mapping(source = "waysToParticipate", target = "waysToParticipate", qualifiedByName = "stringToParticipationMethods")
    @Mapping(source = "locationId", target = "location", qualifiedByName = "mapLocationFromMap")
    @Mapping(source = "siteId", target = "site", qualifiedByName = "mapSiteFromMap")
    @Mapping(source = "entity", target = "businessUnit", qualifiedByName = "mapBusinessUnit")
    @Mapping(source = "entity", target = "numberOfDays", qualifiedByName = "mapNumberOfDays")
    @Mapping(source = "additionalClassification", target = "additionalClassification", qualifiedByName = "stringToAdditionalClassification")
    @Mapping(source = "isPrivateTreaty", target = "privateTreaty")
    @Mapping(source = "catalogExportDate", target = "eventFinalizedDate")
    EventDetailsResponse toResponseBatch(EventEntity entity, @Context Map<String, LocationResponse> locationsMap, @Context Map<String, SiteResponse> sitesMap);

    @Named("stringToCurrencyList")
    default List<String> stringToCurrencyList(String currencyString) {
        if (currencyString == null || currencyString.isEmpty()) {
            return null;
        }
        return Arrays.asList(currencyString.split(","));
    }

    @Named("stringToSellingFormats")
    default List<SellingFormat> stringToSellingFormats(String formatsString) {
        if (formatsString == null || formatsString.isEmpty()) {
            return null;
        }
        return Arrays.stream(formatsString.split(","))
                .map(SellingFormat::valueOf)
                .collect(Collectors.toList());
    }

    @Named("stringToParticipationMethods")
    default List<ParticipationMethod> stringToParticipationMethods(String methodsString) {
        if (methodsString == null || methodsString.isEmpty()) {
            return null;
        }
        return ParticipationMethod.fromDbString(methodsString);
    }

    @Named("mapLocation")
    default LocationResponse mapLocation(String locationId, @Context PlacesServiceClient placesServiceClient) {
        LocationResponse locationResponse = placesServiceClient.getLocationByLocationGuid(locationId);
        return locationResponse;
    }

    @Named("mapSite")
    default SiteResponse mapSite(String siteId, @Context PlacesServiceClient placesServiceClient) {
        SiteResponse siteResponse = placesServiceClient.getSiteBySiteGuid(siteId);
        return siteResponse;
    }

    @Named("mapBusinessUnit")
    default BusinessUnit mapBusinessUnit(EventEntity entity) {
        if (entity.getBusinessUnitName() == null || entity.getBusinessUnitCode() == null) {
            return null;
        }
        return new BusinessUnit(entity.getBusinessUnitName(), entity.getBusinessUnitCode());
    }

    @Named("mapNumberOfDays")
    default Integer mapNumberOfDays(EventEntity entity) {
        return EventDateUtils.mapNumberOfDays(entity);

    }

    @Named("stringToAdditionalClassification")
    default List<String> stringToAdditionalClassification(String additionalClassificationString) {
        if (additionalClassificationString == null || additionalClassificationString.isEmpty()) {
            return null;
        }
        return Arrays.stream(additionalClassificationString.split(","))
                .collect(Collectors.toList());
    }

    @Named("mapLocationFromMap")
    default LocationResponse mapLocationFromMap(String locationId, @Context Map<String, LocationResponse> locationsMap) {
        return locationsMap.get(locationId);
    }

    @Named("mapSiteFromMap")
    default SiteResponse mapSiteFromMap(String siteId, @Context Map<String, SiteResponse> sitesMap) {
        return sitesMap.get(siteId);
    }
}
