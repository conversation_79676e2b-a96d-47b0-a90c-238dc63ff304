package com.rb.capability.auctionmanagement.resource.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;


@Builder
public record SiteResponse(
        @Schema(description = "Id", example = "123e4567-e89b-12d3-a456-426614174000")
        String siteId,

        @Schema(description = "Advertised Name", example = "ABC Location")
        String advertisedName,

        @Schema(description = "Name", example = "ABC Location")
        String name
) {}