package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.request.CreateEventRequest;
import com.rb.capability.auctionmanagement.resource.response.CheckEventResponse;
import com.rb.capability.auctionmanagement.resource.response.CreateEventResponse;
import com.rb.capability.auctionmanagement.service.TestAutomationService;
import com.rb.capability.common.BasicUtils;
import com.rb.capability.common.utils.LogSanitizer;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "Events", description = "Auction Management Event APIs")
@ExtensionMethod(LogSanitizer.class)
public class TestAutomationController {

    private final TestAutomationService testAutomationService;

    @WithSpan("check-saleNumber-exists")
    @GetMapping("/events/{sale_number}/check")
    @Operation(summary = "Check event availability",
            description = "Checks if an event with the given sale number exists")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully checked event availability"),
        @ApiResponse(responseCode = "400", description = "Invalid sale number format"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<CheckEventResponse> checkEventAvailability(@PathVariable("sale_number") String saleNumber) {
        log.info("Checking event availability for sale number: {}", saleNumber.sanitize());

        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_ACTION_NAME, "check_event_availability");
        currentSpan.setAttribute(APP_REQUEST, saleNumber);

        try {
            CheckEventResponse response = testAutomationService.checkSaleNumberAvailability(saleNumber);
            currentSpan.setAttribute(APP_RESPONSE, Objects.requireNonNull(BasicUtils.convertObjectToJsonString(response)));
            currentSpan.setAttribute(APP_STATUS, "SUCCESS");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error checking event availability for sale number: {}", saleNumber.sanitize(), e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            currentSpan.setAttribute(APP_STATUS, "ERROR");
            currentSpan.setAttribute(APP_ERROR, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    @WithSpan("clone-event")
    @PostMapping("/events/clone")
    @Operation(summary = "Clone existing event",
            description = "Creates a new event based on an existing sale number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully cloned event"),
        @ApiResponse(responseCode = "404", description = "Source event not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<CreateEventResponse> cloneEventForTestAutomation(@RequestBody CreateEventRequest request) {
        log.info("Cloning event with tdi tool request: {}", BasicUtils.convertObjectToJsonString(request).sanitize());

        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_ACTION_NAME, "clone_event");
        currentSpan.setAttribute(APP_REQUEST, Objects.requireNonNull(BasicUtils.convertObjectToJsonString(request)));

        try {
            CreateEventResponse response = testAutomationService.cloneEventForTestAutomation(request);
            currentSpan.setAttribute(APP_RESPONSE, Objects.requireNonNull(BasicUtils.convertObjectToJsonString(response)));
            currentSpan.setAttribute(APP_STATUS, "SUCCESS");
            return ResponseEntity.ok(response);
        } catch (BusinessException e) {
            log.error("Error cloning event for tdi tool request: {}", BasicUtils.convertObjectToJsonString(request).sanitize(), e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            currentSpan.setAttribute(APP_STATUS, "ERROR");
            currentSpan.setAttribute(APP_ERROR, e.getMessage());
            throw e;
        }
    }
}
