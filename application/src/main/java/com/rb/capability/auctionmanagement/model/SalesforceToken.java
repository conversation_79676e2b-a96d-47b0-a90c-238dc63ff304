package com.rb.capability.auctionmanagement.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.rb.capability.common.ExcludeFromJacocoGeneratedReport;

import java.time.Duration;
import java.time.Instant;

@ExcludeFromJacocoGeneratedReport
public record SalesforceToken(
    @JsonProperty("access_token")
    String accessToken,

    @JsonProperty("instance_url")
    String instanceUrl,

    @JsonProperty("id")
    String id,

    @JsonProperty("token_type")
    String tokenType,

    @JsonProperty("issued_at")
    String issuedAt,

    @JsonProperty("signature")
    String signature
) {
    public static final int TOKEN_EXPIRE_IN_MINUTE = 59;

    public boolean needRefresh() {
        return Duration.between(
            Instant.ofEpochMilli(Long.parseLong(issuedAt)),
            Instant.now()
        ).toMinutes() > TOKEN_EXPIRE_IN_MINUTE;
    }
}
