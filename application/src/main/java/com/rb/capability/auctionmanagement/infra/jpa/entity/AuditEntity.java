package com.rb.capability.auctionmanagement.infra.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Entity
@Table(name = "audit")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String guid;
    private String fieldName;
    private String oldValue;
    private String newValue;
    private String changedBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Builder.Default
    private Date changedAt = new Date();

}
