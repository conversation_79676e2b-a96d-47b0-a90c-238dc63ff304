package com.rb.capability.auctionmanagement.client;

public enum EventLocationType {
    PERMANENT("PERMANENT_SITE", "Permanent Site"),
    OFFSITE("OFF_SITE", "Off-Site"),
    ON_THE_FARM("ON_THE_FARM", "On the Farm"),
    REGIONAL_AUCTION_SITE("REGIONAL_AUCTION_SITE", "Regional Auction Site"),
    VIRTUAL("VIRTUAL", "Virtual"),
    SATELLITE("SATELLITE", "Satellite"),
    ONLINE_WEB_BASED("ON_LINE_WEB_BASED", "On-line Web-based"),
    UNKNOWN("OTHER", "Other");

    public final String enterpriseEventLocationType;
    public final String description;


    EventLocationType(String enterpriseEventLocationType, String salesForceLocationType) {
        this.enterpriseEventLocationType = enterpriseEventLocationType;
        this.description = salesForceLocationType;
    }


    public static EventLocationType getSiteLocationTypeByEnterpriseEventLocationType(String enterpriseEventLocationType) {
        for (EventLocationType useType : values()) {
            if (useType.enterpriseEventLocationType.equals(enterpriseEventLocationType)) {
                return useType;
            }
        }
        return UNKNOWN;
    }
}