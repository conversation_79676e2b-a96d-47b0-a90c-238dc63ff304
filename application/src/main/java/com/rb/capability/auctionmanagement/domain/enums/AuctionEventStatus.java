package com.rb.capability.auctionmanagement.domain.enums;

public enum AuctionEventStatus {
    New("NEW"),
    Configuring("NEW"),
    Sequencing("NEW"),
    LotNumbering("LOT_ASSIGNMENT"),
    Finalized("GONE_TO_CATALOG"),
    Active("STARTED"),
    Closed("CLOSED");

    private final String operationStatus;

    AuctionEventStatus(String operationStatus) {
        this.operationStatus = operationStatus;
    }

    public String getOperationStatus() {
        return operationStatus;
    }

    public static AuctionEventStatus findByStatusName(String status) {
        if (status == null) {
            return New;
        }

        try {
            return valueOf(status);
        } catch (IllegalArgumentException e) {
            return New;
        }
    }

    public static AuctionEventStatus findByOperationStatus(String operationStatus) {
        if (operationStatus == null) {
            return New;
        }

        for (AuctionEventStatus status : values()) {
            if (status.operationStatus.equals(operationStatus)) {
                return status;
            }
        }
        return New;
    }
}