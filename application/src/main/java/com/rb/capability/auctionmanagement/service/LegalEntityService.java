package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.LegalEntityServiceClient;
import com.rb.capability.common.ExcludeFromJacocoGeneratedReport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
@ExcludeFromJacocoGeneratedReport
public class LegalEntityService {

    private final LegalEntityServiceClient legalEntityServiceClient;

    public String getBusinessNameFromLegalEntityId(String legalEntityId) {
        return legalEntityServiceClient.getLegalEntities().stream()
                .filter(entity -> entity.profile().id().equals(legalEntityId))
                .map(entity -> entity.profile().businessName())
                .findFirst()
                .orElse(null);
    }
}
