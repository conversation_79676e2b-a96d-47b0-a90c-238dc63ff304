package com.rb.capability.auctionmanagement.client;

import com.rb.capability.auctionmanagement.client.exception.ControllerErrorResponseHandler;
import com.rb.capability.auctionmanagement.model.AddressDto;
import com.rb.capability.auctionmanagement.model.ContactDto;
import com.rb.capability.auctionmanagement.model.CreateLocationRequest;
import com.rb.capability.auctionmanagement.model.Location;
import com.rb.capability.auctionmanagement.resource.request.BatchLocationRequest;
import com.rb.capability.auctionmanagement.resource.request.BatchSiteRequest;
import com.rb.capability.auctionmanagement.resource.response.LocationResponse;
import com.rb.capability.auctionmanagement.resource.response.SiteResponse;
import com.rbauction.enterprise.events.models.address.Address;
import com.rbauction.enterprise.events.models.sale.EventLocation;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.APP_EXTERNAL_EXCEPTION;


@Slf4j
@Component
public class PlacesServiceClient {
    private static final int MAX_BATCH_SIZE = 100;
    private final WebClient placesWebClient;
    private final String baseUrl;

    public PlacesServiceClient(@Qualifier("placesWebClient") WebClient placesWebClient, @Value("${places_api.base_url}") String baseUrl) {
        this.placesWebClient = placesWebClient;
        this.baseUrl = baseUrl;
    }

    public String createLocation(EventLocation eventLocation) {
        CreateLocationRequest createRequest = buildCreateLocationRequest(eventLocation);
        String endpoint = baseUrl + "/locations";
        return placesWebClient.post()
                .uri(endpoint)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(createRequest)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(Exception.class, e -> {
                    log.error("Failed to create location", e);
                    if (e instanceof WebClientResponseException ex) {
                        log.error("Response body: {}", ex.getResponseBodyAsString());
                        throw new ResponseStatusException(ex.getStatusCode(),
                                ControllerErrorResponseHandler.extractErrorDetails(ex));
                    }
                    throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR);
                })
                .block();
    }

    private CreateLocationRequest buildCreateLocationRequest(EventLocation eventLocation) {
        Address address = eventLocation.getAddress();
        LocationType type = LocationType.UNKNOWN;
        if (eventLocation.getType() != null) {
            type = eventLocation.getType().equals("OFF_SITE") || eventLocation.getType().equals("ON_THE_FARM") ? LocationType.RB_OFFSITE : LocationType.RB;
        }

        AddressDto addressDto = AddressDto.builder()
                .address(address.getStreetAddress1())
                .city(address.getLocality())
                .provinceStateCode(address.getRegion())
                .provinceState(address.getRegion())
                .countryCode(address.getCountry())
                .country(address.getCountry())
                .postalCode(address.getPostalCode())
                .latitude(address.getLatitude() != null ? Double.parseDouble(address.getLatitude()) : 0.0)
                .longitude(address.getLongitude() != null ? Double.parseDouble(address.getLongitude()) : 0.0)
                .build();
        ContactDto contactDto = null;
        if (eventLocation.getPhoneNumber() != null || eventLocation.getFaxNumber() != null) {
            contactDto = ContactDto.builder()
                    .cellPhoneNumber(eventLocation.getPhoneNumber())
                    .faxNumber(eventLocation.getFaxNumber())
                    .build();
        }

        Location locationDto = Location.builder()
                .name(StringUtils.isBlank(eventLocation.getName()) ? eventLocation.getName() : null)
                .type(type)
                .status(Status.ACTIVE)
                .address(addressDto)
                .locationContact(contactDto)
                .ownerId("1")
                .build();

        return CreateLocationRequest.builder()
                .location(locationDto)
                .caller("AuctionManagementService")
                .build();
    }

    public LocationResponse getLocationByLocationGuid(String locationId) {
        String endpoint = baseUrl + "/locations/{location_id}";
        return placesWebClient.get()
                .uri(endpoint, locationId)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(LocationResponse.class)
                .onErrorResume(Exception.class, e -> {
                    log.error("Failed to retrieve location details", e);
                    Span.current().setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
                    if (e instanceof WebClientResponseException ex) {
                        log.error("Response body: {}", ex.getResponseBodyAsString());
                        return Mono.justOrEmpty(null);
                    }
                    return Mono.justOrEmpty(null);
                })
                .block();
    }

    public SiteResponse getSiteBySiteGuid(String siteGuid) {
        String endpoint = baseUrl + "/sites/{site_id}";
        return placesWebClient.get()
                .uri(endpoint, siteGuid)
                .accept(MediaType.APPLICATION_JSON)
                .retrieve()
                .bodyToMono(SiteResponse.class)
                .onErrorResume(Exception.class, e -> {
                    log.error("Failed retrieve get site details", e);
                    Span.current().setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
                    if (e instanceof WebClientResponseException ex) {
                        log.error("Response body: {}", ex.getResponseBodyAsString());
                        return Mono.justOrEmpty(null);
                    }
                    return Mono.justOrEmpty(null);
                })
                .block();
    }

    public Map<String, LocationResponse> getLocationsByLocationGuids(List<String> locationIds) {
        if (locationIds == null || locationIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, LocationResponse> resultMap = new HashMap<>();

        // Process in batches of 100
        for (int i = 0; i < locationIds.size(); i += MAX_BATCH_SIZE) {
            final int batchIndex = i;  // Create final copy for lambda
            List<String> batchIds = locationIds.subList(i, Math.min(i + MAX_BATCH_SIZE, locationIds.size()));
            String endpoint = baseUrl + "/locations/batch";

            Map<String, LocationResponse> batchResult = placesWebClient.post()
                    .uri(endpoint)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(new BatchLocationRequest(batchIds))
                    .accept(MediaType.APPLICATION_JSON)
                    .retrieve()
                    .bodyToMono(new ParameterizedTypeReference<Map<String, LocationResponse>>() {})
                    .onErrorResume(Exception.class, e -> {
                        log.error("Failed to retrieve locations details for batch starting at index {}", batchIndex, e);
                        Span.current().setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
                        return Mono.just(new HashMap<>());
                    })
                    .block();

            resultMap.putAll(batchResult);
        }

        return resultMap;
    }

    public Map<String, SiteResponse> getSitesBySiteGuids(List<String> siteIds) {
        if (siteIds == null || siteIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, SiteResponse> resultMap = new HashMap<>();

        // Process in batches of 100
        for (int i = 0; i < siteIds.size(); i += MAX_BATCH_SIZE) {
            final int batchIndex = i;  // Create final copy for lambda
            List<String> batchIds = siteIds.subList(i, Math.min(i + MAX_BATCH_SIZE, siteIds.size()));
            String endpoint = baseUrl + "/sites/batch";

            Map<String, SiteResponse> batchResult = placesWebClient.post()
                    .uri(endpoint)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(new BatchSiteRequest(batchIds))
                    .accept(MediaType.APPLICATION_JSON)
                    .retrieve()
                    .bodyToMono(new ParameterizedTypeReference<Map<String, SiteResponse>>() {})
                    .onErrorResume(Exception.class, e -> {
                        log.error("Failed to retrieve sites details for batch starting at index {}", batchIndex, e);
                        Span.current().setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
                        return Mono.just(new HashMap<>());
                    })
                    .block();

            resultMap.putAll(batchResult);
        }

        return resultMap;
    }

}
