package com.rb.capability.auctionmanagement.resource.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

public record EventSearchResponse(

        @Schema(description = "Event Unique Identifier", example = "8DAA475A-9873-AB65-B661-031450B104E0")
        @JsonProperty("auction_id")
        String eventGuid,
        @Schema(description = "A unique number assigned to the Sale Event", example = "2025123")
        String saleNumber,
        @Schema(name = "Name of the Sale Event", example = "Test Name")
        String name,
        @Schema(description = "Advertised Name of the Sale Event", example = "Test Advertised Name")
        String eventAdvertisedName
) {
    public static EventSearchResponse success(String guid, String saleNumber, String name, String eventAdvertisedName) {
        return new EventSearchResponse(guid, saleNumber, name, eventAdvertisedName);
    }
}