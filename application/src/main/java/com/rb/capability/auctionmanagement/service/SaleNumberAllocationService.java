package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.domain.enums.AuctionEventClassification;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.entity.SaleNumberAllocation;
import com.rb.capability.auctionmanagement.infra.jpa.repository.SaleNumberAllocationRepository;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.essentials.capability.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SaleNumberAllocationService {

    private static final String TYPE_CHARITY = "Charity";
    private static final String TYPE_AGRICULTURE = "Agriculture";
    private static final String TYPE_DEFAULT = "Non Agriculture Non Charity";

    private final SaleNumberAllocationRepository saleNumberAllocationRepository;

    public boolean isSaleNumberUsed(Integer saleNumber) {
        return saleNumberAllocationRepository.findBySaleNumberAndInUseTrue(saleNumber).isPresent();
    }

    public Integer generateSaleNumber(int year, EventRequest eventRequest) {
        String type = determineEventType(eventRequest);
        Integer nextSaleNumber = saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, type);

        if (nextSaleNumber == null) {
            throw new BusinessException("No sale numbers available for the specified year and type.");
        }

        return nextSaleNumber;
    }

    public void markSaleNumberInUse(EventEntity eventEntity) {
        String saleNumber = eventEntity.getSaleNumber();
        int year = Integer.parseInt(saleNumber.substring(0, 4));
        Integer saleNumberInt = Integer.parseInt(saleNumber);
        String eventType = determineEventType(eventEntity);
        
        Optional<SaleNumberAllocation> existingAllocation = saleNumberAllocationRepository.findBySaleNumber(saleNumberInt);
        
        if (existingAllocation.isPresent()) {
            updateExistingAllocation(existingAllocation.get(), eventType);
        } else {
            createNewAllocation(saleNumberInt, year, eventType);
        }
    }

    private void updateExistingAllocation(SaleNumberAllocation allocation, String eventType) {
        allocation.setInUse(true);
        allocation.setType(eventType);
        saleNumberAllocationRepository.save(allocation);
    }

    private void createNewAllocation(Integer saleNumber, int year, String eventType) {
        SaleNumberAllocation allocation = SaleNumberAllocation.builder()
                .saleNumber(saleNumber)
                .inUse(true)
                .year(year)
                .type(eventType)
                .build();

        saleNumberAllocationRepository.save(allocation);
    }

    private String determineEventType(EventRequest eventRequest) {
        List<String> classifications = extractClassifications(eventRequest);
        return getEventTypeFromClassifications(classifications);
    }

    private String determineEventType(EventEntity eventEntity) {
        List<String> classifications = extractClassifications(eventEntity);
        return getEventTypeFromClassifications(classifications);
    }

    private String getEventTypeFromClassifications(List<String> classifications) {
        boolean isCharity = AuctionEventClassification.isCharity(classifications);
        boolean isAgriculture = AuctionEventClassification.isAgriculture(classifications);

        if (isCharity) {
            return TYPE_CHARITY;
        } else if (isAgriculture) {
            return TYPE_AGRICULTURE;
        } else {
            return TYPE_DEFAULT;
        }
    }

    private List<String> extractClassifications(EventRequest eventRequest) {
        List<String> classifications = new ArrayList<>();

        Optional.ofNullable(eventRequest.primaryClassification())
                .ifPresent(classifications::add);

        Optional.ofNullable(eventRequest.additionalClassification())
                .map(list -> list.stream()
                        .filter(s -> !s.isEmpty())
                        .map(s -> s.split(","))
                        .flatMap(Arrays::stream)
                        .collect(Collectors.toList()))
                .ifPresent(classifications::addAll);

        return classifications;
    }

    private List<String> extractClassifications(EventEntity eventEntity) {
        List<String> classifications = new ArrayList<>();

        Optional.ofNullable(eventEntity.getPrimaryClassification())
                .ifPresent(classifications::add);

        Optional.ofNullable(eventEntity.getAdditionalClassification())
                .filter(s -> !s.isEmpty())
                .map(s -> s.split(","))
                .map(Arrays::stream)
                .ifPresent(stream -> stream.forEach(classifications::add));

        return classifications;
    }
}
