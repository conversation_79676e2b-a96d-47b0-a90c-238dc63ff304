package com.rb.capability.auctionmanagement.resource.response;

import com.rb.capability.auctionmanagement.client.ContactType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

@Builder(toBuilder = true)
public record ContactDetailsResponse(
        @Schema(description = "Contact Type", example = "Location")
        ContactType type,
        @Schema(description = "First Name", example = "<PERSON>")
        String firstName,
        @Schema(description = "Last Name", example = "Doe")
        String lastName,
        @Schema(description = "Company Name", example = "ABC Company")
        String companyName,
        @Schema(description = "Phone Number", example = "1234567890")
        String phoneNumber,
        @Schema(description = "Fax Number", example = "0987654321")
        String faxNumber,
        @Schema(description = "Email", example = "<EMAIL>")
        String email
) {
}
