package com.rb.capability.auctionmanagement.resource.response;

import io.swagger.v3.oas.annotations.media.Schema;

public record EventResponse(

        @Schema(description = "Event Unique Identifier", example = "8DAA475A-9873-AB65-B661-031450B104E0")
        String eventGuid,

        @Schema(description = "A unique number assigned to the Sale Event", example = "2025123")
        String saleNumber
) {
    public static EventResponse success(String guid, String saleNumber) {
        return new EventResponse(guid, saleNumber);
    }
}