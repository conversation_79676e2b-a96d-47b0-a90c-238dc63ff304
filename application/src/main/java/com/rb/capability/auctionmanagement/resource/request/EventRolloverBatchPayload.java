package com.rb.capability.auctionmanagement.resource.request;

import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Builder(toBuilder = true)
public record EventRolloverBatchPayload(

        @JsonProperty("auctions")
        List<EventRolloverRequest> auctions,

        @JsonProperty("createdBy")
        String createdBy

) {
    @Builder(toBuilder = true)
    @Schema(description = "Rollover event details")
    public static record EventRolloverRequest(

            String auctionId,

            Integer auctionNumber

    ) {
    }
}
