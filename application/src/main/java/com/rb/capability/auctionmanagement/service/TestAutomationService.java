package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.mapper.EventMapper;
import com.rb.capability.auctionmanagement.resource.request.CreateEventRequest;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.auctionmanagement.resource.response.*;
import com.rb.capability.common.BasicUtils;
import com.rb.capability.common.utils.LogSanitizer;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import lombok.RequiredArgsConstructor;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;

@Service
@Slf4j
@RequiredArgsConstructor
@ExtensionMethod(LogSanitizer.class)
public class TestAutomationService {

    private final EventRepository eventRepository;
    private final AsyncEventService asyncEventService;
    private final PlacesServiceClient placesServiceClient;
    private final SaleNumberAllocationService saleNumberAllocationService;

    public CheckEventResponse checkSaleNumberAvailability(String saleNumber) {
        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_ACTION_NAME, "check_sale_number_availability");
        currentSpan.setAttribute(APP_REQUEST, saleNumber);

        try {
            log.info("Checking event availability for sale number: {}", saleNumber.sanitize());
            boolean exists = saleNumberAllocationService.isSaleNumberUsed(Integer.parseInt(saleNumber));
            CheckEventResponse response = CheckEventResponse.builder()
                    .eventFound(exists)
                    .build();

            currentSpan.setAttribute(APP_STATUS, "SUCCESS");
            currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
            currentSpan.setAttribute(LOG_INFO, String.format("Event exists: %s for sale number: %s", exists, saleNumber));

            return response;
        } catch (Exception e) {
            log.error("Failed to check event availability for sale number: {}", saleNumber.sanitize(), e);
            currentSpan.setAttribute(APP_STATUS, "ERROR");
            currentSpan.setAttribute(APP_ERROR, e.getMessage());
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException("Failed to check event availability");
        }
    }

    @Transactional
    public CreateEventResponse cloneEventForTestAutomation(CreateEventRequest request) {
        Span currentSpan = Span.current();
        currentSpan.setAttribute(APP_ACTION_NAME, "clone_event_for_test_automation");
        currentSpan.setAttribute(APP_REQUEST, BasicUtils.convertObjectToJsonString(request));

        String cloneSaleNumber = "";
        try {
            if (request.eventParameters().isEmpty()) {
                throw new BusinessException("Event parameters are empty");
            }

            // Get source event details
            cloneSaleNumber = request.eventParameters().get(0).saleNumber();
            String finalCloneSaleNumber = cloneSaleNumber;

            log.info("Starting event cloning process for sale number: {}", cloneSaleNumber.sanitize());
            currentSpan.setAttribute(LOG_INFO, String.format("Cloning event with sale number: %s", cloneSaleNumber));

            return eventRepository.findBySaleNumber(cloneSaleNumber)
                    .map(sourceEvent -> {
                        List<CreateEventResponse.ClonedEvents> clonedEvents = new ArrayList<>();
                        currentSpan.setAttribute(EVENT_ID, sourceEvent.getGuid());

                        for (CreateEventRequest.EventParameters params : request.eventParameters()) {
                            log.info("Cloning event with parameters: {}", BasicUtils.convertObjectToJsonString(params).sanitize());

                            // Create new event entity from source using mapper
                            EventEntity newEvent = EventMapper.INSTANCE.toEntity(EventRequest.builder()
                                    .brand(sourceEvent.getBrand())
                                    .currency(List.of(sourceEvent.getCurrency().split(",")))
                                    .locationId(sourceEvent.getLocationId())
                                    .siteId(sourceEvent.getSiteId())
                                    .locationType(sourceEvent.getLocationType())
                                    .primaryClassification(sourceEvent.getPrimaryClassification())
                                    .additionalClassification(sourceEvent.getAdditionalClassification() != null
                                            ? Arrays.asList(sourceEvent.getAdditionalClassification().split(",")) : null)
                                    .timezone(sourceEvent.getTimezone())
                                    .schedulingStatus(SchedulingStatus.Confirmed.name())
                                    .startDateTime(params.eventStartDate())
                                    .endDateTime(params.eventEndDate())
                                    .auctionFormats(Arrays.stream(sourceEvent.getSellingFormats().split(","))
                                            .map(SellingFormat::valueOf)
                                            .collect(Collectors.toList()))
                                    .waysToParticipate(ParticipationMethod.fromDbString(sourceEvent.getWaysToParticipate()))
                                    .rbMarketplaceAuction(Boolean.TRUE)
                                    .olrEnabled(sourceEvent.getIsOlrEnabled())
                                    .registrationOpen(Boolean.FALSE)
                                    .build());

                            newEvent.setBusinessUnitCode(sourceEvent.getBusinessUnitCode());
                            newEvent.setBusinessUnitName(sourceEvent.getBusinessUnitName());
                            newEvent.setLegalEntityId(sourceEvent.getLegalEntityId());
                            newEvent.setSaleNumber(params.newSaleNumber());
                            newEvent.setGuid(UUID.randomUUID().toString());
                            newEvent.setStatus(sourceEvent.getStatus());
                            newEvent.setAllowIpInspection(sourceEvent.getAllowIpInspection() != null && sourceEvent.getAllowIpInspection());
                            newEvent.setSiteConfiguration(sourceEvent.getSiteConfiguration());
                            newEvent.setIsPrivateTreaty(sourceEvent.getIsPrivateTreaty() != null && sourceEvent.getIsPrivateTreaty());
                            newEvent.setInitialAuctionSyncDate(new java.sql.Timestamp(System.currentTimeMillis()));

                            // Generate and set event name
                            String eventName = generateEventName(newEvent);
                            newEvent.setName(eventName);
                            newEvent.setEventAdvertisedName(eventName);

                            currentSpan.setAttribute(LOCATION_ID, newEvent.getLocationId());
                            log.info("Saving cloned event with new sale number: {}", params.newSaleNumber().sanitize());

                            // Save the new event
                            EventEntity savedEvent = eventRepository.save(newEvent);
                            saleNumberAllocationService.markSaleNumberInUse(savedEvent);
                            // Process async operations
                            asyncEventService.processEventAsync(savedEvent);

                            // Create clone response
                            CreateEventResponse.ClonedEvents clonedEvent = CreateEventResponse.ClonedEvents.builder()
                                    .saleNumber(savedEvent.getSaleNumber())
                                    .id(savedEvent.getId().toString())
                                    .gid(savedEvent.getGuid())
                                    .eventStartDate(savedEvent.getEventStartDate().toString())
                                    .eventEndDate(savedEvent.getEventEndDate().toString())
                                    .build();

                            clonedEvents.add(clonedEvent);
                        }

                        if (clonedEvents.isEmpty()) {
                            throw new BusinessException("Failed to clone event");
                        }

                        CreateEventResponse response = CreateEventResponse.builder()
                                .requestId(request.requestId())
                                .clonedEvents(clonedEvents)
                                .build();

                        currentSpan.setAttribute(APP_STATUS, "SUCCESS");
                        currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
                        return response;
                    })
                    .orElseThrow(() -> new BusinessException(String.format("Source event not found with sale number: %s", finalCloneSaleNumber)));

        } catch (BusinessException e) {
            log.error("Business error while cloning event for sale number {}: {}", cloneSaleNumber.sanitize(), e.getMessage());
            currentSpan.setAttribute(APP_STATUS, "ERROR");
            currentSpan.setAttribute(APP_ERROR, e.getMessage());
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Failed to clone event for sale number {}", cloneSaleNumber.sanitize(), e);
            currentSpan.setAttribute(APP_STATUS, "ERROR");
            currentSpan.setAttribute(APP_ERROR, e.getMessage());
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException("Failed to clone event");
        }
    }

    private String generateEventName(EventEntity eventEntity) {
        if (eventEntity.getLocationId() == null) {
            log.info("Location is null, cannot generate event name.");
            throw new BusinessException("Location details not found");
        }

        LocalDate eventDate = eventEntity.getEventStartDate().toInstant()
                .atZone(ZoneId.of(eventEntity.getTimezone()))
                .toLocalDate();
        final String formattedDate = eventDate.format(DateTimeFormatter.ofPattern("MMM dd, yyyy"));

        StringBuilder nameBuilder = new StringBuilder();

        // Fetch location and site details
        LocationResponse location = placesServiceClient.getLocationByLocationGuid(eventEntity.getLocationId());
        SiteResponse site = eventEntity.getSiteId() != null ? placesServiceClient.getSiteBySiteGuid(eventEntity.getSiteId()) : null;

        if (eventEntity.getLocationType() == EventLocationType.PERMANENT) {
            // For permanent sites, use site name
            if (site != null) {
                nameBuilder.append(site.name());
            } else {
                nameBuilder.append(location.name());
            }
        } else if (eventEntity.getLocationType() == EventLocationType.OFFSITE
                || eventEntity.getLocationType() == EventLocationType.ON_THE_FARM) {
            // For off-site or on the farm, use city
            nameBuilder.append(location.city());
        }

        // Add state/province code if exists
        if (StringUtils.isNotEmpty(location.stateProvinceCode())) {
            nameBuilder.append(", ").append(location.stateProvinceCode());
        }

        // Add country code
        if (StringUtils.isNotEmpty(location.countryCode())) {
            nameBuilder.append(", ").append(location.countryCode());
        }

        // Add date
        nameBuilder.append(" - ").append(formattedDate);

        return nameBuilder.toString();
    }
}