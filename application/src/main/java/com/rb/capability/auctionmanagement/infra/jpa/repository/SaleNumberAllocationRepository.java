package com.rb.capability.auctionmanagement.infra.jpa.repository;

import com.rb.capability.auctionmanagement.infra.jpa.entity.SaleNumberAllocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SaleNumberAllocationRepository extends JpaRepository<SaleNumberAllocation, Long> {

    @Query("SELECT MIN(s.saleNumber) FROM SaleNumberAllocation s WHERE s.year = :year AND s.type = :type AND s.inUse = false")
    Integer findNextSaleNumberByYearAndType(@Param("year") int year, @Param("type") String type);

    Optional<SaleNumberAllocation> findBySaleNumber(Integer saleNumber);

    @Query("SELECT s FROM SaleNumberAllocation s WHERE s.saleNumber = :saleNumber AND s.inUse = true")
    Optional<SaleNumberAllocation> findBySaleNumberAndInUseTrue(@Param("saleNumber") Integer saleNumber);

}
