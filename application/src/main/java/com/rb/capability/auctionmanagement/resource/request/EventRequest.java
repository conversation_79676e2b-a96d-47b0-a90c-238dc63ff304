package com.rb.capability.auctionmanagement.resource.request;

import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.resource.response.BusinessUnit;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Builder;

import java.sql.Timestamp;
import java.util.List;

@Builder(toBuilder = true)
public record EventRequest(

        @Schema(description = "The public name of the sale", example = "Fort Worth, TX, USA - Mar 1, 2025")
        @NotBlank(message = "AuctionAdvertisedName is mandatory")
        @Size(max = 255, message = "AuctionAdvertisedName must not exceed 255 characters")
        String auctionAdvertisedName,

        @Schema(description = "Which brand of the sale", example = "RBA")
        @NotBlank(message = "Brand is mandatory")
        @Size(max = 50, message = "Brand must not exceed 50 characters")
        String brand,

        @Schema(description = "The timezone of the sale", example = "America/Chicago")
        @NotBlank(message = "Timezone is mandatory")
        @Size(max = 255, message = "Timezone must not exceed 255 characters")
        String timezone,

        @Schema(description = "The start date of the sale", example = "2025-03-01")
        @NotBlank(message = "StartDateTime is mandatory")
        String startDateTime,

        @Schema(description = "The end date of the sale", example = "2025-03-01")
        @NotBlank(message = "EndDateTime is mandatory")
        String endDateTime,

        @Schema(description = "The currency of the sale", example = "USD")
        @NotEmpty(message = "Currency must not be empty")
        @Size(max = 50, message = "currency must not exceed 50 characters")
        List<String> currency,

        @Schema(description = "Location id for the sale", example = "E2DDE423-5762-849C-E053-85180F0A144A")
        String locationId,

        @Schema(description = "Site id for the sale", example = "905034B4-64A0-4460-9090-705424A050D0")
        @NotBlank(message = "siteId is mandatory")
        @Size(max = 50, message = "siteId must not exceed 50 characters")
        String siteId,

        @Schema(description = "The primary classification of the sale", example = "Industrial")
        @NotBlank(message = "primaryClassification is mandatory")
        @Size(max = 30, message = "primaryClassification must not exceed 30 characters")
        String primaryClassification,

        @Schema(description = "The scheduling status of the sale", example = "Scheduled")
        String schedulingStatus,
        @Schema(description = "The selling formats of the sale", example = "TAL")
        @NotEmpty(message = "auctionFormats must not be empty")
        @Size(max = 30, message = "auctionFormats must not exceed 30 characters")
        List<SellingFormat> auctionFormats,

        @Schema(description = "The ways to participate in the sale", example = "ONSITE")
        @NotEmpty(message = "waysToParticipate must not be empty")
        @Size(max = 30, message = "waysToParticipate must not exceed 30 characters")
        List<ParticipationMethod> waysToParticipate,

        @Schema(description = "Whether registration is open for the sale", example = "true")
        Boolean registrationOpen,

        @Schema(description = "Registration open date for the sale", example = "2025-03-01T00:00:00Z")
        Timestamp registrationAutoOpenDate,

        @Schema(description = "Whether TAL bidding is open for the sale", example = "true")
        Boolean talBidOpen,

        @Schema(description = "TAL bid open date for the sale", example = "2025-03-01T00:00:00Z")
        Timestamp talBidAutoOpenDate,

        @Schema(description = "Whether priority bidding is open for the sale", example = "true")
        Boolean priorityBidOpen,

        @Schema(description = "Priority bid open date for the sale", example = "2025-03-01T00:00:00Z")
        Timestamp priorityBidAutoOpenDate,

        @Schema(description = "Whether OLR is enabled for the sale", example = "true")
        Boolean olrEnabled,

        @Schema(description = "The name of the sale", example = "Fort Worth, TX, USA - Mar 1, 2025")
        @NotBlank(message = "Name is mandatory")
        @Size(max = 255, message = "Name must not exceed 255 characters")
        String name,

        @Schema(description = "Legal Entity Id", example = "ae28f2e4-9579-44bf-b8c4-39d38129f72a")
        @NotBlank(message = "legalEntityId is mandatory")
        @Size(max = 50, message = "legalEntityId must not exceed 50 characters")
        String legalEntityId,

        @Schema(description = "Business Unit ")
        BusinessUnit businessUnit,

        @Schema(description = "Allow IP Inspection", example = "true")
        Boolean allowIpInspection,

        @Schema(description = "The additional classification of the sale", example = "Industrial")
        List<String> additionalClassification,
        @Schema(description = "The Location Type", example = "Permanent")
        EventLocationType locationType,
        @Schema(description = "Set to true if the sale is a RB Marketplace sale", example = "true")
        Boolean rbMarketplaceAuction,
        @Schema(description = "The User full name", example = "John Doe")
        String createdBy,
        @Schema(description = "The User full name", example = "John Doe")
        String lastModifierName,
        @Schema(description = "Whether Private Treaty", example = "true")
        Boolean privateTreaty,
        @Schema(description = "The auction number of the sale", example = "20250301")
        Integer auctionNumber,
        @Schema(description = "The site configuration of the sale", example = "Fort Worth On-site Template")
        String siteConfiguration,
        @Schema(description = "The source auction number", example = "20240301")
        String sourceAuctionNumber,
        @Schema(description = "Add a note to the auction record", example = "I canceled the event")
        String notes,
        @Schema(description = "Flag to indicate if this is a do not use event", example = "false")
        Boolean doNotUse,
        @Schema(description = "Flag to indicate if duplicate auction check needs to be performed", example = "false")
        Boolean skipDuplicateCheck



) {
}