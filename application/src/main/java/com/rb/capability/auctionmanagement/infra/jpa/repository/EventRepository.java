package com.rb.capability.auctionmanagement.infra.jpa.repository;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.resource.response.EventSearchResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

@Repository
public interface EventRepository extends JpaRepository<EventEntity, Long> {
    
    Optional<EventEntity> findByGuid(String guid);

    @Query(value = "select ee from EventEntity ee where ee.eventStartDate >= :startDate and ee.eventStartDate <= :endDate"
            + " AND UPPER(ee.eventAdvertisedName) NOT LIKE 'DO NOT USE%'")
    List<EventEntity> findEventsWithFilters(@Param("startDate") Timestamp startDate, @Param("endDate") Timestamp endDate);

    @Query(value = "SELECT guid, sale_number, name, event_advertised_name FROM event "
            + "WHERE sale_number LIKE CONCAT(:searchParam, '%') "
            + "OR LOWER(name) LIKE LOWER(CONCAT(:searchParam, '%')) "
            + "OR LOWER(event_advertised_name) LIKE LOWER(CONCAT(:searchParam, '%')) ORDER BY "
            + "CASE "
            + "WHEN DATE_TRUNC('month', event_start_date) = DATE_TRUNC('month', CURRENT_DATE) THEN 0 "
            + "WHEN EXTRACT(YEAR FROM event_start_date) = EXTRACT(YEAR FROM CURRENT_DATE) THEN 1 "
            + "ELSE 2 "
            + "END,"
            + "event_start_date DESC",
            nativeQuery = true)
    List<EventSearchResponse> findEvents(@Param("searchParam") String searchParam);

    Optional<EventEntity> findBySaleNumber(String saleNumber);

    @Query(value = "SELECT * FROM event "
            + "WHERE event_start_date > CURRENT_TIMESTAMP "
            + "AND UPPER(event_advertised_name) NOT LIKE 'DO NOT USE%' "
            + "AND ((is_priority_bid_open IS NULL OR is_priority_bid_open = false) AND priority_bid_auto_open_date < CURRENT_TIMESTAMP "
            + "OR (is_tal_bid_open IS NULL OR is_tal_bid_open = false) AND tal_bid_auto_open_date < CURRENT_TIMESTAMP "
            + "OR (is_registration_open IS NULL OR is_registration_open = false) AND registration_auto_open_date < CURRENT_TIMESTAMP)",
            nativeQuery = true)
    List<EventEntity> findAllUpcomingEventsBiddingFlags();
}
