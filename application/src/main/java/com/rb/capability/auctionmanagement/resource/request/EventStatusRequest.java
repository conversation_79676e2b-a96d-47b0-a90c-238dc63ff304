package com.rb.capability.auctionmanagement.resource.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.io.Serializable;

@Builder(toBuilder = true)
public record EventStatusRequest(

        @Schema(description = "The current status of the event", example = "Scheduled")
        String status,

        @Schema(description = "The catalog export date for the event", example = "2025-03-01T00:00:00Z")
        String catalogExportDate,

        @Schema(description = "The user ID of the operator performing this status update", example = "100245")
        int operatorUserId

) implements Serializable {
}
