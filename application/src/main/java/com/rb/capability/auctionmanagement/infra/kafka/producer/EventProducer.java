package com.rb.capability.auctionmanagement.infra.kafka.producer;

import com.rb.capability.auctionmanagement.client.LegalEntityServiceClient;
import com.rb.capability.auctionmanagement.client.model.LegalEntity;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.resource.response.LocationResponse;
import com.rb.capability.auctionmanagement.resource.response.SiteResponse;
import com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys;
import com.rbauction.enterprise.events.models.marketplace.capability.auctionmanagement.*;
import io.opentelemetry.api.trace.Span;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;

import static net.logstash.logback.argument.StructuredArguments.kv;

@Component
@Slf4j
@RequiredArgsConstructor
public class EventProducer {

    @Value("${kafka.topics.enterprise_auction}")
    private String topic;

    private final KafkaTemplate<String, Object> auctionEventKafkaTemplate;
    private final LegalEntityServiceClient legalEntityServiceClient;

    public void publishAuctionEvent(EventDetailsResponse eventResponse) {
        Auction auction = getEventDetailsToPublish(eventResponse);
        String auctionEventGuid = auction.getContent().getAuctionId();
        Span.current().setAttribute(SpanAttributeKeys.EVENT_ID, auctionEventGuid);

        try {
            auctionEventKafkaTemplate.send(topic, eventResponse.eventGuid(), auction)
                    .whenComplete((result, ex) -> {
                        if (ex != null) {
                            logFailed(auction, ex);
                        } else {
                            logSuccess(result);
                        }
                    });
        } catch (Exception e) {
            log.error("send message to kafka failed", e);
        }
    }

    public void logSuccess(SendResult<String, Object> result) {
        if (result != null) {
            var event = (Auction) result.getProducerRecord().value();
            log.info("Auction message sent success with topic: {}, messageId: {}, content: {}.",
                    topic,
                    event.getMessageId(),
                    event.getContent());
        }
    }

    public void logFailed(Auction auction, Throwable throwable) {
        log.error("Auction message publish failed with topic: {}, messageId: {}, content: {}, error: {}.",
                topic,
                auction.getMessageId(),
                auction.getContent(),
                throwable.getMessage());
        log.error("Exception: {}", kv("exception", throwable));
    }

    public Auction getEventDetailsToPublish(EventDetailsResponse eventDetailsResponse) {
        List<String> currencies = eventDetailsResponse.currency();
        if (currencies == null || currencies.isEmpty()) {
            return null;
        }

        AuctionContent content = AuctionContent.newBuilder()
                .setAuctionId(eventDetailsResponse.eventGuid())
                .setAuctionNumber(eventDetailsResponse.saleNumber())
                .setName(eventDetailsResponse.name())
                .setAdvertisedName(eventDetailsResponse.eventAdvertisedName())
                .setTimezone(eventDetailsResponse.timezone())
                .setChannel(eventDetailsResponse.brand())
                .setStartDateTime(eventDetailsResponse.eventStartDate() != null
                        ? Instant.ofEpochMilli(eventDetailsResponse.eventStartDate().getTime()) : null)
                .setEndDateTime(eventDetailsResponse.eventEndDate() != null
                        ? Instant.ofEpochMilli(eventDetailsResponse.eventEndDate().getTime()) : null)
                .setPrimaryClassification(eventDetailsResponse.primaryClassification())
                .setLegalEntityName(getBusinessNameFromLegalEntityId(eventDetailsResponse.legalEntityId()))
                .setCurrency(currencies)
                .setSchedulingStatus(eventDetailsResponse.schedulingStatus())
                .setOperationStatus(eventDetailsResponse.status())
                .setAuctionFormats(eventDetailsResponse.sellingFormats().stream()
                        .map(Enum::name)
                        .toList())
                .setWaysToParticipate(eventDetailsResponse.waysToParticipate().stream()
                        .map(Enum::name)
                        .toList())
                .setRegistrationOpen(eventDetailsResponse.registrationOpen())
                .setRegistrationAutoOpenDate(eventDetailsResponse.registrationAutoOpenDate() != null
                        ? Instant.ofEpochMilli(eventDetailsResponse.registrationAutoOpenDate().getTime()).truncatedTo(java.time.temporal.ChronoUnit.SECONDS) : null)
                .setTalBidOpen(eventDetailsResponse.talBidOpen())
                .setTalBidAutoOpenDate(eventDetailsResponse.talBidAutoOpenDate() != null
                        ? Instant.ofEpochMilli(eventDetailsResponse.talBidAutoOpenDate().getTime()).truncatedTo(java.time.temporal.ChronoUnit.SECONDS) : null)
                .setPriorityBidOpen(eventDetailsResponse.priorityBidOpen())
                .setPriorityBidAutoOpenDate(eventDetailsResponse.priorityBidAutoOpenDate() != null
                        ? Instant.ofEpochMilli(eventDetailsResponse.priorityBidAutoOpenDate().getTime()).truncatedTo(java.time.temporal.ChronoUnit.SECONDS) : null)
                .setOlrEnabled(eventDetailsResponse.olrEnabled())
                .setLocation(buildLocationInfo(eventDetailsResponse))
                .setSite(buildSiteInfo(eventDetailsResponse))
                .setRbMarketplaceAuction(eventDetailsResponse.isRbMarketplaceSale())
                .setBusinessUnit(BusinessUnitContent.newBuilder()
                        .setCode(eventDetailsResponse.businessUnit().code())
                        .setName(eventDetailsResponse.businessUnit().name())
                        .build())
                .setAllowsIpInspection(eventDetailsResponse.allowIpInspection() != null
                        ? String.valueOf(eventDetailsResponse.allowIpInspection()) : null)
                .setAdditionalClassifications(eventDetailsResponse.additionalClassification())
                .setNumberOfDays(eventDetailsResponse.numberOfDays())
                .build();

        return Auction.newBuilder()
                .setMessageId(eventDetailsResponse.eventGuid())
                .setCreatedTime(Instant.now())
                .setContent(content)
                .setSourceSystem("AUCTION_MANAGEMENT")
                .build();
    }

    private AuctionLocation buildLocationInfo(EventDetailsResponse event) {
        LocationResponse locationResponse = event.location();
        if (locationResponse == null) {
            return null;
        }

        // Add this line to set the span attribute
        Span.current().setAttribute("app.location.id", locationResponse.id());

        return AuctionLocation.newBuilder()
                .setId(locationResponse.id())
                .setType(event.locationType() != null  ? event.locationType().description : locationResponse.type())
                .setName(locationResponse.name())
                .setStreetAddress1(locationResponse.streetAddress1())
                .setCity(locationResponse.city())
                .setStateProvinceCode(locationResponse.stateProvinceCode() == null && locationResponse.stateProvince() != null
                        ? locationResponse.stateProvince() : locationResponse.stateProvinceCode())
                .setStateProvince(locationResponse.stateProvince())
                .setCountryCode(locationResponse.countryCode() == null && locationResponse.country() != null
                        ? locationResponse.country() : locationResponse.countryCode())
                .setCountry(locationResponse.country())
                .setPostalCode(locationResponse.postalCode())
                .setLatitude(locationResponse.latitude().doubleValue())
                .setLongitude(locationResponse.longitude().doubleValue())
                .setContacts(locationResponse.contacts().stream()
                        .map(contact -> LocationContact.newBuilder()
                                .setType(contact.type().name())
                                .setFirstName(contact.firstName())
                                .setLastName(contact.lastName())
                                .setPhoneNumber(contact.phoneNumber())
                                .setFaxNumber(contact.faxNumber())
                                .setEmail(contact.email())
                                .build())
                        .toList())
                .build();
    }

    private Site buildSiteInfo(EventDetailsResponse event) {
        SiteResponse siteResponse = event.site();
        if (siteResponse == null) {
            return null;
        }

        return Site.newBuilder()
                .setId(event.siteId())
                .setAdvertisedName(siteResponse.advertisedName())
                .build();
    }

    private String getBusinessNameFromLegalEntityId(String legalEntityId) {
        if (legalEntityId == null || legalEntityId.isBlank()) {
            return null;
        }
        try {
            List<LegalEntity> legalEntities = legalEntityServiceClient.getLegalEntities();

            return legalEntities.stream()
                    .filter(entity -> entity != null
                            && entity.profile() != null
                            && legalEntityId.equals(entity.profile().id()))
                    .map(entity -> entity.profile().businessName())
                    .findFirst()
                    .orElse(null);

        } catch (Exception e) {
            log.error("Error fetching business name for LegalEntityId {}: {}", legalEntityId, e.getMessage(), e);
            return null;
        }
    }
}
