package com.rb.capability.auctionmanagement.resource.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rb.capability.common.ExcludeFromJacocoGeneratedReport;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ExcludeFromJacocoGeneratedReport
public class SalesforceEventResponse {
    @JsonProperty("requestId")
    private String requestId;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("gid")
    private String gid;
}