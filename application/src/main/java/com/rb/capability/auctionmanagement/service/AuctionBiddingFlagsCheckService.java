package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.audit.AuditHelper;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.kafka.producer.EventProducer;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuctionBiddingFlagsCheckService {

    private final EventService eventService;
    private final EventProducer eventProducer;
    private final PlacesServiceClient placesServiceClient;
    private final AuditService auditService;

    @Transactional
    @WithSpan("sync-bidding-flags-unmatched-data")
    public void syncBiddingFlagsUnMatchedData(List<EventEntity> events) {
        log.info("Starting bidding flags sync for {} events", events.size());
        Span.current().setAttribute("app.events.count", events.size());
        
        List<EventEntity> eventsWithFlagUpdated = new ArrayList<>();

        try {
            for (EventEntity event : events) {
                try {
                    Span.current().setAttribute("app.event.id", event.getGuid());
                    log.info("{} {} {} {} - EVENT MISMATCH invalid BIDDING FLAGS",
                            event.getGuid(), event.getName(),
                            event.getIsPriorityBidOpen(), event.getIsTalBidOpen());

                    boolean updated = false;
                    // Store old values for audit
                    Map<String, Object> oldValues = AuditHelper.extractFieldValues(event);
                    event.setOldValues(oldValues);

                    // Check and update priority bidding flag
                    if ((event.getIsPriorityBidOpen() == null || !event.getIsPriorityBidOpen())
                            && (event.getPriorityBidAutoOpenDate() != null
                            && event.getPriorityBidAutoOpenDate().before(Timestamp.valueOf(LocalDateTime.now())))) {
                        event.setIsPriorityBidOpen(true);
                        updated = true;
                        log.info("Setting Priority bid open for event: {}", event.getGuid());
                    }

                    // Check and update TAL bidding flag
                    if ((event.getIsTalBidOpen() == null || !event.getIsTalBidOpen())
                            && (event.getTalBidAutoOpenDate() != null
                            && event.getTalBidAutoOpenDate().before(Timestamp.valueOf(LocalDateTime.now())))) {
                        event.setIsTalBidOpen(true);
                        updated = true;
                        log.info("Setting TAL bid open for event: {}", event.getGuid());
                    }

                    // Check and update registration flag
                    if ((event.getIsRegistrationOpen() == null || !event.getIsRegistrationOpen())
                            && (event.getRegistrationAutoOpenDate() != null
                            && event.getRegistrationAutoOpenDate().before(Timestamp.valueOf(LocalDateTime.now())))) {
                        event.setIsRegistrationOpen(true);
                        updated = true;
                        log.info("Setting Registration open for event: {}", event.getGuid());
                    }

                    if (updated) {
                        event.setLastModifiedDate(new Timestamp(System.currentTimeMillis()));
                        eventsWithFlagUpdated.add(event);
                    }
                } catch (Exception e) {
                    log.error("Error processing bidding flags for event: {}", event.getGuid(), e);
                    Span.current().recordException(e);
                }
            }

            // Save all updated events in batch
            if (!eventsWithFlagUpdated.isEmpty()) {
                eventService.saveEvents(eventsWithFlagUpdated);
                // Audit the changes
                for (EventEntity updatedEvent : eventsWithFlagUpdated) {
                    auditService.auditUpdate(updatedEvent);
                }
                log.info("Updated bidding flags for {} events", eventsWithFlagUpdated.size());
            }

        } catch (Exception e) {
            log.error("Failed to auto check auction events bidding flags", e);
            Span.current().recordException(e);
        } finally {
            // Publishing events for all updated entities
            for (EventEntity updatedEvent : eventsWithFlagUpdated) {
                try {
                    EventDetailsResponse response = EventDetailsResponse.fromEntity(updatedEvent, placesServiceClient);
                    eventProducer.publishAuctionEvent(response);
                    log.info("Published event after bidding flag update: {}", updatedEvent.getGuid());
                } catch (Exception e) {
                    log.error("Failed to publish event after bidding flag update: {}", updatedEvent.getGuid(), e);
                    Span.current().recordException(e);
                }
            }

            log.info("Completed bidding flags sync for {} events, updated {} events",
                    events.size(), eventsWithFlagUpdated.size());
        }
    }
}
