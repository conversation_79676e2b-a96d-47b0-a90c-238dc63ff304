package com.rb.capability.auctionmanagement.resource.response;

import java.util.List;

public record EventRolloverResponse(
    int successCount,
    int totalCount,
    List<String> failedAuctions
) {
    public String summaryMessage() {
        return "The rollover process is complete. %d of %d auctions successfully created."
                .formatted(successCount, totalCount);
    }

    public boolean isSuccess() {
        return failedAuctions == null || failedAuctions.isEmpty();
    }
}
