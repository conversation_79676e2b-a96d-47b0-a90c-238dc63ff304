package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.response.AuditDetailsResponse;
import com.rb.capability.auctionmanagement.service.AuditService;
import com.rb.capability.common.BasicUtils;
import com.rb.essentials.capability.exception.BusinessException;
import com.rb.essentials.log.ApplicationTier;
import com.rb.essentials.log.LoggableEvent;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.zalando.problem.Problem;

import java.util.List;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;

@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class AuditController {
    private final AuditService auditService;

    @GetMapping(value = "/events/{auction_id}/history", produces = MediaType.APPLICATION_JSON_VALUE)
    @LoggableEvent(applicationTire = ApplicationTier.RESOURCE, action = "Get audit details")
    @Operation(tags = {"Audits"}, summary = "Get audit details", description = "Retrieves audit details by GUID")
    @WithSpan("get-audit-details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved audits",
                    content = @Content(
                            mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = AuditDetailsResponse.class))
                    )),
        @ApiResponse(responseCode = "400", description = "Bad Request",
                    content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                            schema = @Schema(implementation = Problem.class),
                            examples = {@ExampleObject(value = """
                                    {
                                      "title": "Bad Request",
                                      "status": 400
                                    }
                                    """)})),
        @ApiResponse(responseCode = "500", description = "Internal Server Error",
                    content = @Content(mediaType = MediaType.APPLICATION_PROBLEM_JSON_VALUE,
                            schema = @Schema(implementation = String.class),
                            examples = {@ExampleObject(value = """
                                    {
                                      "title": "Internal Server Error",
                                      "status": 500,
                                      "detail": "Error while fetching audit list"
                                    }
                                        """)}))
    })
    public ResponseEntity<List<AuditDetailsResponse>> getAuditDetails(@PathVariable("auction_id") String guid) {
        Span currentSpan = Span.current();
        try {
            log.info("AuctionManagement: getAuditDetails endpoint called for GUID: " + guid);
            List<AuditDetailsResponse> response = auditService.getAuditDetails(guid);
            currentSpan.setAttribute(APP_RESPONSE, BasicUtils.convertObjectToJsonString(response));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Unexpected error while fetching audit details for GUID: " + guid, e);
            currentSpan.setAttribute(APP_EXTERNAL_EXCEPTION, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

}
