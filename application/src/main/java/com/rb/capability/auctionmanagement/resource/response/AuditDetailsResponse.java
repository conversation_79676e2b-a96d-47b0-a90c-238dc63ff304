package com.rb.capability.auctionmanagement.resource.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rb.capability.auctionmanagement.infra.jpa.entity.AuditEntity;
import com.rb.capability.auctionmanagement.mapper.AuditDetailsResponseMapper;
import java.util.ArrayList;
import java.util.List;

public record AuditDetailsResponse(

        @JsonIgnore
        String guid,
        String fieldName,
        String oldValue,
        String newValue,
        String changedBy,
        String changedAt

) {
    public static AuditDetailsResponse fromEntity(AuditEntity entity) {
        return AuditDetailsResponseMapper.INSTANCE.toResponse(entity);
    }

    public static List<AuditDetailsResponse> fromEntityList(List<AuditEntity> entityList) {
        List<AuditDetailsResponse> auditDetailsResponseList = new ArrayList<>();
        if (entityList == null || entityList.isEmpty()) {
            return auditDetailsResponseList;
        }
        entityList.stream().forEach(eventEntity -> {
            auditDetailsResponseList.add(fromEntity(eventEntity));
        });
        return auditDetailsResponseList;
    }

}