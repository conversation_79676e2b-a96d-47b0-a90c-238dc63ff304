package com.rb.capability.auctionmanagement.resource.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;

@Builder
public record SiteLocation(

    @Schema(description = "State Province Code", example = "UT")
    String stateProvinceCode,
    @Schema(description = "State Province", example = "Utah")
    String stateProvince,
    @Schema(description = "Country Code", example = "USA")
    String countryCode,
    @Schema(description = "Country", example = "United States")
    String country

) {}