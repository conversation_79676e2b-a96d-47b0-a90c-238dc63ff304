package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.domain.enums.*;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.infra.jpa.repository.SaleNumberAllocationRepository;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload;
import com.rb.capability.auctionmanagement.resource.response.*;
import com.rb.capability.common.utils.EventDateUtils;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class EventRollOverService {

    private final EventRepository eventRepository;
    private final SaleNumberAllocationService saleNumberAllocationService;
    private final PlacesServiceClient placesServiceClient;
    private final SaleNumberAllocationRepository saleNumberAllocationRepository;
    private static final String TYPE_DEFAULT = "Non Agriculture Non Charity";

    @Transactional
    @WithSpan("batch-rollover")
    public ValidationResponse validateRolloverBatch(EventRolloverBatchPayload rolloverAuctions) {
        String message = "";
        boolean success = false;

        try {
            int nextYear = Year.now().plusYears(1).getValue();
            int start = Integer.parseInt(nextYear + "101");
            int end = Integer.parseInt(nextYear + "501");

            int unusedCount = saleNumberAllocationService.countBySaleNumberBetweenAndInUseFalse(start, end);

            if (rolloverAuctions != null && rolloverAuctions.auctions() != null && !rolloverAuctions.auctions().isEmpty()) {
                int requested = rolloverAuctions.auctions().size();

                if (unusedCount >= requested) {
                    message = "Please confirm that " + requested + " auctions will be rolled over to " + nextYear;
                    success = true;
                } else {
                    message = "There are not enough unused auctions to create all the requested auctions. "
                            + "Please either reduce the number of rollover auctions or free up auction numbers by "
                            + "deleting auctions in Proposed status.";
                }
            }

        } catch (Exception e) {
            log.error("Error validating rollover batch", e);
            throw new BusinessException(e.getMessage());
        }

        return new ValidationResponse(success, message);
    }


    public EventRolloverResponse prepareEventRollOver(EventRolloverBatchPayload requests) {

        List<String> failed = new ArrayList<>();
        int year = Year.now().plusYears(1).getValue();
        List<EventRolloverBatchPayload.EventRolloverRequest> auctions = requests.auctions();
        for (EventRolloverBatchPayload.EventRolloverRequest request : auctions) {
            try {
                Optional<EventEntity> originalEventOpt = eventRepository.findBySaleNumber(String.valueOf(request.auctionNumber()));

                if (originalEventOpt.isEmpty()) {
                    log.warn("Original event not found for auctionNumber: {}", request.auctionNumber());
                    failed.add(String.valueOf(request.auctionNumber()));
                    continue;
                }

                EventEntity originalEvent = originalEventOpt.get();
                Timestamp rolledOverStartDate = EventDateUtils.getRolledOverStart(originalEvent.getEventStartDate(), year);
                int noOfDays = EventDateUtils.getUtcDayDifference(originalEvent.getEventStartDate(), originalEvent.getEventEndDate());
                Timestamp rolledOverEndDate = EventDateUtils.getEndDatePlusDays(rolledOverStartDate, noOfDays, originalEvent.getEventEndDate());

                String startDate = rolledOverStartDate.toLocalDateTime().toLocalDate().toString();

                Optional<String> saleNumberOpt = eventRepository.checkAuctionWithStartDateAndSiteId(
                        startDate, originalEvent.getSiteId()
                );

                if (saleNumberOpt.isPresent()) {
                    log.warn("Duplicate auction found for auctionNumber: {}", request.auctionNumber());
                    failed.add(String.valueOf(request.auctionNumber()));
                    continue;
                }

                Integer saleNumber = saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, TYPE_DEFAULT);
                SiteResponse siteResponse = placesServiceClient.getSiteBySiteGuid(originalEvent.getSiteId());
                EventEntity rollOverEntity = getEventEntityToSaveFromSourceEvent(originalEvent, saleNumber, siteResponse, requests.createdBy(),
                        rolledOverStartDate, rolledOverEndDate);
                boolean success = rolloverBatchInsert(rollOverEntity, originalEvent);
                if (!success) {
                    failed.add(rollOverEntity.getSourceAuctionNumber());
                }

            } catch (Exception e) {
                log.error("Failed to prepare event insert for auctionNumber: {}", request.auctionNumber(), e);
                failed.add(String.valueOf(request.auctionNumber()));
            }
        }


        return new EventRolloverResponse(auctions.size() - failed.size(), auctions.size(), failed);
    }

    @Transactional
    public boolean rolloverBatchInsert(EventEntity rollOverEntity, EventEntity sourceEntity) {
        try {
            EventEntity savedEntity = eventRepository.save(rollOverEntity);
            saleNumberAllocationService.markSaleNumberInUse(savedEntity);
            sourceEntity.setIsRolledOver(true);
            eventRepository.save(sourceEntity);

            return true;

        } catch (Exception e) {
            log.error("Transactional insert failed for sourceAuctionNumber: {}", rollOverEntity.getSourceAuctionNumber(), e);
            return false;
        }
    }


    private static EventEntity getEventEntityToSaveFromSourceEvent(
            EventEntity sourceEvent,
            Integer saleNumber,
            SiteResponse siteResponse,
            String userName,
            Timestamp rolledOverStartDate,
            Timestamp rolledOverEndDate) {

        EventEntity newEvent = mapNewEvent(sourceEvent, saleNumber, rolledOverStartDate, rolledOverEndDate, userName);

        if (siteResponse != null) {
            List<String> nameParts = new ArrayList<>();
            EventLocationType locationType = sourceEvent.getLocationType();

            String name = siteResponse.name();
            String state = siteResponse.location() != null ? siteResponse.location().stateProvinceCode() : null;
            String country = siteResponse.location() != null ? siteResponse.location().countryCode() : null;

            if (locationType == EventLocationType.REGIONAL_AUCTION_SITE) {
                String regionalName = siteResponse.defaultAuctionName();
                if (regionalName != null && !regionalName.isEmpty()) {
                    nameParts.add(regionalName + " Regional Event");
                } else {
                    nameParts.add(name + (state != null && !state.isEmpty() ? ", " + state : ""));
                }
            } else if (locationType == EventLocationType.PERMANENT) {
                if (name != null && !name.isEmpty()) {
                    nameParts.add(name);
                }
                if (state != null && !state.isEmpty()) {
                    nameParts.add(state);
                }
            }

            if (country != null && !country.isEmpty()) {
                nameParts.add(country);
            }

            String fullName = String.join(", ", nameParts);
            String finalNameWithDate = fullName;
            Timestamp utcTimestamp = newEvent.getEventStartDate();
            String eventTimezone = newEvent.getTimezone();
            ZoneId zoneId = ZoneId.of(eventTimezone);
            ZonedDateTime zonedStart = utcTimestamp.toInstant().atZone(zoneId);

            if (zonedStart != null) {
                String formattedDate = zonedStart.format(DateTimeFormatter.ofPattern("MMM dd, yyyy", Locale.US));
                finalNameWithDate = fullName + " - " + formattedDate;
            }
            newEvent.setName(finalNameWithDate);
            newEvent.setEventAdvertisedName(finalNameWithDate);
        }

        return newEvent;
    }


    private static EventEntity mapNewEvent(EventEntity sourceEvent, int saleNumber, Timestamp rolledOverStart,
                                           Timestamp rolledOverEnd, String name) {
        EventEntity newEvent = new EventEntity();
        newEvent.setGuid(UUID.randomUUID().toString().toUpperCase());
        newEvent.setSaleNumber(String.valueOf(saleNumber));
        newEvent.setEventStartDate(rolledOverStart);
        newEvent.setEventEndDate(rolledOverEnd);
        newEvent.setStatus(AuctionEventStatus.New.name());
        newEvent.setSchedulingStatus(SchedulingStatus.Proposed);
        newEvent.setTimezone(sourceEvent.getTimezone());
        newEvent.setCreatedDate(Timestamp.from(Instant.now()));
        newEvent.setSiteId(sourceEvent.getSiteId());
        newEvent.setPrimaryClassification(sourceEvent.getPrimaryClassification());
        newEvent.setAdditionalClassification(sourceEvent.getAdditionalClassification());
        newEvent.setBrand(sourceEvent.getBrand());
        newEvent.setLegalEntityId(sourceEvent.getLegalEntityId());
        newEvent.setCurrency(sourceEvent.getCurrency());
        newEvent.setBusinessUnitCode(sourceEvent.getBusinessUnitCode());
        newEvent.setBusinessUnitName(sourceEvent.getBusinessUnitName());
        newEvent.setSiteConfiguration(sourceEvent.getSiteConfiguration());
        newEvent.setSellingFormats(sourceEvent.getSellingFormats());
        newEvent.setWaysToParticipate(sourceEvent.getWaysToParticipate());
        newEvent.setLocationId(sourceEvent.getLocationId());
        newEvent.setLocationType(sourceEvent.getLocationType());
        newEvent.setAllowIpInspection(sourceEvent.getAllowIpInspection());
        newEvent.setIsRbMarketplaceSale(sourceEvent.getIsRbMarketplaceSale());
        newEvent.setIsPrivateTreaty(sourceEvent.getIsPrivateTreaty());
        newEvent.setDoNotUse(sourceEvent.getDoNotUse());
        newEvent.setIsTalBidOpen(false);
        newEvent.setTalBidAutoOpenDate(null);
        newEvent.setIsOlrEnabled(false);
        newEvent.setIsPriorityBidOpen(false);
        newEvent.setPriorityBidAutoOpenDate(null);
        newEvent.setIsRegistrationOpen(false);
        newEvent.setRegistrationAutoOpenDate(null);
        newEvent.setCreatedDate(new Timestamp(System.currentTimeMillis()));
        newEvent.setLastModifiedDate(null);
        newEvent.setLastModifierName(null);
        newEvent.setCreatedBy(name);
        newEvent.setCatalogExportDate(null);
        newEvent.setNotes(null);
        newEvent.setInitialAuctionSyncDate(null);
        newEvent.setSourceAuctionNumber(sourceEvent.getSaleNumber());
        newEvent.setIsRolledOver(false);
        return newEvent;
    }
}
