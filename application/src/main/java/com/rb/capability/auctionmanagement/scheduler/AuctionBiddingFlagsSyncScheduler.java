package com.rb.capability.auctionmanagement.scheduler;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.service.AuctionBiddingFlagsCheckService;
import io.opentelemetry.api.trace.Span;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@RequiredArgsConstructor
public class AuctionBiddingFlagsSyncScheduler {

    private final EventRepository eventRepository;
    private final AuctionBiddingFlagsCheckService auctionBiddingFlagsCheckService;

    @Value("${auction.sync.bidding.flags.enabled:true}")
    private boolean isAuctionSyncBiddingFlagsEnabled;

    @Scheduled(fixedDelayString = "${event.sync.bidding.flags.processing.interval:30000}", 
               initialDelayString = "${event.sync.bidding.flags.processing.initialDelay:30000}")
    @SchedulerLock(name = "AuctionBiddingFlagsSyncScheduler",
                  lockAtLeastFor = "${event.sync.bidding.flags.processing.lockAtLeastFor:PT1S}", 
                  lockAtMostFor = "${event.sync.bidding.flags.processing.lockAtMostFor:PT60S}")
    public void autoSync() {
        try {
            if (!isAuctionSyncBiddingFlagsEnabled) {
                log.info("AuctionBiddingFlagsSyncScheduler - Sync is disabled");
                return;
            }

            Span.current().setAttribute("app.scheduler.name", "AuctionBiddingFlagsSyncScheduler");
            long startTime = System.currentTimeMillis();

            List<EventEntity> eventsToProcess = eventRepository.findAllUpcomingEventsBiddingFlags();
            if (eventsToProcess != null && !eventsToProcess.isEmpty()) {
                log.info("AuctionBiddingFlagsSyncScheduler - Number of events: {}", eventsToProcess.size());
                ExecutorService executorService = Executors.newFixedThreadPool(10);
                executorService.submit(() -> auctionBiddingFlagsCheckService.syncBiddingFlagsUnMatchedData(eventsToProcess));
                executorService.shutdown();
                boolean finished = executorService.awaitTermination(30, TimeUnit.MINUTES);
                log.info("AuctionBiddingFlagsSyncScheduler - Done {} Elapsed time: {} ms",
                        (finished ? "successfully" : "with timeout"), (System.currentTimeMillis() - startTime));
            }
        } catch (Exception e) {
            log.error("AuctionBiddingFlagsSyncScheduler - Failed", e);
            Span.current().setAttribute("app.scheduler.error", e.getMessage());
            Span.current().recordException(e);
        }
    }
}