package com.rb.capability.auctionmanagement.resource.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.mapper.EventDetailsResponseMapper;
import io.swagger.v3.oas.annotations.media.Schema;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public record EventDetailsResponse(
        @JsonProperty("auction_id")
        String eventGuid,
        @JsonIgnore
        String locationId,
        @JsonIgnore
        String siteId,
        @JsonProperty("auction_number")
        String saleNumber,
        @JsonProperty("source_auction_number")
        String sourceAuctionNumber,
        @JsonProperty("name")
        String name,
        @JsonProperty("auction_advertised_name")
        String eventAdvertisedName,
        @JsonProperty("number_of_days")
        Integer numberOfDays,
        String brand,
        String timezone,
        String legalEntityId,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        @JsonProperty("start_date_time")
        Timestamp eventStartDate,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        @JsonProperty("end_date_time")
        Timestamp eventEndDate,
        List<String> currency,
        String primaryClassification,
        String schedulingStatus,
        @JsonProperty("operation_status")
        String status,
        @JsonProperty("auction_formats")
        List<SellingFormat> sellingFormats,
        List<ParticipationMethod> waysToParticipate,
        Boolean registrationOpen,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        Timestamp registrationAutoOpenDate,
        Boolean talBidOpen,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        Timestamp talBidAutoOpenDate,
        Boolean priorityBidOpen,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        Timestamp priorityBidAutoOpenDate,
        Boolean olrEnabled,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        Timestamp createdDate,
        String lastModifierName,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
        Timestamp lastModifiedDate,
        LocationResponse location,
        SiteResponse site,
        @JsonProperty("rb_marketplace_auction")
        Boolean isRbMarketplaceSale,
        @JsonProperty("business_unit")
        BusinessUnit businessUnit,
        Boolean allowIpInspection,
        List<String> additionalClassification,
        EventLocationType locationType,
        String createdBy,
        Boolean privateTreaty,
        String siteConfiguration,
        String notes,
        Timestamp initialAuctionSyncDate,
        Timestamp eventFinalizedDate,
        Boolean rolledOver,
        @Schema(description = "Flag indicating if this is a do not use event")
        Boolean doNotUse



) {
    public static EventDetailsResponse fromEntity(EventEntity entity, PlacesServiceClient placesServiceClient) {
        return EventDetailsResponseMapper.INSTANCE.toResponse(entity, placesServiceClient);
    }

    public static List<EventDetailsResponse> fromEntityListBatch(List<EventEntity> entityList, PlacesServiceClient placesServiceClient) {
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }

        // Collect all unique location IDs and site IDs
        List<String> locationIds = entityList.stream()
            .map(EventEntity::getLocationId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        List<String> siteIds = entityList.stream()
            .map(EventEntity::getSiteId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        // Batch fetch all locations and sites
        Map<String, LocationResponse> locationsMap = placesServiceClient.getLocationsByLocationGuids(locationIds);
        Map<String, SiteResponse> sitesMap = placesServiceClient.getSitesBySiteGuids(siteIds);

        // Map entities using both maps
        return entityList.stream()
            .map(entity -> EventDetailsResponseMapper.INSTANCE.toResponseBatch(entity, locationsMap, sitesMap))
            .collect(Collectors.toList());
    }
}