package com.rb.capability.auctionmanagement.infra.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.sql.Timestamp;

@Entity
@Table(name = "sale_number_allocation")
@Builder
@Data
@RequiredArgsConstructor
@AllArgsConstructor
public class SaleNumberAllocation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "sale_number", nullable = false, unique = true)
    private Integer saleNumber;

    @Column(name = "in_use", nullable = false)
    private boolean inUse;

    @Column(name = "year", nullable = false)
    private int year;
    private String type;

    @CreationTimestamp
    private Timestamp createdAt;

}
