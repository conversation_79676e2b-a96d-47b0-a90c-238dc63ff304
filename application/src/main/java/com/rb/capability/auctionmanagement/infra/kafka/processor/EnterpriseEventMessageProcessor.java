package com.rb.capability.auctionmanagement.infra.kafka.processor;


import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.LegalEntityServiceClient;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.model.LegalEntity;
import com.rb.capability.auctionmanagement.domain.enums.*;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.entity.SaleNumberAllocation;
import com.rb.capability.auctionmanagement.infra.jpa.repository.SaleNumberAllocationRepository;
import com.rb.capability.auctionmanagement.service.EventService;
import com.rbauction.enterprise.events.models.address.Address;
import com.rbauction.enterprise.events.models.sale.EventLocation;
import com.rbauction.enterprise.events.models.sale.SaleEvent;
import io.opentelemetry.api.trace.Span;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.rb.capability.auctionmanagement.domain.enums.AuctionEventClassification.getByEnterpriseEventClassification;

@Slf4j
@Component
@RequiredArgsConstructor
public class EnterpriseEventMessageProcessor<T> {

    private final EventService eventService;
    private final PlacesServiceClient placesServiceClient;
    private final SaleNumberAllocationRepository saleNumberAllocationRepository;
    private final LegalEntityServiceClient legalEntityServiceClient;

    public void processRecord(ConsumerRecord<String, SaleEvent> record) {
        try {
            SaleEvent saleEvent = record.value();

            if (saleEvent.getSaleNumber() == null) {
                Span.current().setAttribute("app.skip.process-sale-event", saleEvent.getSaleEventGUID());
                return;
            }

            if ("IP_WEB".equals(saleEvent.getSourceSystem())) {
                Optional<EventEntity> eventEntityOpt = eventService.findEventByGuid(saleEvent.getSaleEventGUID());
                EventEntity entity = eventEntityOpt.orElse(new EventEntity());

                if (saleEvent.getEventLocations() != null && !saleEvent.getEventLocations().isEmpty()) {
                    saleEvent.getEventLocations().stream()
                            .filter(Objects::nonNull)
                            .findFirst()
                            .ifPresent(eventLocation -> {
                                // Set siteId if available
                                if (eventLocation.getSiteGUID() != null) {
                                    entity.setSiteId(eventLocation.getSiteGUID());
                                }
                                entity.setLocationType(EventLocationType.getSiteLocationTypeByEnterpriseEventLocationType(eventLocation.getType()));
                                // If address exists, attempt to get or create location ID
                                Address address = eventLocation.getAddress();
                                if (address != null) {
                                    String locationId = getOrCreateLocationId(eventLocation);
                                    entity.setLocationId(locationId);
                                } else {
                                    log.warn("Address is null for location with GUID: {}", eventLocation.getSiteGUID());
                                }
                            });
                }

                saveEvent(saleEvent, entity);
                log.info("Processed EventEntity: {}", entity);
            }
        } catch (Exception e) {
            Span.current().recordException(e);
            log.error("Failed to process message key:{} topic:{}", record.key(), record.topic(), e);
        }
    }

    public void saveEvent(SaleEvent saleEvent, EventEntity eventEntity) {
        try {
            populateEventEntity(saleEvent, eventEntity);
            eventService.saveEvent(eventEntity);
            Span.current().setAttribute("app.create-event.id", eventEntity.getGuid());
            assignSaleNumberAllocation(saleEvent, eventEntity);
        } catch (Exception e) {
            Span.current().recordException(e);
        }
    }

    private void assignSaleNumberAllocation(SaleEvent saleEvent, EventEntity eventEntity) {
        Integer saleNumber = saleEvent.getSaleNumber();
        
        // Extract year from sale number
        String saleNumberStr = saleNumber.toString();
        int year = Integer.parseInt(saleNumberStr.substring(0, 4));
        
        // Determine event type based on classifications
        String eventType = determineEventType(eventEntity);
        Optional<SaleNumberAllocation> existingAllocation = saleNumberAllocationRepository.findBySaleNumber(saleNumber);
        if (existingAllocation.isPresent()) {
            SaleNumberAllocation allocation = existingAllocation.get();
            allocation.setInUse(true);
            allocation.setType(eventType);
            saleNumberAllocationRepository.save(allocation);
            return;
        } else {
            // Create and save allocation record
            SaleNumberAllocation allocation = SaleNumberAllocation.builder()
                    .saleNumber(saleNumber)
                    .inUse(true)
                    .year(year)
                    .type(eventType)
                    .build();

            saleNumberAllocationRepository.save(allocation);
        }
    }


    private String determineEventType(EventEntity eventEntity) {
        List<String> classifications = extractClassifications(eventEntity);
        
        boolean isCharity = AuctionEventClassification.isCharity(classifications);
        boolean isAgriculture = AuctionEventClassification.isAgriculture(classifications);
        
        if (isCharity) {
            return "Charity";
        } else if (isAgriculture) {
            return "Agriculture";
        } else {
            return "Non Agriculture Non Charity";
        }
    }

    private List<String> extractClassifications(EventEntity eventEntity) {
        List<String> classifications = new ArrayList<>();
        
        Optional.ofNullable(eventEntity.getPrimaryClassification())
                .ifPresent(classifications::add);
                
        Optional.ofNullable(eventEntity.getAdditionalClassification())
                .filter(s -> !s.isEmpty())
                .map(s -> s.split(","))
                .map(List::of)
                .ifPresent(classifications::addAll);
                
        return classifications;
    }

    private void populateEventEntity(SaleEvent saleEvent, EventEntity eventEntity) {
        eventEntity.setGuid(saleEvent.getSaleEventGUID());
        eventEntity.setSaleNumber(saleEvent.getSaleNumber() != null ? saleEvent.getSaleNumber().toString() : null);
        eventEntity.setEventAdvertisedName(saleEvent.getAdvertisedName());
        eventEntity.setBrand("RBA");
        eventEntity.setTimezone(saleEvent.getTimeZone());
        eventEntity.setEventStartDate(saleEvent.getStartDateTime() != null ? new Timestamp(saleEvent.getStartDateTime()) : null);
        eventEntity.setEventEndDate(saleEvent.getEndDateTime() != null ? new Timestamp(saleEvent.getEndDateTime()) : null);
        eventEntity.setCurrency(saleEvent.getCurrency());
        eventEntity.setPrimaryClassification(getByEnterpriseEventClassification(saleEvent.getPrimaryClassification()));
        eventEntity.setAdditionalClassification(formatClassificationList(saleEvent.getAdditionalClassifications()));
        eventEntity.setSchedulingStatus(saleEvent.getSchedulingStatus() != null
                ? SchedulingStatus.getSchedulingStatusFromEnterpriseMessage(saleEvent.getSchedulingStatus())
                : null);
        eventEntity.setSellingFormats(SellingFormat.getSellingFormatFromEnterpriseMessage(saleEvent.getType()));
        eventEntity.setWaysToParticipate(ParticipationMethod.getWaysToParticipateFromEnterpriseMessage(saleEvent.getWaysToParticipate()));
        eventEntity.setIsRegistrationOpen(saleEvent.getIsRegistrationEnabled());
        eventEntity.setRegistrationAutoOpenDate(saleEvent.getRegistrationEnabledDate() != null
                ? new Timestamp(saleEvent.getRegistrationEnabledDate().toEpochMilli()) : null);
        eventEntity.setIsTalBidOpen(saleEvent.getIsOnlineBiddingEnabled());
        eventEntity.setTalBidAutoOpenDate(saleEvent.getTalBiddingEnabledDate() != null
                ? new Timestamp(saleEvent.getTalBiddingEnabledDate().toEpochMilli()) : null);
        eventEntity.setIsPriorityBidOpen(saleEvent.getIsPriorityBiddingEnabled());
        eventEntity.setPriorityBidAutoOpenDate(saleEvent.getPriorityBiddingEnabledDate() != null
                ? new Timestamp(saleEvent.getPriorityBiddingEnabledDate().toEpochMilli()) : null);
        eventEntity.setIsOlrEnabled(saleEvent.getOlrEnabled());
        eventEntity.setLastModifierName("System");
        eventEntity.setCreatedBy("System");
        eventEntity.setLastModifiedDate(new Timestamp(System.currentTimeMillis()));
        eventEntity.setIsRbMarketplaceSale(saleEvent.getIsRBMarketplaceSaleEvent());
        eventEntity.setName(saleEvent.getName());
        eventEntity.setStatus(saleEvent.getOperationStatus() != null ? AuctionEventStatus.findByOperationStatus(saleEvent.getOperationStatus()).name() : null);
        eventEntity.setBusinessUnitCode(saleEvent.getBusinessUnitCode());
        eventEntity.setBusinessUnitName(saleEvent.getBusinessUnitName());
        eventEntity.setOracleProjectId(saleEvent.getOracleProjectID());
        eventEntity.setLegalEntityId(getLegalEntityIdFromMarsSegmentId(saleEvent.getLegalEntitySegmentId(), saleEvent.getLegalEntityName()));
    }

    public static String formatClassificationList(List<String> classifications) {
        if (classifications == null || classifications.isEmpty()) {
            return null;
        }

        return classifications.stream()
                .map(AuctionEventClassification::getByEnterpriseEventClassification)
                .collect(Collectors.joining(","));
    }

    /**
     * Attempts to retrieve an existing location via the Places service.
     * If not found, creates a new location.
     */
    private String getOrCreateLocationId(EventLocation eventLocation) {
        return placesServiceClient.createLocation(eventLocation);
    }

    private String getLegalEntityIdFromMarsSegmentId(Integer marsLegalEntitySegmentId, String legalEntityName) {
        if (marsLegalEntitySegmentId == null && StringUtils.isBlank(legalEntityName)) {
            return null;
        }

        try {
            List<LegalEntity> legalEntities = legalEntityServiceClient.getLegalEntities();

            return legalEntities.stream()
                .filter(entity -> entity != null
                    && entity.profile() != null)
                .filter(entity ->
                    // Match by segment ID if available
                    (marsLegalEntitySegmentId != null
                        && entity.profile().marsLegalEntitySegmentId() != null
                        && entity.profile().marsLegalEntitySegmentId().equals(marsLegalEntitySegmentId.longValue()))
                    || // Or match by business name if segment ID not found
                    (marsLegalEntitySegmentId == null
                        && !StringUtils.isBlank(legalEntityName)
                        && legalEntityName.equals(entity.profile().businessName()))
                )
                .map(entity -> entity.profile().id())
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.error("Error retrieving legal entity for MARS_LEGAL_ENTITY_SEGMENT_ID {} or Business Unit Name {}: {}",
                    marsLegalEntitySegmentId, legalEntityName, e.getMessage(), e);
            return null;
        }
    }
}
