package com.rb.capability.auctionmanagement.domain.enums;

import lombok.Generated;

public enum Reason implements RecordableEnum {
    UNKNOWN("Unknown"),
    INVALID_DATA("Existing contract is invalid"),
    INVALID_INPUT("Invalid Input"),
    INVALID_CONFIG("Invalid Configuration"),
    MISSING_DATA("Missing Data"),
    API_ERROR("Api error"),
    READ_TIMEOUT("Read timed out"),
    API_SERVICE_ERROR("Api service error"),
    UNAUTHORIZED("Unauthorized"),
    UNAUTHENTICATED("Salesforce authentication failed"),
    NOT_FOUND("Not Found"),
    SERVER_UNAVAILABLE("Server unavailable"),
    INVALID_REQUEST("Request is invalid"),
    ALREADY_EXISTS("Already exists");

    private final String reasonDesc;

    Reason(String reason) {
        this.reasonDesc = reason;
    }

    @Generated
    @Override
    public String toString() {
        return this.reasonDesc;
    }
}