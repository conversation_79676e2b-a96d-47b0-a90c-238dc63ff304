package com.rb.capability.auctionmanagement.client.exception;

import com.rb.capability.auctionmanagement.domain.enums.Reason;

public class AuctionManagementException extends RuntimeException {
    private final Reason reason;
    private final String detail;
    private Exception exception;

    public AuctionManagementException(Reason reason, String detail) {
        super(detail);
        this.reason = reason;
        this.detail = detail;
        this.exception = null;
    }


    public String getDetail() {
        return this.detail;
    }

    public Exception getException() {
        return (exception == null) ? null : new Exception(exception);
    }
}