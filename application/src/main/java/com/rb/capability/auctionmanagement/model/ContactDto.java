package com.rb.capability.auctionmanagement.model;

import jakarta.validation.constraints.Size;
import lombok.Builder;

@Builder(toBuilder = true)
public record ContactDto(

        @Size(max = 200, message = "First Name must be at most 200 characters")
        String firstName,

        @Size(max = 200, message = "Last Name must be at most 200 characters")
        String lastName,

        @Size(max = 500, message = "Email must be at most 500 characters")
        String email,

        @Size(max = 100, message = "Fax Number must be at most 100 characters")
        String faxNumber,

        @Size(max = 100, message = "Cell Phone Number must be at most 100 characters")
        String cellPhoneNumber,

        @Size(max = 200, message = "Company Name must be at most 200 characters")
        String companyName) {

}
