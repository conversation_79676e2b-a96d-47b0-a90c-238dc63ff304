package com.rb.capability.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.jose4j.json.internal.json_simple.JSONObject;

import java.util.Map;

public class BasicUtils {
    public static String convertObjectToJsonString(Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            return null;
        }
    }

    public static JSONObject convertToJsonObject(Object obj) {
        try {
            return new JSONObject(new ObjectMapper().convertValue(obj, Map.class));
        } catch (Exception e) {
            return null;
        }
    }
}
