package com.rb.capability.common.utils;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;

import java.sql.Time;
import java.sql.Timestamp;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;

public class EventDateUtils {

    public static int mapNumberOfDays(EventEntity entity) {

        ZoneId eventTimeZone = ZoneId.of(entity.getTimezone());

        Instant startInstant = entity.getEventStartDate().toInstant();
        Instant endInstant = entity.getEventEndDate().toInstant();
        ZonedDateTime startDate = startInstant.atZone(eventTimeZone);
        ZonedDateTime endDate = endInstant.atZone(eventTimeZone);

        int daysDiff = (int) ChronoUnit.DAYS.between(startDate.toLocalDate(), endDate.toLocalDate()) + 1;

        return daysDiff;
    }

    public static Timestamp getRolledOverStart(Timestamp originalStartTimestamp, int targetYear) {
        LocalDateTime originalDateTime = originalStartTimestamp.toLocalDateTime();
        LocalDate originalDate = originalDateTime.toLocalDate();
        LocalTime originalTime = originalDateTime.toLocalTime();

        DayOfWeek dayOfWeek = originalDate.getDayOfWeek();
        int ordinalWeek = (originalDate.getDayOfMonth() - 1) / 7 + 1;

        LocalDate targetDate = LocalDate.of(targetYear, originalDate.getMonth(), 1)
                .with(TemporalAdjusters.dayOfWeekInMonth(ordinalWeek, dayOfWeek));
        LocalDateTime rolledOverDateTime = LocalDateTime.of(targetDate, originalTime);
        return Timestamp.valueOf(rolledOverDateTime);
    }

    public static Timestamp getEndDatePlusDays(Timestamp startTimestamp, int numberOfDays, Timestamp endTimestamp) {
        LocalDate startDate = startTimestamp.toLocalDateTime().toLocalDate();
        LocalTime originalTime = endTimestamp.toLocalDateTime().toLocalTime();

        LocalDate endDate = startDate.plusDays(numberOfDays - 1);
        LocalDateTime endDateTime = LocalDateTime.of(endDate, originalTime);

        return Timestamp.valueOf(endDateTime);
    }

    public static int getUtcDayDifference(Timestamp start, Timestamp end) {
        LocalDate startDate = start.toInstant().atZone(ZoneOffset.UTC).toLocalDate();
        LocalDate endDate = end.toInstant().atZone(ZoneOffset.UTC).toLocalDate();

        return (int) ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }
}
