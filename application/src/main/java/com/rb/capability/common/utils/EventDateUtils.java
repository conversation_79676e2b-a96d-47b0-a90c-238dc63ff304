package com.rb.capability.common.utils;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;

import java.time.*;
import java.time.temporal.ChronoUnit;

public class EventDateUtils {

    public static int mapNumberOfDays(EventEntity entity) {

        ZoneId eventTimeZone = ZoneId.of(entity.getTimezone());

        Instant startInstant = entity.getEventStartDate().toInstant();
        Instant endInstant = entity.getEventEndDate().toInstant();
        ZonedDateTime startDate = startInstant.atZone(eventTimeZone);
        ZonedDateTime endDate = endInstant.atZone(eventTimeZone);

        int daysDiff = (int) ChronoUnit.DAYS.between(startDate.toLocalDate(), endDate.toLocalDate()) + 1;

        return daysDiff;
    }
}
