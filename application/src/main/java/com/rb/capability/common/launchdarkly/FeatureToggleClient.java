package com.rb.capability.common.launchdarkly;

import com.launchdarkly.sdk.LDUser;
import com.launchdarkly.sdk.server.LDClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FeatureToggleClient {
    LDClient client;
    LDUser user;

    @Autowired
    public FeatureToggleClient(LDClient ldClient) {
        this.client = ldClient;

        // end user key, not configured in launch darkly
        this.user = new LDUser("");
    }

    public boolean getFeatureFlagByKey(String keyName) {
        return this.client.boolVariation(keyName, this.user, true);
    }

    public boolean isFeatureFlagEnabled(String keyName) {
        return this.client.boolVariation(keyName, this.user, false);
    }
}
