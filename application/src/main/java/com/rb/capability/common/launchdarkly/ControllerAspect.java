package com.rb.capability.common.launchdarkly;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.NotImplementedException;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Component
@AllArgsConstructor
public class ControllerAspect {
    private final FeatureToggleClient featureToggleClient;

    @Before(value = "@annotation(before)")
    public void featureToggleHandler(FeatureToggle before) {
        var featureFlagName = before.featureName();
        if (Boolean.FALSE.equals(featureToggleClient.getFeatureFlagByKey(featureFlagName))) {
            throw new NotImplementedException();
        }
    }
}
