package com.rb.capability.common.config;

import com.rb.capability.common.seedwork.BaseEntity;
import org.mapstruct.MapperConfig;
import org.mapstruct.Mapping;

@MapperConfig
public interface BaseMapperConfig<T extends BaseEntity, E> {

    @Mapping(ignore = true, target = "id")
    @Mapping(ignore = true, target = "createdDate")
    @Mapping(ignore = true, target = "updatedDate")
    T domainToEntity(E source);
}