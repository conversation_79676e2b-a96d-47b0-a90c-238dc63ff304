package com.rb.capability.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JsonUtils {
    private static final String JSON_PREFIX = ")]}',\n";

    public static String convertObjectToJsonString(Object o) {
        return convertObjectToJsonString(o, false, false);
    }

    public static String convertObjectToJsonString(Object o, boolean isPrettyPrint, boolean withPrefix) {
        String result = new JsonObject().toString();

        ObjectMapper om = new ObjectMapper();
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        ObjectWriter ow = om.writer();

        if (isPrettyPrint) {
            ow = ow.withDefaultPrettyPrinter();
        }

        try {
            result = ow.writeValueAsString(o);
        } catch (Exception e) {
            log.error("Failed to convert an object " + o + " to JSON string", e);
        }

        result = result != null ? result : new JsonObject().toString();

        return (withPrefix ? JSON_PREFIX : "") + result;
    }
}
