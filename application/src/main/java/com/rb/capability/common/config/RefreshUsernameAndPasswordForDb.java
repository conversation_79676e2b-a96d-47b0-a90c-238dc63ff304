package com.rb.capability.common.config;

import com.zaxxer.hikari.HikariDataSource;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.context.refresh.ContextRefresher;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
@Profile("!test & !local")
public class RefreshUsernameAndPasswordForDb {

    private static final String VAULT_FOLDER = "/vault/secrets/";
    private static final WatchEvent.Kind[] EVENTS = {
        StandardWatchEventKinds.ENTRY_MODIFY, StandardWatchEventKinds.ENTRY_CREATE, StandardWatchEventKinds.ENTRY_DELETE
    };

    private ThreadPoolTaskExecutor executor;

    private ContextRefresher contextRefresher;

    private HikariDataSource hikariDataSource;

    private DataSourceProperties dataSourceProperties;

    @EventListener(ApplicationReadyEvent.class)
    public void afterApplicationReady() {
        executor.execute(() -> {
            while (true) {
                log.info("Executor: {}", executor.getClass().getCanonicalName());
                try (WatchService watchService = FileSystems.getDefault().newWatchService()) {
                    Path path = Paths.get(VAULT_FOLDER);
                    path.register(watchService, EVENTS);
                    while (true) {
                        WatchKey watchKey;
                        try {
                            watchKey = watchService.take();
                        } catch (InterruptedException e) {
                            log.warn("Interrupted while waiting for file modification event: " + e.getMessage(), e);
                            continue;
                        }
                        watchKey.pollEvents()
                                .forEach(event ->
                                        log.info("Event kind: {}, File affected: {}.", event.kind(), event.context()));
                        watchKey.reset();
                        contextRefresher.refresh();
                        hikariDataSource.getHikariConfigMXBean().setUsername(dataSourceProperties.getUsername());
                        hikariDataSource.getHikariConfigMXBean().setPassword(dataSourceProperties.getPassword());
                        log.info("Soft evicting db connections...");
                        hikariDataSource.getHikariPoolMXBean().softEvictConnections();
                    }
                } catch (Exception e) {
                    log.error("An error occurred while setting up the file watcher: " + e.getMessage(), e);
                }
            }
        });
    }
}
