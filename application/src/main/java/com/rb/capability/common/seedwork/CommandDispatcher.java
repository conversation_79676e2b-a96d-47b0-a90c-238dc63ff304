package com.rb.capability.common.seedwork;

import org.springframework.core.ResolvableType;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import java.util.*;
import java.util.function.Function;

@Component
public class CommandDispatcher<C extends Command> {

    private final Map<String, CommandHandler<? extends DomainEvent, C>> commandHandlerMap;

    public CommandDispatcher(Collection<CommandHandler<? extends DomainEvent, C>> commandHandlerMap) {
        this.commandHandlerMap = new HashMap<>();
        for (CommandHandler<? extends DomainEvent, C> handler : commandHandlerMap) {
            ResolvableType[] interfaces = ResolvableType.forType(ClassUtils.getUserClass(handler.getClass())).getInterfaces();
            Arrays.stream(interfaces)
                  .filter(resolvableType -> Objects.equals(resolvableType.getRawClass(), CommandHandler.class))
                  .findFirst()
                  .ifPresent(resolvableType -> this.commandHandlerMap.put(resolvableType.getGeneric(1).getType().getTypeName(), handler));
        }
    }

    public <T extends C> List<DomainEvent> dispatch(T command) {
        var commandTypeName = command.getClass().getTypeName();
        if (!this.commandHandlerMap.containsKey(commandTypeName)) {
            throw new UnsupportedOperationException("there is no command handler mapped with %s".formatted(commandTypeName));
        }

        var commandHandler = this.commandHandlerMap.get(commandTypeName);
        var events = commandHandler.execute(command);
        return events.stream().map((Function<DomainEvent, DomainEvent>) domainEvent -> domainEvent).toList();
    }
}
