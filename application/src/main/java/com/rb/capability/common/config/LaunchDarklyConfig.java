package com.rb.capability.common.config;

import com.launchdarkly.sdk.server.LDClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LaunchDarklyConfig {
    @Bean
    public LDClient launchDarklyClient(
            @Value("${ld_sdk_key}") String launchDarklySdkKey) {
        return new LDClient(launchDarklySdkKey);
    }
}
