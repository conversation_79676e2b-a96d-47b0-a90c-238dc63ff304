package com.rb.capability.common.constants;

public enum EnterpriseEventLocationType {
    PermanentSite("PERMANENT_SITE"),
    Virtual("VIRTUAL"),
    OnlineWebBased("ON_LINE_WEB_BASED"),
    OffSite("OFF_SITE"),
    OnTheFarm("ON_THE_FARM"),
    RegionalAuctionSite("REGIONAL_AUCTION_SITE"),
    Satellite("SATELLITE"),
    Other("OTHER");

    public final String enterpriseEventLocationType;

    EnterpriseEventLocationType(String enterpriseEventLocationType) {
        this.enterpriseEventLocationType = enterpriseEventLocationType;
    }

    public static String getEnumByString(String code) {
        for (EnterpriseEventLocationType e : EnterpriseEventLocationType.values()) {
            if (e.name().equals(code)) {
                return e.enterpriseEventLocationType;
            }
        }
        return code;
    }
}
