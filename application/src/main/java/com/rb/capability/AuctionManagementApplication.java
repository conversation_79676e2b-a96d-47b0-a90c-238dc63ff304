package com.rb.capability;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@PropertySource({"classpath:git.properties"})
@ComponentScan("com.rb")
@OpenAPIDefinition(
        info = @Info(title = "Auction Management API's"),
        servers = {
            @Server(url = "http://auction-management.auction-management-dev.svc.cluster.local:8080/v1", description = "Dev Internal"),
            @Server(url = "http://auction-management.auction-management-qa.svc.cluster.local:8080/v1", description = "QA Internal"),
            @Server(url = "http://auction-management.auction-management-prod.svc.cluster.local:8080/v1", description = "Prod Internal"),
            @Server(url = "https://api.dev.marketplace.ritchiebros.com/auction-management/v1", description = "Dev Public"),
            @Server(url = "https://api.qa.marketplace.ritchiebros.com/auction-management/v1", description = "QA Public")
        }
)
@EnableAsync
@EnableRetry
public class AuctionManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuctionManagementApplication.class, args);
    }
}
