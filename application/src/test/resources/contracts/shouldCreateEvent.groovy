package contracts

import org.springframework.cloud.contract.spec.Contract

Contract.make {
    description "should create an event"
    
    request {
        method POST()
        url "/api/v1/events"
        headers {
            contentType('application/json')
        }
        body("""
            {
                "name": "Test Event",
                "auctionAdvertisedName": "Test Event",
                "brand": "RBA",
                "timezone": "UTC",
                "startDateTime": "2025-03-23",
                "endDateTime": "2025-03-23",
                "currency": ["USD"],
                "locationId": "3232sfs",
                "siteId": "343543fdsf",
                "primaryClassification": "Industrial",
                "schedulingStatus": "Proposed",
                "auctionFormats": ["TAL"],
                "waysToParticipate": ["ONSITE"],
                "registrationOpen": true,
                "talBidOpen": true,
                "priorityBidOpen": true,
                "olrEnabled": true,
                "legalEntityId": "1234567890",
                "businessUnit": {
                    "code": "123",
                    "name": "Test BU"
                }
            }
        """)
    }
    
    response {
        status 201
        headers {
            contentType('application/json')
        }
        body("""
            {
                "guid": "test-guid-123",
                "saleNumber": "2025101"
            }
        """)
    }
}