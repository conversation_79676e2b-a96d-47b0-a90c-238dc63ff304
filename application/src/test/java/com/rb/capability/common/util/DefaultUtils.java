package com.rb.capability.common.util;

import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rbauction.enterprise.events.models.address.Address;
import com.rbauction.enterprise.events.models.sale.EventLocation;
import com.rbauction.enterprise.events.models.sale.SaleEvent;
import java.util.List;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.resource.response.BusinessUnit;

import java.time.Instant;
import java.util.Collections;

public class DefaultUtils {

    public static SaleEvent createSampleSaleEvent() {
        SaleEvent saleEvent = new SaleEvent();
        saleEvent.setSourceSystem("IP_WEB");
        saleEvent.setSaleEventGUID("test-guid");
        saleEvent.setSaleNumber(2025101);
        saleEvent.setAdvertisedName("Test Advertised Event");
        saleEvent.setTimeZone("America/New_York");
        saleEvent.setCurrency("USD");
        saleEvent.setPrimaryClassification("INDUSTRIAL");
        saleEvent.setAdditionalClassifications(Collections.singletonList("CHARITY"));
        saleEvent.setSchedulingStatus("CONFIRMED");
        saleEvent.setWaysToParticipate("ONLINE");
        saleEvent.setIsRegistrationEnabled(true);
        saleEvent.setIsPriorityBiddingEnabled(true);
        saleEvent.setRegistrationEnabledDate(Instant.now());
        saleEvent.setTalBiddingEnabledDate(Instant.now());
        saleEvent.setPriorityBiddingEnabledDate(Instant.now());
        saleEvent.setOlrEnabled(true);
        saleEvent.setIsRBMarketplaceSaleEvent(false);
        saleEvent.setOperationStatus("NEW");
        saleEvent.setBusinessUnitCode("520");
        saleEvent.setBusinessUnitName("520 Brisbane");
        saleEvent.setOracleProjectID(1199782);

        EventLocation eventLocation = new EventLocation();
        eventLocation.setSiteGUID("site-123");
        Address address = new Address();
        address.setStreetAddress1("123 Test St");
        address.setLocality("Test City");
        address.setRegion("Test Region");
        address.setCountry("Test Country");
        address.setPostalCode("12345");
        eventLocation.setAddress(address);

        saleEvent.setEventLocations(Collections.singletonList(eventLocation));

        return saleEvent;
    }

    public static EventEntity createSampleEventEntity() {
        return EventEntity.builder()
                .saleNumber("2025101")
                .eventAdvertisedName("Test Advertised Event")
                .brand("RB")
                .timezone("America/New_York")
                .currency("USD")
                .primaryClassification("INDUSTRIAL")
                .schedulingStatus(SchedulingStatus.Confirmed)
                .sellingFormats("TAL")
                .waysToParticipate("ONLINE")
                .isRegistrationOpen(true).build();
    }

    public static EventRequest createSampleEventRequest() {
        return EventRequest.builder()
                .name("Edmonton, AB, CAN - Feb 26, 2025")
                .auctionAdvertisedName("Edmonton, AB, CAN - Feb 26, 2025")
                .brand("RBA")
                .locationId("c84cd85a-6415-4115-94d8-71cd165607bd")
                .siteId("00849404-D084-4474-B460-64147024A084")
                .timezone("America/Denver")
                .startDateTime("2025-03-29")
                .endDateTime("2025-03-30")
                .currency(List.of("CAD"))
                .rbMarketplaceAuction(true)
                .primaryClassification("Agriculture")
                .schedulingStatus("Confirmed")
                .auctionFormats(List.of(SellingFormat.TAL, SellingFormat.OLR))
                .waysToParticipate(List.of(ParticipationMethod.ONLINE, ParticipationMethod.ONSITE))
                .legalEntityId("56e7145f-74be-4aff-a742-f8c3832b8f6e")
                .businessUnit(BusinessUnit.builder().name("123 Test").code("123").build())
                .build();
    }

    public static EventRequest.EventRequestBuilder createDefaultEventRequest() {
        return EventRequest.builder()
                .name("Test Event")
                .auctionAdvertisedName("Test Event")
                .brand("RBA")
                .timezone("UTC")
                .startDateTime("2025-03-23")
                .endDateTime("2025-03-23")
                .currency(List.of("USD"))
                .locationId("3232sfs")
                .siteId("343543fdsf")
                .primaryClassification("Industrial")
                .schedulingStatus("Proposed")
                .auctionFormats(List.of(SellingFormat.TAL))
                .waysToParticipate(List.of(ParticipationMethod.ONSITE))
                .registrationOpen(true)
                .registrationAutoOpenDate(null)
                .talBidOpen(true)
                .talBidAutoOpenDate(null)
                .priorityBidOpen(true)
                .priorityBidAutoOpenDate(null)
                .olrEnabled(true)
                .legalEntityId("1234567890")
                .businessUnit(BusinessUnit.builder().name("123 Test").code("123").build());
    }
}
