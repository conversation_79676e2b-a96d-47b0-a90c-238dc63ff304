package com.rb.capability.archunit;

import static com.tngtech.archunit.library.Architectures.layeredArchitecture;

import com.tngtech.archunit.core.domain.JavaClasses;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import com.tngtech.archunit.core.importer.ImportOption;
import com.tngtech.archunit.library.Architectures;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
class LayeredArchitectureTest {

    private JavaClasses checkClasses;

    private static final String commonPackagePath = "com.rb.capability";

    @BeforeEach
    public void setUp() {
        checkClasses = new ClassFileImporter()
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_JARS)
                .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                .importPackages(commonPackagePath);
    }

    @Test
    void layer_dependencies_are_respected() {
        Architectures.LayeredArchitecture layeredArchitecture = layeredArchitecture()
                .layer("resource")
                .definedBy("..resource..")
                .layer("domain")
                .definedBy("..domain..")
                .layer("infra")
                .definedBy("..infra..")
                .layer("commandhandler")
                .definedBy("..commandhandler..");

        layeredArchitecture
                .whereLayer("resource")
                .mayNotBeAccessedByAnyLayer()
                .allowEmptyShould(true)
                .check(checkClasses);

        layeredArchitecture
                .whereLayer("domain")
                .mayOnlyBeAccessedByLayers("commandhandler", "infra", "resource")
                .allowEmptyShould(true)
                .check(checkClasses);

        layeredArchitecture
                .whereLayer("infra")
                .mayOnlyBeAccessedByLayers("commandhandler", "resource", "domain")
                .allowEmptyShould(true)
                .check(checkClasses);

        layeredArchitecture
                .whereLayer("commandhandler")
                .mayOnlyBeAccessedByLayers("resource")
                .allowEmptyShould(true)
                .check(checkClasses);
    }
}
