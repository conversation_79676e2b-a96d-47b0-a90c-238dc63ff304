package com.rb.capability.archunit.rules;

import com.rb.essentials.log.LoggableEvent;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

@AnalyzeClasses(packages = "com.rb.capability")
public class CommandHandlerRulesTest {

    @ArchTest
    static final ArchRule commandHandlerMethodsShouldBeAnnotatedWithLoggableEvent =
        methods().that()
                 .areDeclaredInClassesThat()
                 .resideInAPackage("..commandhandler..")
                 .and()
                 .arePublic()
                 .should()
                 .beAnnotatedWith(LoggableEvent.class)
                 .allowEmptyShould(true);
}
