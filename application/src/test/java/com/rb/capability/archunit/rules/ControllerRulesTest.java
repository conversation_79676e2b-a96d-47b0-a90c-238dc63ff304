package com.rb.capability.archunit.rules;

import com.rb.essentials.log.LoggableEvent;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.lang.ArchRule;
import org.springframework.web.bind.annotation.*;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods;

@AnalyzeClasses(packages = "com.rb.capability")
public class ControllerRulesTest {

    @ArchTest
    static final ArchRule controllerMethodsShouldBeAnnotatedWithLoggableEvent =
        methods().that()
                 .areDeclaredInClassesThat().resideInAPackage("..resource..")
                 .and()
                 .areDeclaredInClassesThat().haveSimpleNameEndingWith("Controller")
                 .and()
                 .areAnnotatedWith(PostMapping.class)
                 .or()
                 .areAnnotatedWith(GetMapping.class)
                 .or()
                 .areAnnotatedWith(PutMapping.class)
                 .or()
                 .areAnnotatedWith(PatchMapping.class)
                 .or()
                 .areAnnotatedWith(DeleteMapping.class)
                 .should()
                 .beAnnotatedWith(LoggableEvent.class)
                 .allowEmptyShould(true);
}
