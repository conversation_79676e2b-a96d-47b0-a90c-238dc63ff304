package com.rb.capability.auctionmanagement.utils;


import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.common.utils.EventDateUtils;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.time.*;
import java.time.temporal.TemporalAdjusters;

import static org.junit.jupiter.api.Assertions.*;

class EventDateUtilsTest {

    @Test
    void testMapNumberOfDays_ReturnsCorrectDifference() {
        EventEntity event = new EventEntity();
        event.setTimezone("UTC");

        Timestamp start = Timestamp.valueOf("2025-04-13 15:00:00");
        Timestamp end = Timestamp.valueOf("2025-04-15 06:00:00");

        event.setEventStartDate(start);
        event.setEventEndDate(end);

        int days = EventDateUtils.mapNumberOfDays(event);

        assertEquals(3, days); // 13, 14, 15
    }

    @Test
    void testGetRolledOverStart_CorrectDayOfWeekAndTime() {
        Timestamp original = Timestamp.valueOf("2024-04-13 10:15:00"); // 2nd Saturday of April
        int targetYear = 2025;

        Timestamp result = EventDateUtils.getRolledOverStart(original, targetYear);

        LocalDateTime expectedDate = LocalDate.of(2025, 4, 1)
                .with(TemporalAdjusters.dayOfWeekInMonth(2, DayOfWeek.SATURDAY))
                .atTime(10, 15);

        assertEquals(expectedDate, result.toLocalDateTime());
    }

    @Test
    void testGetEndDatePlusDays_AddsCorrectDaysPreservingTime() {
        Timestamp start = Timestamp.valueOf("2025-04-13 00:00:00");
        Timestamp originalEnd = Timestamp.valueOf("2025-04-15 06:30:00");

        Timestamp expected = Timestamp.valueOf("2025-04-15 06:30:00");

        Timestamp result = EventDateUtils.getEndDatePlusDays(start, 3, originalEnd);

        assertEquals(expected, result);
    }
}
