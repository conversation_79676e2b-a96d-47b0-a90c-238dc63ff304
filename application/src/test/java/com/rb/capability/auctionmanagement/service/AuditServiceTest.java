package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.audit.AuditDisplayName;
import com.rb.capability.auctionmanagement.audit.AuditHelper;
import com.rb.capability.auctionmanagement.infra.jpa.entity.AuditEntity;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.AuditRepository;
import com.rb.capability.auctionmanagement.resource.response.AuditDetailsResponse;
import com.rb.essentials.capability.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AuditServiceTest {

    @Mock
    private AuditRepository auditRepository;

    @InjectMocks
    private AuditService auditService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getAuditDetails_Success() {
        String eventGuid = "123";
        List<AuditEntity> auditEntities = Collections.singletonList(new AuditEntity());
        when(auditRepository.findByGuidOrderByChangedAtDesc(eventGuid)).thenReturn(auditEntities);

        List<AuditDetailsResponse> result = auditService.getAuditDetails(eventGuid);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(auditRepository).findByGuidOrderByChangedAtDesc(eventGuid);
    }

    @Test
    void getAuditDetails_Exception() {
        String eventGuid = "123";
        when(auditRepository.findByGuidOrderByChangedAtDesc(eventGuid)).thenThrow(new RuntimeException("DB Error"));

        BusinessException exception = assertThrows(BusinessException.class,
                () -> auditService.getAuditDetails(eventGuid));
        assertEquals("DB Error", exception.getMessage());
        verify(auditRepository).findByGuidOrderByChangedAtDesc(eventGuid);
    }

    @Test
    void auditUpdate_NoOldValues() {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setLastModifierName("user1");
        eventEntity.setOldValues(null);

        auditService.auditUpdate(eventEntity);

        verify(auditRepository, never()).saveAll(anyList());
    }

    @Test
    void auditUpdate_WithChanges() {
        // Setup
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setLastModifierName("user1");
        eventEntity.setIsRegistrationOpen(true);  // Using a field with @AuditDisplayName

        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("isRegistrationOpen", false);
        eventEntity.setOldValues(oldValues);

        // Capture the list of audit entities saved
        ArgumentCaptor<List<AuditEntity>> auditEntitiesCaptor = ArgumentCaptor.forClass(List.class);

        // Execute
        auditService.auditUpdate(eventEntity);

        // Verify
        verify(auditRepository).saveAll(auditEntitiesCaptor.capture());
        List<AuditEntity> savedAuditEntities = auditEntitiesCaptor.getValue();

        assertFalse(savedAuditEntities.isEmpty());
        AuditEntity savedAudit = savedAuditEntities.get(0);
        assertEquals("123", savedAudit.getGuid());
        assertEquals("Registration Open", savedAudit.getFieldName());  // Updated to match the @AuditDisplayName
        assertEquals("false", savedAudit.getOldValue());
        assertEquals("true", savedAudit.getNewValue());
        assertEquals("user1", savedAudit.getChangedBy());
    }

    @Test
    void auditUpdate_WithTimestampChanges() {
        // Setup
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setLastModifierName("user1");
        eventEntity.setTimezone("America/New_York");

        // Set a timestamp field that has changed
        Timestamp newTimestamp = Timestamp.valueOf("2025-05-24 12:00:00");
        eventEntity.setTalBidAutoOpenDate(newTimestamp);

        // Create old values map with different timestamp
        Map<String, Object> oldValues = new HashMap<>();
        Timestamp oldTimestamp = Timestamp.valueOf("2025-05-23 12:00:00");
        oldValues.put("talBidAutoOpenDate", "2025-05-23 08:00:00"); // Formatted old value
        oldValues.put("talBidAutoOpenDate_original", oldTimestamp); // Original timestamp for comparison
        eventEntity.setOldValues(oldValues);

        // Mock the AuditHelper.convertTimestampToLocalTime method
        try (MockedStatic<AuditHelper> mockedAuditHelper = mockStatic(AuditHelper.class)) {
            // Setup the mock to return a formatted string when called
            mockedAuditHelper.when(() -> AuditHelper.isTimestampField(anyString())).thenReturn(true);
            mockedAuditHelper.when(() -> AuditHelper.convertTimestampToLocalTime(any(Timestamp.class), anyString()))
                    .thenReturn("2025-05-24 08:00:00");

            // Capture the list of audit entities saved
            ArgumentCaptor<List<AuditEntity>> auditEntitiesCaptor = ArgumentCaptor.forClass(List.class);

            // Execute
            auditService.auditUpdate(eventEntity);

            // Verify
            verify(auditRepository).saveAll(auditEntitiesCaptor.capture());
            List<AuditEntity> savedAuditEntities = auditEntitiesCaptor.getValue();

            // Verify that we have an audit entry for the timestamp field
            boolean foundTalBidChange = savedAuditEntities.stream()
                .anyMatch(audit ->
                    audit.getFieldName().equals("TAL Bid Auto Open Date")
                            && "2025-05-23 08:00:00".equals(audit.getOldValue())
                            && "2025-05-24 08:00:00".equals(audit.getNewValue())
                );

            assertTrue(foundTalBidChange, "Should find the talBidAutoOpenDate change in audit entries");
        }
    }

    @Test
    void auditUpdate_NoChangesInTimestampFields() {
        // Setup
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setLastModifierName("user1");
        eventEntity.setTimezone("America/New_York");
        eventEntity.setIsRegistrationOpen(false);
        eventEntity.setIsTalBidOpen(false);
        eventEntity.setIsPriorityBidOpen(false);
        eventEntity.setIsOlrEnabled(false);
        eventEntity.setIsRbMarketplaceSale(false);

        // Set a timestamp field that has NOT changed (same value, different representation)
        Timestamp timestamp = Timestamp.valueOf("2025-05-24 12:00:00");
        eventEntity.setTalBidAutoOpenDate(timestamp);

        // Create old values map with same timestamp but different string representation
        Map<String, Object> oldValues = new HashMap<>();
        // Add all fields with their current values to prevent false positives
        oldValues.put("timezone", "America/New_York");
        oldValues.put("isRegistrationOpen", false);
        oldValues.put("isTalBidOpen", false);
        oldValues.put("isPriorityBidOpen", false);
        oldValues.put("isOlrEnabled", false);
        oldValues.put("isRbMarketplaceSale", false);

        // Add the timestamp field with both formatted and original values
        oldValues.put("talBidAutoOpenDate", "2025-05-24 08:00:00"); // Formatted old value (in NY timezone)
        oldValues.put("talBidAutoOpenDate_original", timestamp); // Same original timestamp
        eventEntity.setOldValues(oldValues);

        // Execute
        auditService.auditUpdate(eventEntity);

        // Verify that no audit entries were saved since there were no actual changes
        verify(auditRepository, never()).saveAll(anyList());
    }

    @Test
    void auditInsert_WithMultipleFields() {
        // Setup
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setCreatedBy("user1");
        eventEntity.setName("Test Event");
        eventEntity.setPrimaryClassification("INDUSTRIAL");

        // Capture the list of audit entities saved
        ArgumentCaptor<List<AuditEntity>> auditEntitiesCaptor = ArgumentCaptor.forClass(List.class);

        // Execute
        auditService.auditInsert(eventEntity);

        // Verify
        verify(auditRepository).saveAll(auditEntitiesCaptor.capture());
        List<AuditEntity> savedAuditEntities = auditEntitiesCaptor.getValue();

        assertFalse(savedAuditEntities.isEmpty());
        assertTrue(savedAuditEntities.size() >= 2); // At least name and classification should be audited

        // Verify each field was properly audited
        savedAuditEntities.forEach(audit -> {
            assertEquals("123", audit.getGuid());
            assertEquals("user1", audit.getChangedBy());
            assertNull(audit.getOldValue());
            assertNotNull(audit.getNewValue());
        });
    }

    @Test
    void auditUpdate_NoChanges() {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setName("Same Name");
        eventEntity.setIsRegistrationOpen(false);
        eventEntity.setIsTalBidOpen(false);
        eventEntity.setIsPriorityBidOpen(false);
        eventEntity.setIsOlrEnabled(false);
        eventEntity.setIsRbMarketplaceSale(false);

        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("name", "Same Name");
        oldValues.put("isRegistrationOpen", false);  // Match exact field names
        oldValues.put("isTalBidOpen", false);
        oldValues.put("isPriorityBidOpen", false);
        oldValues.put("isOlrEnabled", false);
        oldValues.put("isRbMarketplaceSale", false);
        eventEntity.setOldValues(oldValues);

        auditService.auditUpdate(eventEntity);

        verify(auditRepository, never()).saveAll(anyList());
    }

    @Test
    void auditInsert_WithNullFields() {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid("123");
        eventEntity.setCreatedBy("user1");
        eventEntity.setName(null); // Null field should be ignored

        auditService.auditInsert(eventEntity);

        ArgumentCaptor<List<AuditEntity>> captor = ArgumentCaptor.forClass(List.class);
        verify(auditRepository).saveAll(captor.capture());

        List<AuditEntity> savedAudits = captor.getValue();
        savedAudits.forEach(audit ->
            assertNotNull(audit.getNewValue())
        );
    }

    @Test
    void auditUpdate_WithDisplayNameAnnotation() {
        // Setup
        EventEntity entity = new EventEntity();
        entity.setGuid("123");
        entity.setLastModifierName("user1");
        entity.setIsRegistrationOpen(true);

        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put("isRegistrationOpen", false);
        entity.setOldValues(oldValues);

        // Execute
        auditService.auditUpdate(entity);

        // Verify
        ArgumentCaptor<List<AuditEntity>> captor = ArgumentCaptor.forClass(List.class);
        verify(auditRepository).saveAll(captor.capture());

        List<AuditEntity> savedAudits = captor.getValue();

        // Find and verify the specific audit entry
        boolean hasRegistrationOpenAudit = savedAudits.stream()
            .anyMatch(audit ->
                "Registration Open".equals(audit.getFieldName())
                        && "false".equals(audit.getOldValue())
                        && "true".equals(audit.getNewValue())
                        && "123".equals(audit.getGuid())
                        && "user1".equals(audit.getChangedBy())
            );

        assertTrue(hasRegistrationOpenAudit,
            "No matching audit entry found for Registration Open field with display name annotation");
    }

    @Test
    void auditUpdate_illegalAccessException_shouldThrowRuntimeException() throws Exception {
        // Given
        Field mockField = mock(Field.class);
        when(mockField.getName()).thenReturn("testField");
        when(mockField.get(any())).thenThrow(new IllegalAccessException("Access denied"));

        EventEntity mockEntity = mock(EventEntity.class);
        Map<String, Object> oldValues = Map.of("testField", "oldValue");
        List<AuditEntity> auditEntries = new ArrayList<>();

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            try {
                Object oldValue = oldValues.get(mockField.getName());
                Object newValue = mockField.get(mockEntity); // This line throws
                if (!Objects.equals(oldValue, newValue)) {
                    String displayName = mockField.isAnnotationPresent(AuditDisplayName.class)
                            ? mockField.getAnnotation(AuditDisplayName.class).value()
                            : mockField.getName();

                    AuditEntity log = new AuditEntity();
                    log.setFieldName(displayName);
                    log.setGuid(mockEntity.getGuid());
                    log.setOldValue(oldValue != null ? oldValue.toString() : null);
                    log.setNewValue(newValue != null ? newValue.toString() : null);
                    log.setChangedBy(mockEntity.getLastModifierName());
                    log.setChangedAt(new Date());
                    auditEntries.add(log);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Error accessing field values", e);
            }
        });

        assertTrue(exception.getCause() instanceof IllegalAccessException);
        assertEquals("Error accessing field values", exception.getMessage());
    }
}
