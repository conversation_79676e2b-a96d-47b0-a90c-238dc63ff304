package com.rb.capability.auctionmanagement.domain.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

class SellingFormatTest {

    @Test
    void testEnumValues() {
        // Verify all enum values are properly defined
        SellingFormat[] values = SellingFormat.values();
        assertEquals(4, values.length);
        assertEquals(SellingFormat.TAL, values[0]);
        assertEquals(SellingFormat.OLR, values[1]);
        assertEquals(SellingFormat.ONLINE_AUCTION, values[2]);
        assertEquals(SellingFormat.MARKETPLACE, values[3]);
    }

    @Test
    void testEnumValueOf() {
        assertEquals(SellingFormat.TAL, SellingFormat.valueOf("TAL"));
        assertEquals(SellingFormat.OLR, SellingFormat.valueOf("OLR"));
        assertEquals(SellingFormat.ONLINE_AUCTION, SellingFormat.valueOf("ONLINE_AUCTION"));
        assertEquals(SellingFormat.MARKETPLACE, SellingFormat.valueOf("MARKETPLACE"));
    }

    @Test
    void testValueOf_InvalidValue() {
        assertThrows(IllegalArgumentException.class, () -> SellingFormat.valueOf("INVALID"));
    }

    @ParameterizedTest
    @MethodSource("provideEnterpriseFormatMappings")
    void testGetSellingFormatFromEnterpriseMessage_ValidFormats(String input, String expected) {
        assertEquals(expected, SellingFormat.getSellingFormatFromEnterpriseMessage(input));
    }

    private static Stream<Arguments> provideEnterpriseFormatMappings() {
        return Stream.of(
            Arguments.of("LIVE_TAL_ONLY", "TAL"),
            Arguments.of("LIVE_OLR_TAL", "TAL,OLR"),
            Arguments.of("LIVE_AUCTION", "OLR"),
            Arguments.of("ONLINE_AUCTION", "ONLINE_AUCTION"),
            Arguments.of("MARKETPLACE", "MARKETPLACE")
        );
    }

    @ParameterizedTest
    @NullAndEmptySource
    void testGetSellingFormatFromEnterpriseMessage_NullOrEmptyFormat(String format) {
        assertNull(SellingFormat.getSellingFormatFromEnterpriseMessage(format));
    }

    @ParameterizedTest
    @ValueSource(strings = {"INVALID", "UNKNOWN", "DRAFT"})
    void testGetSellingFormatFromEnterpriseMessage_InvalidFormat(String format) {
        assertNull(SellingFormat.getSellingFormatFromEnterpriseMessage(format));
    }
}