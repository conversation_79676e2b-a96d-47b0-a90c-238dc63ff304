package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.exception.DataNotFoundException;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.infra.kafka.producer.EventProducer;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.auctionmanagement.resource.request.EventStatusRequest;
import com.rb.capability.auctionmanagement.resource.response.BusinessUnit;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.resource.response.EventResponse;
import com.rb.capability.auctionmanagement.resource.response.EventSearchResponse;
import com.rb.essentials.capability.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import static com.rb.capability.common.util.DefaultUtils.createSampleEventEntity;
import static com.rb.capability.common.util.DefaultUtils.createSampleEventRequest;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.rb.capability.auctionmanagement.audit.AuditHelper;

@ExtendWith(MockitoExtension.class)
class EventServiceTest {

    @Mock
    private EventRepository eventRepository;

    @InjectMocks
    private EventService eventService;
    @Mock
    private AuditService auditService;
    @Mock
    private EventProducer eventProducer;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private SaleNumberAllocationService saleNumberAllocationService;

    @Mock
    private AsyncEventService asyncEventService;

    private EventRequest validEventRequest;
    private EventEntity eventEntity;

    private static final String FROM_DATE = "2025-03-01T00:00:00Z";
    private static final String TO_DATE = "2025-03-31T23:59:59Z";

    @BeforeEach
    void setUp() {
        validEventRequest = EventRequest.builder()
                .name("Test Event")
                .auctionAdvertisedName("Test Event")
                .brand("RBA")
                .timezone("UTC")
                .startDateTime("2025-03-23")
                .endDateTime("2025-03-23")
                .currency(List.of("USD"))
                .locationId("3232sfs")
                .siteId("343543fdsf")
                .primaryClassification("INDUSTRIAL")
                .schedulingStatus("Proposed")
                .auctionFormats(List.of(SellingFormat.TAL))
                .waysToParticipate(List.of(ParticipationMethod.ONSITE))
                .registrationOpen(true)
                .registrationAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .talBidOpen(true)
                .talBidAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .priorityBidOpen(true)
                .priorityBidAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .olrEnabled(true)
                .legalEntityId("1234567890")
                .auctionNumber(2025101)
                .sourceAuctionNumber("2024101")
                .businessUnit(BusinessUnit.builder().code("123").name("Test BU").build())
                .build();

        eventEntity = new EventEntity();
        eventEntity.setGuid("12345");
        eventEntity.setSaleNumber("SALE-67890");
    }

    @Test
    void createEvent_WithValidRequest() {
        var eventRequest = createSampleEventRequest();
        when(saleNumberAllocationService.generateSaleNumber(anyInt(), any(EventRequest.class))).thenReturn(2025101);
        when(eventRepository.save(any(EventEntity.class))).thenReturn(EventEntity.builder()
                .saleNumber("2025101")
                .eventAdvertisedName("Test Advertised Event")
                .eventStartDate(Timestamp.valueOf("2025-03-15 00:00:00"))
                .eventEndDate(Timestamp.valueOf("2025-03-30 00:00:00"))
                .timezone("America/Chicago")
                .schedulingStatus(SchedulingStatus.valueOf("Confirmed"))
                .guid("test-guid").build());

        EventResponse response = eventService.createEvent(eventRequest);

        assertNotNull(response);
        assertEquals("test-guid", response.eventGuid());
        assertEquals("2025101", response.saleNumber());
        verify(saleNumberAllocationService).generateSaleNumber(anyInt(), any(EventRequest.class));
        verify(eventRepository).save(any(EventEntity.class));
        verify(asyncEventService).processEventAsync(any());
        verify(saleNumberAllocationService).markSaleNumberInUse(any());
    }

    @Test
    void getEventDetails_Success() {
        String eventGuid = "123e4567-e89b-12d3-a456-************";
        EventEntity mockEntity = createMockEventEntity(eventGuid);
        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEntity));

        EventDetailsResponse response = eventService.getEventDetails(eventGuid);

        assertNotNull(response);
        assertEquals(eventGuid, response.eventGuid());
        assertEquals(mockEntity.getSaleNumber(), response.saleNumber());
        assertEquals(mockEntity.getEventAdvertisedName(), response.eventAdvertisedName());
        verify(eventRepository).findByGuid(eventGuid);
    }

    @Test
    void getEventDetails_NotFound() {
        String eventGuid = "non-existent-eventGuid";
        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.empty());

        DataNotFoundException exception = assertThrows(DataNotFoundException.class,
                () -> eventService.getEventDetails(eventGuid));
        assertEquals("Event not found with GUID: " + eventGuid, exception.getMessage());
        verify(eventRepository).findByGuid(eventGuid);
    }

    private EventEntity createMockEventEntity(String guid) {
        return EventEntity.builder()
                .guid(guid)
                .saleNumber("20240101")
                .eventAdvertisedName("Test Event")
                .brand("RB")
                .timezone("UTC")
                .eventStartDate(new Timestamp(System.currentTimeMillis()))
                .eventEndDate(new Timestamp(System.currentTimeMillis()))
                .currency("USD")
                .primaryClassification("INDUSTRIAL")
                .schedulingStatus(SchedulingStatus.Proposed)
                .sellingFormats("TAL")
                .waysToParticipate("ONSITE")
                .isRegistrationOpen(true)
                .registrationAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .isTalBidOpen(true)
                .talBidAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .isPriorityBidOpen(true)
                .priorityBidAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .isOlrEnabled(true)
                .createdDate(new Timestamp(System.currentTimeMillis()))
                .lastModifierName("testUser")
                .lastModifiedDate(new Timestamp(System.currentTimeMillis()))
                .build();
    }



    @Test
    void testFindEventsWithFilters_SuccessfulWithStatus() {
        EventEntity event1 = new EventEntity();
        event1.setSchedulingStatus(SchedulingStatus.Proposed);
        event1.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event1.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event1.setTimezone("America/New_York");
        event1.setPrimaryClassification("Industrial");
        event1.setLocationType(EventLocationType.PERMANENT);

        EventEntity event2 = new EventEntity();
        event2.setSchedulingStatus(SchedulingStatus.Cancelled);
        event2.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event2.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event2.setTimezone("America/New_York");
        event2.setPrimaryClassification("Dealer Dismantler");
        event2.setLocationType(EventLocationType.REGIONAL_AUCTION_SITE);

        List<EventEntity> mockEvents = Arrays.asList(event1, event2);

        Timestamp startDate = Timestamp.from(ZonedDateTime.parse(FROM_DATE).toInstant());
        Timestamp endDate = Timestamp.from(ZonedDateTime.parse(TO_DATE).toInstant());

        when(eventRepository.findEventsWithFilters(startDate, endDate))
                .thenReturn(mockEvents);

        List<EventDetailsResponse> result = eventService.findEventsWithFilters(
                FROM_DATE,
                TO_DATE,
                new String[]{"Proposed"},
                new String[]{"Industrial", "Dealer Dismantler"},
                new String[]{"PERMANENT", "REGIONAL_AUCTION_SITE"},
                false
        );

        assertNotNull(result);
        assertEquals(1, result.size()); // Only one event matches the "Proposed" status
        assertEquals("Proposed", result.get(0).schedulingStatus());

        verify(eventRepository, times(1)).findEventsWithFilters(startDate, endDate);
    }


    @Test
    void testFindEventsWithFilters_SuccessfulWithoutStatus() {
        EventEntity event1 = new EventEntity();
        event1.setSchedulingStatus(SchedulingStatus.Proposed);
        event1.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event1.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event1.setTimezone("America/New_York");
        event1.setPrimaryClassification("Industrial");
        event1.setLocationType(EventLocationType.PERMANENT);

        EventEntity event2 = new EventEntity();
        event2.setSchedulingStatus(SchedulingStatus.Proposed);
        event2.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event2.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event2.setTimezone("America/New_York");
        event2.setPrimaryClassification("Dealer Dismantler");
        event2.setLocationType(EventLocationType.REGIONAL_AUCTION_SITE);

        List<EventEntity> mockEvents = Arrays.asList(event1, event2);

        Timestamp startDate = Timestamp.from(ZonedDateTime.parse(FROM_DATE).toInstant());
        Timestamp endDate = Timestamp.from(ZonedDateTime.parse(TO_DATE).toInstant());

        when(eventRepository.findEventsWithFilters(startDate, endDate))
                .thenReturn(mockEvents);

        List<EventDetailsResponse> result = eventService.findEventsWithFilters(
                FROM_DATE,
                TO_DATE,
                null, // No scheduling status filter
                new String[]{"Industrial", "Dealer Dismantler"},
                new String[]{"PERMANENT", "REGIONAL_AUCTION_SITE"},
                false
        );

        assertNotNull(result);
        assertEquals(2, result.size()); // All matching primaryType and locationType pass
        verify(eventRepository, times(1)).findEventsWithFilters(startDate, endDate);
    }


    @Test
    void testFindEventsWithFilters_EmptyResult() {
        Timestamp startDate = Timestamp.from(ZonedDateTime.parse(FROM_DATE).toInstant());
        Timestamp endDate = Timestamp.from(ZonedDateTime.parse(TO_DATE).toInstant());

        when(eventRepository.findEventsWithFilters(startDate, endDate))
                .thenReturn(Collections.emptyList());

        List<EventDetailsResponse> result = eventService.findEventsWithFilters(
                FROM_DATE,
                TO_DATE,
                new String[]{"Proposed"},
                new String[]{"Industrial", "Dealer Dismantler"},
                new String[]{"PERMANENT", "REGIONAL_AUCTION_SITE"},
                false
        );

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(eventRepository, times(1)).findEventsWithFilters(startDate, endDate);
    }


    @Test
    void testFindEventsWithFilters_InvalidDateFormat() {
        String invalidDate = "invalid-date-format";

        assertThrows(BusinessException.class, () ->
                eventService.findEventsWithFilters(
                        invalidDate,
                        TO_DATE,
                        new String[]{"SCHEDULED"},
                        null,
                        null,
                        false
                )
        );

        verify(eventRepository, never()).findEventsWithFilters(any(), any());
    }


    @Test
    void testFindEventsWithFilters_MultipleStatuses() {
        EventEntity event1 = new EventEntity();
        event1.setSchedulingStatus(SchedulingStatus.Published);
        event1.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event1.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event1.setTimezone("America/New_York");
        event1.setPrimaryClassification("Industrial");
        event1.setLocationType(EventLocationType.PERMANENT);

        EventEntity event2 = new EventEntity();
        event2.setSchedulingStatus(SchedulingStatus.Proposed);
        event2.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event2.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event2.setTimezone("America/New_York");
        event2.setPrimaryClassification("Dealer Dismantler");
        event2.setLocationType(EventLocationType.REGIONAL_AUCTION_SITE);

        EventEntity event3 = new EventEntity();
        event3.setSchedulingStatus(SchedulingStatus.Cancelled);
        event3.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        event3.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        event3.setTimezone("America/New_York");
        event3.setPrimaryClassification("Dealer Dismantler");
        event3.setLocationType(EventLocationType.REGIONAL_AUCTION_SITE);

        List<EventEntity> mockEvents = Arrays.asList(event1, event2, event3);

        Timestamp startDate = Timestamp.from(ZonedDateTime.parse(FROM_DATE).toInstant());
        Timestamp endDate = Timestamp.from(ZonedDateTime.parse(TO_DATE).toInstant());

        when(eventRepository.findEventsWithFilters(startDate, endDate))
                .thenReturn(mockEvents);

        List<EventDetailsResponse> result = eventService.findEventsWithFilters(
                FROM_DATE,
                TO_DATE,
                new String[]{"Proposed", "Published"},
                new String[]{"Industrial", "Dealer Dismantler"},
                new String[]{"PERMANENT", "REGIONAL_AUCTION_SITE"},
                false
        );

        assertNotNull(result);
        assertEquals(2, result.size()); // Only Published and Proposed match
        verify(eventRepository, times(1)).findEventsWithFilters(startDate, endDate);
    }


    @Test
    void getEventDetails_ShouldReturnEventDetailsResponseWithNewFields() {
        EventEntity mockEventEntity = createSampleEventEntity();
        mockEventEntity.setEventStartDate(Timestamp.valueOf("2025-03-01 00:00:00"));
        mockEventEntity.setEventEndDate(Timestamp.valueOf("2025-03-02 00:00:00"));
        mockEventEntity.setStatus("NEW");
        mockEventEntity.setName("TEST");

        String eventGuid = "test-event-guid2";

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEventEntity));


        EventDetailsResponse response = eventService.getEventDetails(eventGuid);

        assertNotNull(response);

        assertFalse(response.isRbMarketplaceSale());
        assertNull(response.businessUnit());
        assertEquals(mockEventEntity.getStatus(), response.status());
        assertEquals(mockEventEntity.getName(), response.name());
        verify(eventRepository).findByGuid(eventGuid);

    }

    @Test
    void testUpdateEvent_Success() {
        when(eventRepository.findByGuid("12345")).thenReturn(Optional.of(eventEntity));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(eventEntity);

        EventResponse response = eventService.updateEvent("12345", validEventRequest);

        assertNotNull(response);
        assertEquals("12345", response.eventGuid());
        assertEquals("SALE-67890", response.saleNumber());
    }

    @Test
    void testUpdateEvent_NotFound() {
        when(eventRepository.findByGuid("12345")).thenReturn(Optional.empty());

        DataNotFoundException thrown = assertThrows(DataNotFoundException.class,
                () -> eventService.updateEvent("12345", validEventRequest));

        assertEquals("Event not found with GUID: 12345", thrown.getMessage());
    }

    @Test
    void testFindEventByGuid() {
        // Arrange
        String guid = "test-guid";
        EventEntity mockEvent = new EventEntity();
        mockEvent.setGuid(guid);

        when(eventRepository.findByGuid(guid)).thenReturn(Optional.of(mockEvent));
        Optional<EventEntity> result = eventService.findEventByGuid(guid);
        assertTrue(result.isPresent());
        assertEquals(guid, result.get().getGuid());
        verify(eventRepository, times(1)).findByGuid(guid);
    }

    @Test
    void testSaveEvent() {
        EventEntity event = new EventEntity();
        event.setGuid("event-guid");
        eventService.saveEvent(event);
        verify(eventRepository, times(1)).save(event);
    }

    @Test
    void updateEventStatus_Success() {
        // Given
        String eventGuid = "test-guid";
        EventStatusRequest request = new EventStatusRequest("Published", "2024-03-01T10:00:00Z", 1);

        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .schedulingStatus(SchedulingStatus.Confirmed)
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        assertEquals(eventGuid, response.eventGuid());
        verify(eventRepository).findByGuid(eventGuid);
        verify(eventRepository).save(any(EventEntity.class));
    }

    @Test
    void updateEventStatus_WhenStatusIsLotNumbering() {
        // Given
        String eventGuid = "test-guid";
        EventStatusRequest request = new EventStatusRequest("LotNumbering", null, 1);
        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .catalogExportDate(Timestamp.valueOf("2024-03-01 10:00:00"))
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        verify(eventRepository).save(argThat(event ->
                event.getCatalogExportDate() == null
                        && "LotNumbering".equals(event.getStatus())
        ));
    }

    @Test
    void updateEventStatus_WhenStatusIsFinalized() {
        // Given
        String eventGuid = "test-guid";
        String exportDate = "2024-03-01T10:00:00Z";
        EventStatusRequest request = new EventStatusRequest("Finalized", exportDate, 1);
        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        verify(eventRepository).save(argThat(event ->
                event.getCatalogExportDate() != null
                        && event.getIsOlrEnabled()
                        && "Finalized".equals(event.getStatus())
        ));
    }

    @Test
    void updateEventStatus_WhenStatusIsDefault() {
        // Given
        String eventGuid = "test-guid";
        EventStatusRequest request = new EventStatusRequest("Active", null, 1);
        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .catalogExportDate(Timestamp.valueOf("2024-03-01 10:00:00"))
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        verify(eventRepository).save(argThat(event ->
                event.getCatalogExportDate() == null
                        && "Active".equals(event.getStatus())
        ));
    }

    @Test
    void updateEventStatus_WhenStatusIsNull() {
        // Given
        String eventGuid = "test-guid";
        EventStatusRequest request = new EventStatusRequest(null, null, 1);
        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .lastModifiedDate(new Timestamp(System.currentTimeMillis()))  // Set initial last modified date
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenAnswer(invocation -> {
            EventEntity savedEntity = invocation.getArgument(0);
            // Ensure the saved entity has a last modified date
            if (savedEntity.getLastModifiedDate() == null) {
                savedEntity.setLastModifiedDate(new Timestamp(System.currentTimeMillis()));
            }
            return savedEntity;
        });

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);

        ArgumentCaptor<EventEntity> entityCaptor = ArgumentCaptor.forClass(EventEntity.class);
        verify(eventRepository).save(entityCaptor.capture());

        EventEntity savedEntity = entityCaptor.getValue();
        assertNotNull(savedEntity.getLastModifiedDate(), "Last modified date should be set");
        assertEquals(eventGuid, savedEntity.getGuid(), "GUID should match");
        assertEquals("2024101", savedEntity.getSaleNumber(), "Sale number should match");

        // Verify the last modified date was updated
        Timestamp originalTimestamp = mockEvent.getLastModifiedDate();
        Timestamp updatedTimestamp = savedEntity.getLastModifiedDate();
        assertTrue(updatedTimestamp.after(originalTimestamp) || updatedTimestamp.equals(originalTimestamp),
                "Last modified date should be updated or remain the same");
    }

    @Test
    void updateEvent_SetsTalBidAutoOpenDate_WhenAllConditionsMet() {
        // Arrange
        EventRequest updateRequest = createSampleEventRequest().toBuilder()
                .primaryClassification("Agriculture")
                .schedulingStatus("Published") // new status
                .locationType(EventLocationType.ON_THE_FARM)
                .auctionFormats(List.of(SellingFormat.TAL))
                .startDateTime("2025-03-15")
                .timezone("America/Chicago")
                .talBidAutoOpenDate(null)
                .build();

        EventEntity existingEvent = EventEntity.builder()
                .guid("12345")
                .saleNumber("SALE-67890")
                .primaryClassification("Agriculture")
                .schedulingStatus(SchedulingStatus.Confirmed)
                .locationType(EventLocationType.ON_THE_FARM)
                .sellingFormats("TAL")
                .eventStartDate(Timestamp.valueOf("2025-03-15 08:00:00"))
                .timezone("America/Chicago")
                .build();

        when(eventRepository.findByGuid("12345")).thenReturn(Optional.of(existingEvent));
        when(eventRepository.save(any(EventEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        EventResponse response = eventService.updateEvent("12345", updateRequest);

        // Assert
        assertNotNull(response);
        assertEquals("12345", response.eventGuid());

        ArgumentCaptor<EventEntity> entityCaptor = ArgumentCaptor.forClass(EventEntity.class);
        verify(eventRepository).save(entityCaptor.capture());
        EventEntity savedEvent = entityCaptor.getValue();

        assertNotNull(savedEvent.getTalBidAutoOpenDate(), "TAL Bid Auto Open Date should be set");

        ZonedDateTime actual = savedEvent.getTalBidAutoOpenDate().toInstant().atZone(ZoneId.of("America/Chicago"));
        ZonedDateTime expected = ZonedDateTime.of(LocalDate.of(2025, 3, 10), LocalTime.of(12, 0), ZoneId.of("America/Chicago"));

        assertEquals(expected.toInstant(), actual.toInstant(), "TAL open date should be 5 days before start date at 12 PM local time");
    }

    @Test
    void getEventDetails_throwsBusinessException_whenFromEntityReturnsNull() {
        // Given
        String eventGuid = "12345";
        EventEntity eventEntity = new EventEntity();
        eventEntity.setGuid(eventGuid);

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(eventEntity));

        try (MockedStatic<EventDetailsResponse> mockedResponse = org.mockito.Mockito.mockStatic(EventDetailsResponse.class)) {
            mockedResponse.when(() -> EventDetailsResponse.fromEntity(any(EventEntity.class), any(PlacesServiceClient.class)))
                    .thenReturn(null);

            assertThatThrownBy(() -> eventService.getEventDetails(eventGuid))
                    .isInstanceOf(BusinessException.class)
                    .hasMessage("Error while fetching event details");

        }
    }

    @Test
    void testExtractFieldValues_shouldReturnExpectedMap() {
        // Given
        EventEntity entity = new EventEntity();
        entity.setName("Auction 1");

        // Mock AuditHelper.extractFieldValues
        try (MockedStatic<AuditHelper> mockedAuditHelper = mockStatic(AuditHelper.class)) {
            Map<String, Object> mockValues = new HashMap<>();
            mockValues.put("name", "Auction 1");
            // Add more fields to simulate a real response
            mockValues.put("guid", "test-guid");
            mockValues.put("isRegistrationOpen", true);

            mockedAuditHelper.when(() -> AuditHelper.extractFieldValues(any(EventEntity.class)))
                .thenReturn(mockValues);

            // When
            Map<String, Object> result = eventService.getAuditValues(entity);

            // Then
            assertNotNull(result);
            assertEquals("Auction 1", result.get("name"));
            assertEquals("test-guid", result.get("guid"));
            assertEquals(true, result.get("isRegistrationOpen"));
        }
    }

    @Test
    void updateEventStatus_WhenStatusIsFinalized_ShouldAuditChanges() {
        // Given
        String eventGuid = "test-guid";
        String exportDate = "2024-03-01T10:00:00Z";
        EventStatusRequest request = new EventStatusRequest("Finalized", exportDate, 1);

        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .status("Draft")
                .isOlrEnabled(false)
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        verify(eventRepository).save(argThat(event ->
                event.getCatalogExportDate() != null
                && event.getIsOlrEnabled()
                && "Finalized".equals(event.getStatus())
        ));

        verify(auditService).auditUpdate(argThat(event -> {
            Map<String, Object> oldValues = event.getOldValues();
            return oldValues != null
                && "Draft".equals(oldValues.get("status"))
                && Boolean.FALSE.equals(oldValues.get("isOlrEnabled"))
                && "Finalized".equals(event.getStatus())
                && event.getIsOlrEnabled();
        }));
    }

    @Test
    void updateEventStatus_WhenStatusIsLotNumbering_ShouldAuditChanges() {
        // Given
        String eventGuid = "test-guid";
        EventStatusRequest request = new EventStatusRequest("LotNumbering", null, 1);

        Timestamp oldExportDate = Timestamp.valueOf("2024-03-01 10:00:00");
        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .status("Draft")
                .catalogExportDate(oldExportDate)
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        verify(eventRepository).save(argThat(event ->
                event.getCatalogExportDate() == null
                && "LotNumbering".equals(event.getStatus())
        ));

        verify(auditService).auditUpdate(argThat(event -> {
            Map<String, Object> oldValues = event.getOldValues();
            return oldValues != null
                && "Draft".equals(oldValues.get("status"))
                && oldExportDate.equals(oldValues.get("catalogExportDate"))
                && "LotNumbering".equals(event.getStatus())
                && event.getCatalogExportDate() == null;
        }));
    }

    @Test
    void updateEventStatus_WhenNoChanges_ShouldStillCallAudit() {
        // Given
        String eventGuid = "test-guid";
        EventStatusRequest request = new EventStatusRequest("Active", null, 1);

        EventEntity mockEvent = EventEntity.builder()
                .guid(eventGuid)
                .saleNumber("2024101")
                .status("Active")
                .build();

        when(eventRepository.findByGuid(eventGuid)).thenReturn(Optional.of(mockEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(mockEvent);

        // When
        EventResponse response = eventService.updateEventStatus(eventGuid, request);

        // Then
        assertNotNull(response);
        verify(auditService).auditUpdate(any(EventEntity.class));
    }

    @Test
    void setInitialAuctionSyncDate_Confirmed_viaReflection_shouldSetTimestamp() throws Exception {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setSchedulingStatus(SchedulingStatus.Confirmed);

        Method method = EventService.class.getDeclaredMethod("setInitialAuctionSyncDate", EventEntity.class, SchedulingStatus.class);
        method.setAccessible(true);

        method.invoke(eventService, eventEntity, SchedulingStatus.Proposed);

        assertNotNull(eventEntity.getInitialAuctionSyncDate());
    }

    @Test
    void setInitialAuctionSyncDate_Published_viaReflection_shouldSetTimestamp() throws Exception {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setSchedulingStatus(SchedulingStatus.Published);

        Method method = EventService.class.getDeclaredMethod("setInitialAuctionSyncDate", EventEntity.class, SchedulingStatus.class);
        method.setAccessible(true);

        method.invoke(eventService, eventEntity, SchedulingStatus.Proposed);

        assertNotNull(eventEntity.getInitialAuctionSyncDate());
    }

    @Test
    void setInitialAuctionSyncDate_Canceled_viaReflection_shouldSetTimestamp() throws Exception {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setSchedulingStatus(SchedulingStatus.Cancelled);

        Method method = EventService.class.getDeclaredMethod("setInitialAuctionSyncDate", EventEntity.class, SchedulingStatus.class);
        method.setAccessible(true);

        method.invoke(eventService, eventEntity, SchedulingStatus.Proposed);

        assertNotNull(eventEntity.getInitialAuctionSyncDate());
    }

    @Test
    void shouldReturnEventDetailsResponseList_whenSearchEventIsValid() {
        EventSearchResponse event1 = new EventSearchResponse(
                "test-guid1",
                "20250101",
                "Test Event1",
                "Test Event1"
        );

        EventSearchResponse event2 = new EventSearchResponse(
                "test-guid2",
                "20250202",
                "Test Event2",
                "Test Event2"
        );

        List<EventSearchResponse> mockEvents = Arrays.asList(event1, event2);

        String searchEvent = "Tech";

        when(eventRepository.findEvents(searchEvent)).thenReturn(mockEvents);

        List<EventSearchResponse> result = eventService.findEvents(searchEvent);

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(eventRepository, times(1)).findEvents(searchEvent);

    }

    @Test
    void shouldThrowBusinessException_whenRepositoryThrowsException() {
        String searchEvent = "errorCase";
        when(eventRepository.findEvents(searchEvent)).thenThrow(new RuntimeException("DB error"));

        BusinessException exception = assertThrows(BusinessException.class, () -> {
            eventService.findEvents(searchEvent);
        });
        assertEquals("DB error", exception.getMessage());
    }

    @Test
    void getEventDetailsByAuctionNumber_Success() {
        // Arrange
        String auctionNumber = "2025101";
        EventEntity mockEntity = EventEntity.builder()
                .guid("123e4567-e89b-12d3-a456-************")
                .saleNumber(auctionNumber)
                .eventAdvertisedName("Test Event")
                .eventStartDate(Timestamp.valueOf("2025-03-15 00:00:00"))
                .eventEndDate(Timestamp.valueOf("2025-03-30 00:00:00"))
                .timezone("America/Chicago")
                .build();

        when(eventRepository.findBySaleNumber(auctionNumber)).thenReturn(Optional.of(mockEntity));

        // Act
        EventDetailsResponse response = eventService.getEventDetailsByAuctionNumber(auctionNumber);

        // Assert
        assertNotNull(response);
        assertEquals(mockEntity.getGuid(), response.eventGuid());
        assertEquals(mockEntity.getSaleNumber(), response.saleNumber());
        assertEquals(mockEntity.getEventAdvertisedName(), response.eventAdvertisedName());
        verify(eventRepository).findBySaleNumber(auctionNumber);
    }

    @Test
    void getEventDetailsByAuctionNumber_NotFound() {
        // Arrange
        String auctionNumber = "non-existent-auction-number";
        when(eventRepository.findBySaleNumber(auctionNumber)).thenReturn(Optional.empty());

        // Act & Assert
        DataNotFoundException exception = assertThrows(DataNotFoundException.class,
                () -> eventService.getEventDetailsByAuctionNumber(auctionNumber));
        assertEquals("Event not found with auction number: " + auctionNumber, exception.getMessage());
        verify(eventRepository).findBySaleNumber(auctionNumber);
    }

    @Test
    void shouldThrowExceptionWhenDuplicateAuctionExists() {
        String startDate = "2025-06-26";
        String siteId = "100";
        String timezone = "Asia/Kolkata";
        String expectedSaleNumber = "ABC123";

        // Use doReturn().when() instead of when().thenReturn() to avoid strict stubbing issues
        doReturn(Optional.of(expectedSaleNumber))
                .when(eventRepository).checkAuctionWithStartDateAndSiteId(anyString(), eq(siteId));

        BusinessException ex = assertThrows(BusinessException.class, () ->
                eventService.duplicateAuctionsValidation(startDate, siteId, timezone));

        assertTrue(ex.getMessage().contains("Oracle will reject the auction as duplicate"));
        assertTrue(ex.getMessage().contains(expectedSaleNumber));
    }

    @Test
    void shouldPassWhenNoDuplicateAuctionExists() {
        String startDate = "2025-06-26";
        String siteId = "100";
        String timezone = "Asia/Kolkata";

        // Use doReturn().when() instead of when().thenReturn() to avoid strict stubbing issues
        doReturn(Optional.empty())
                .when(eventRepository).checkAuctionWithStartDateAndSiteId(anyString(), eq(siteId));

        assertDoesNotThrow(() ->
                eventService.duplicateAuctionsValidation(startDate, siteId, timezone));
    }
}
