
package com.rb.capability.auctionmanagement.infra.kafka.processor;

import com.rb.capability.auctionmanagement.client.LegalEntityServiceClient;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.model.LegalEntity;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.entity.SaleNumberAllocation;
import com.rb.capability.auctionmanagement.infra.jpa.repository.SaleNumberAllocationRepository;
import com.rb.capability.auctionmanagement.service.EventService;
import com.rbauction.enterprise.events.models.sale.SaleEvent;
import io.opentelemetry.api.trace.Span;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.rb.capability.common.util.DefaultUtils.createSampleSaleEvent;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EnterpriseEventMessageProcessorTest {

    @Mock
    private EventService eventService;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private LegalEntityServiceClient legalEntityServiceClient;

    @Mock
    private SaleNumberAllocationRepository saleNumberAllocationRepository;

    @Mock
    private Span span;

    @InjectMocks
    private EnterpriseEventMessageProcessor<ConsumerRecord<String, SaleEvent>> processor;

    @Test
    void processRecord_IgnoresNonIpWebSourceSystem() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setSourceSystem("OTHER_SYSTEM");
        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService, never()).findEventByGuid(any());
        verify(eventService, never()).saveEvent(any());
    }

    @Test
    void processRecord_IgnoreMessagesWithoutSaleNumber() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setSaleNumber(null);
        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);

            processor.processRecord(record);

            // Assert
            verify(span).setAttribute("app.skip.process-sale-event", "test-guid");
            verify(eventService, never()).findEventByGuid(any());
            verify(eventService, never()).saveEvent(any());
        }
    }

    @Test
    void processRecord_HandlesNullEventLocations() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setEventLocations(null);
        saleEvent.setLegalEntitySegmentId(101); // Matching segment ID

        LegalEntity.Profile profile = new LegalEntity.Profile("legal-123", 101L, "Test Business Name");
        LegalEntity legalEntity = new LegalEntity(profile);
        when(legalEntityServiceClient.getLegalEntities()).thenReturn(List.of(legalEntity));

        EventEntity existingEvent = new EventEntity();
        when(eventService.findEventByGuid("test-guid")).thenReturn(Optional.of(existingEvent));

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(any());
        verify(placesServiceClient, never()).createLocation(any());
        verify(eventService).saveEvent(argThat(entity ->
                "legal-123".equals(entity.getLegalEntityId())
        ));
    }

    @Test
    void processRecord_HandlesEmptyEventLocations() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setEventLocations(Collections.emptyList());

        EventEntity existingEvent = new EventEntity();
        when(eventService.findEventByGuid("test-guid")).thenReturn(Optional.of(existingEvent));

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(any());
        verify(placesServiceClient, never()).createLocation(any());
    }

    @Test
    void processRecord_ProcessesEventLocations() {
        SaleEvent saleEvent = createSampleSaleEvent();

        EventEntity existingEvent = new EventEntity();
        when(eventService.findEventByGuid("test-guid")).thenReturn(Optional.of(existingEvent));
        when(placesServiceClient.createLocation(any())).thenReturn("location-id-001");

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(any());
        verify(placesServiceClient, times(1)).createLocation(any());
    }

    @Test
    void processRecord_verifySaleNumberAllocationWithNoAllocation() {
        SaleEvent saleEvent = createSampleSaleEvent();

        EventEntity existingEvent = new EventEntity();
        when(eventService.findEventByGuid("test-guid")).thenReturn(Optional.of(existingEvent));
        when(placesServiceClient.createLocation(any())).thenReturn("location-id-001");
        when(saleNumberAllocationRepository.findBySaleNumber(any())).thenReturn(Optional.empty());

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(any());
        verify(placesServiceClient, times(1)).createLocation(any());
        verify(saleNumberAllocationRepository, times(1)).save(any());
    }

    @Test
    void processRecord_verifySaleNumberAllocationWithExistingAllocation() {
        SaleEvent saleEvent = createSampleSaleEvent();

        EventEntity existingEvent = new EventEntity();
        when(eventService.findEventByGuid("test-guid")).thenReturn(Optional.of(existingEvent));
        when(placesServiceClient.createLocation(any())).thenReturn("location-id-001");
        when(saleNumberAllocationRepository.findBySaleNumber(any())).thenReturn(
                Optional.of(SaleNumberAllocation.builder()
                        .id(1L)
                        .saleNumber(2025101)
                        .inUse(false)
                        .year(2025)
                        .type("Non Agriculture Non Charity")
                        .build())
        );

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(any());
        verify(placesServiceClient, times(1)).createLocation(any());
        verify(saleNumberAllocationRepository, times(1)).save(any());
    }


    @Test
    void processRecord_HandlesExceptionInSaveEvent() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setSourceSystem("IP_WEB");

        EventEntity existingEvent = new EventEntity();
        when(eventService.findEventByGuid(any())).thenReturn(Optional.of(existingEvent));
        doThrow(new RuntimeException("Test exception")).when(eventService).saveEvent(any());

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        // Act & Assert
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);

            // Should not throw exception
            assertDoesNotThrow(() -> processor.processRecord(record));

            // Verify exception was recorded
            verify(span).recordException(any(RuntimeException.class));
        }
    }

    @Test
    void processRecord_CreatesNewEntityWhenNotFound() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setSourceSystem("IP_WEB");

        when(eventService.findEventByGuid(any())).thenReturn(Optional.empty());

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(argThat(entity ->
            entity.getGuid().equals(saleEvent.getSaleEventGUID())
        ));
    }

    @Test
    void saveEvent_HandlesExceptionGracefully() {
        SaleEvent saleEvent = createSampleSaleEvent();
        EventEntity eventEntity = new EventEntity();

        doThrow(new RuntimeException("Test exception")).when(eventService).saveEvent(any());

        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);

            // Act & Assert
            assertDoesNotThrow(() -> processor.saveEvent(saleEvent, eventEntity));

            // Verify exception was recorded
            verify(span).recordException(any(RuntimeException.class));
        }
    }

    @Test
    void processRecord_LegalEntityNotFound_ShouldSetNull() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setLegalEntitySegmentId(202);

        when(eventService.findEventByGuid(any())).thenReturn(Optional.of(new EventEntity()));
        when(placesServiceClient.createLocation(any())).thenReturn("location-id");
        when(legalEntityServiceClient.getLegalEntities()).thenReturn(List.of()); // No match

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(argThat(entity ->
                entity.getLegalEntityId() == null
        ));
    }

    @Test
    void processRecord_HandlesNullRecord() {
        assertThrows(NullPointerException.class, () -> processor.processRecord(null));
    }

    @Test
    void processRecord_HandlesNullSaleEvent() {
        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", null);

        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);

            assertDoesNotThrow(() -> processor.processRecord(record));

            verify(span).recordException(any(NullPointerException.class));
            verify(eventService, never()).findEventByGuid(any());
        }
    }

    @Test
    void processRecord_LegalEntityNotFoundBySegmentId_FindsByBusinessName() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setLegalEntitySegmentId(null);
        saleEvent.setLegalEntityName("Test Business");

        LegalEntity.Profile profile = new LegalEntity.Profile("legal-123", null, "Test Business");
        LegalEntity legalEntity = new LegalEntity(profile);
        when(legalEntityServiceClient.getLegalEntities()).thenReturn(List.of(legalEntity));
        when(eventService.findEventByGuid(any())).thenReturn(Optional.of(new EventEntity()));

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        verify(eventService).saveEvent(argThat(entity ->
                "legal-123".equals(entity.getLegalEntityId())
        ));
    }

    @Test
    void processRecord_NoSegmentIdAndNoBusinessName_SetsNull() {
        SaleEvent saleEvent = createSampleSaleEvent();
        saleEvent.setLegalEntitySegmentId(null);
        saleEvent.setBusinessUnitName(null);

        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        processor.processRecord(record);

        // Verify that the saved entity has null legal entity ID
        verify(eventService).saveEvent(argThat(entity ->
                entity.getLegalEntityId() == null
        ));
    }
}
