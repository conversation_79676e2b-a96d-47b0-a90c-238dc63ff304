package com.rb.capability.auctionmanagement.domain.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class AuctionEventStatusTest {

    @Test
    void testEnumValues() {
        // Verify all enum values are properly defined
        assertEquals("NEW", AuctionEventStatus.New.getOperationStatus());
        assertEquals("NEW", AuctionEventStatus.Configuring.getOperationStatus());
        assertEquals("NEW", AuctionEventStatus.Sequencing.getOperationStatus());
        assertEquals("LOT_ASSIGNMENT", AuctionEventStatus.LotNumbering.getOperationStatus());
        assertEquals("GONE_TO_CATALOG", AuctionEventStatus.Finalized.getOperationStatus());
        assertEquals("STARTED", AuctionEventStatus.Active.getOperationStatus());
        assertEquals("CLOSED", AuctionEventStatus.Closed.getOperationStatus());
    }

    @ParameterizedTest
    @MethodSource("provideStatusMappings")
    void testFindByStatusName_ValidStatuses(String input, AuctionEventStatus expected) {
        assertEquals(expected, AuctionEventStatus.findByStatusName(input));
    }

    private static Stream<Arguments> provideStatusMappings() {
        return Stream.of(
            Arguments.of("New", AuctionEventStatus.New),
            Arguments.of("Configuring", AuctionEventStatus.Configuring),
            Arguments.of("Sequencing", AuctionEventStatus.Sequencing),
            Arguments.of("LotNumbering", AuctionEventStatus.LotNumbering),
            Arguments.of("Finalized", AuctionEventStatus.Finalized),
            Arguments.of("Active", AuctionEventStatus.Active),
            Arguments.of("Closed", AuctionEventStatus.Closed)
        );
    }

    @Test
    void testFindByStatusName_InvalidStatus() {
        // Should return New status for invalid input
        assertEquals(AuctionEventStatus.New, AuctionEventStatus.findByStatusName("INVALID_STATUS"));
    }

    @ParameterizedTest
    @NullSource
    void testFindByStatusName_NullStatus(String nullStatus) {
        // Should return New status for null input
        assertEquals(AuctionEventStatus.New, AuctionEventStatus.findByStatusName(nullStatus));
    }

    @Test
    void testEnumName() {
        // Verify enum name() method returns correct values
        assertEquals("New", AuctionEventStatus.New.name());
        assertEquals("Configuring", AuctionEventStatus.Configuring.name());
        assertEquals("Sequencing", AuctionEventStatus.Sequencing.name());
        assertEquals("LotNumbering", AuctionEventStatus.LotNumbering.name());
        assertEquals("Finalized", AuctionEventStatus.Finalized.name());
        assertEquals("Active", AuctionEventStatus.Active.name());
        assertEquals("Closed", AuctionEventStatus.Closed.name());
    }

    @Test
    void testValueOf() {
        // Verify valueOf() method works correctly
        assertEquals(AuctionEventStatus.New, AuctionEventStatus.valueOf("New"));
        assertEquals(AuctionEventStatus.Configuring, AuctionEventStatus.valueOf("Configuring"));
        assertEquals(AuctionEventStatus.Sequencing, AuctionEventStatus.valueOf("Sequencing"));
        assertEquals(AuctionEventStatus.LotNumbering, AuctionEventStatus.valueOf("LotNumbering"));
        assertEquals(AuctionEventStatus.Finalized, AuctionEventStatus.valueOf("Finalized"));
        assertEquals(AuctionEventStatus.Active, AuctionEventStatus.valueOf("Active"));
        assertEquals(AuctionEventStatus.Closed, AuctionEventStatus.valueOf("Closed"));
    }

    @Test
    void testValues() {
        // Verify values() method returns all enum constants
        AuctionEventStatus[] values = AuctionEventStatus.values();
        assertNotNull(values);
        assertEquals(7, values.length);
    }

    @Test
    void testStatusTransitions() {
        // Verify logical status transitions through operation status
        assertEquals("NEW", AuctionEventStatus.New.getOperationStatus());
        assertEquals("NEW", AuctionEventStatus.Configuring.getOperationStatus());
        assertEquals("NEW", AuctionEventStatus.Sequencing.getOperationStatus());
        assertEquals("LOT_ASSIGNMENT", AuctionEventStatus.LotNumbering.getOperationStatus());
        assertEquals("GONE_TO_CATALOG", AuctionEventStatus.Finalized.getOperationStatus());
        assertEquals("STARTED", AuctionEventStatus.Active.getOperationStatus());
        assertEquals("CLOSED", AuctionEventStatus.Closed.getOperationStatus());
    }
}