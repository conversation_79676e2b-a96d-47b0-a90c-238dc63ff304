package com.rb.capability.auctionmanagement.domain.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

class SchedulingStatusTest {

    @Test
    void testEnumValues() {
        // Verify all enum values are properly defined
        assertEquals("PROPOSED", SchedulingStatus.Proposed.enterpriseSchedulingStatus);
        assertEquals("CONFIRMED", SchedulingStatus.Confirmed.enterpriseSchedulingStatus);
        assertEquals("PUBLISHED", SchedulingStatus.Published.enterpriseSchedulingStatus);
        assertEquals("CANCELLED", SchedulingStatus.Cancelled.enterpriseSchedulingStatus);
        assertEquals("CLOSED", SchedulingStatus.Closed.enterpriseSchedulingStatus);
    }

    @ParameterizedTest
    @MethodSource("provideEnterpriseStatusMappings")
    void testGetSchedulingStatusFromEnterpriseMessage_ValidStatuses(String input, SchedulingStatus expected) {
        assertEquals(expected, SchedulingStatus.getSchedulingStatusFromEnterpriseMessage(input));
    }

    private static Stream<Arguments> provideEnterpriseStatusMappings() {
        return Stream.of(
            Arguments.of("PROPOSED", SchedulingStatus.Proposed),
            Arguments.of("CONFIRMED", SchedulingStatus.Confirmed),
            Arguments.of("PUBLISHED", SchedulingStatus.Published),
            Arguments.of("CANCELLED", SchedulingStatus.Cancelled),
            Arguments.of("CLOSED", SchedulingStatus.Closed)
        );
    }

    @ParameterizedTest
    @NullAndEmptySource
    void testGetSchedulingStatusFromEnterpriseMessage_NullOrEmptyStatus(String status) {
        assertNull(SchedulingStatus.getSchedulingStatusFromEnterpriseMessage(status));
    }

    @ParameterizedTest
    @ValueSource(strings = {"INVALID", "UNKNOWN", "DRAFT"})
    void testGetSchedulingStatusFromEnterpriseMessage_InvalidStatus(String status) {
        assertNull(SchedulingStatus.getSchedulingStatusFromEnterpriseMessage(status));
    }

    @Test
    void testEnumName() {
        // Verify enum name() method returns correct values
        assertEquals("Proposed", SchedulingStatus.Proposed.name());
        assertEquals("Confirmed", SchedulingStatus.Confirmed.name());
        assertEquals("Published", SchedulingStatus.Published.name());
        assertEquals("Cancelled", SchedulingStatus.Cancelled.name());
        assertEquals("Closed", SchedulingStatus.Closed.name());
    }

    @Test
    void testValueOf() {
        // Verify valueOf() method works correctly
        assertEquals(SchedulingStatus.Proposed, SchedulingStatus.valueOf("Proposed"));
        assertEquals(SchedulingStatus.Confirmed, SchedulingStatus.valueOf("Confirmed"));
        assertEquals(SchedulingStatus.Published, SchedulingStatus.valueOf("Published"));
        assertEquals(SchedulingStatus.Cancelled, SchedulingStatus.valueOf("Cancelled"));
        assertEquals(SchedulingStatus.Closed, SchedulingStatus.valueOf("Closed"));
    }

    @Test
    void testValueOf_InvalidValue() {
        // Verify valueOf() throws IllegalArgumentException for invalid values
        assertThrows(IllegalArgumentException.class, () -> SchedulingStatus.valueOf("INVALID"));
    }

    @Test
    void testValues() {
        // Verify values() method returns all enum constants
        SchedulingStatus[] values = SchedulingStatus.values();
        assertNotNull(values);
        assertEquals(5, values.length);
        
        // Verify all expected values are present
        assertTrue(containsStatus(values, SchedulingStatus.Proposed));
        assertTrue(containsStatus(values, SchedulingStatus.Confirmed));
        assertTrue(containsStatus(values, SchedulingStatus.Published));
        assertTrue(containsStatus(values, SchedulingStatus.Cancelled));
        assertTrue(containsStatus(values, SchedulingStatus.Closed));
    }

    @Test
    void testEnterpriseSchedulingStatusField() {
        // Verify enterprise scheduling status field is correctly set and immutable
        assertNotNull(SchedulingStatus.Proposed.enterpriseSchedulingStatus);
        assertNotNull(SchedulingStatus.Confirmed.enterpriseSchedulingStatus);
        assertNotNull(SchedulingStatus.Published.enterpriseSchedulingStatus);
        assertNotNull(SchedulingStatus.Cancelled.enterpriseSchedulingStatus);
        assertNotNull(SchedulingStatus.Closed.enterpriseSchedulingStatus);
    }

    @Test
    void testStatusProgression() {
        // Verify logical status progression
        SchedulingStatus[] expectedProgression = {
            SchedulingStatus.Proposed,
            SchedulingStatus.Confirmed,
            SchedulingStatus.Published,
            SchedulingStatus.Closed
        };
        
        for (int i = 0; i < expectedProgression.length - 1; i++) {
            assertTrue(expectedProgression[i].ordinal() < expectedProgression[i + 1].ordinal(),
                    "Status " + expectedProgression[i] + " should come before " + expectedProgression[i + 1]);
        }
    }

    @Test
    void testCancelledStatus() {
        // Verify Cancelled status can be reached from any other status
        assertTrue(SchedulingStatus.Cancelled.ordinal() > SchedulingStatus.Proposed.ordinal());
        assertTrue(SchedulingStatus.Cancelled.ordinal() > SchedulingStatus.Confirmed.ordinal());
        assertTrue(SchedulingStatus.Cancelled.ordinal() > SchedulingStatus.Published.ordinal());
    }

    // Helper method to check if a status exists in array
    private boolean containsStatus(SchedulingStatus[] values, SchedulingStatus status) {
        for (SchedulingStatus value : values) {
            if (value == status) {
                return true;
            }
        }
        return false;
    }
}