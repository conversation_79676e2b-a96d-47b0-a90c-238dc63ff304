package com.rb.capability.auctionmanagement.infra.kafka.consumer;

import com.rb.capability.auctionmanagement.infra.kafka.processor.EnterpriseEventMessageProcessor;
import com.rb.capability.common.launchdarkly.FeatureToggleClient;
import com.rb.essentials.capability.exception.BusinessException;
import com.rbauction.enterprise.events.models.sale.SaleEvent;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class KafkaMessageConsumerTest {

    @Mock
    private EnterpriseEventMessageProcessor enterpriseEventMessageProcessor;

    @Mock
    private FeatureToggleClient featureToggleClient;

    @InjectMocks
    private KafkaMessageConsumer kafkaMessageConsumer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testListenMarsEvents_FeatureToggleEnabled() {
        // Arrange
        SaleEvent saleEvent = new SaleEvent();
        saleEvent.setSaleEventGUID("guid-123");
        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        when(featureToggleClient.isFeatureFlagEnabled(anyString())).thenReturn(false);

        // Act
        kafkaMessageConsumer.listenMarsEvents(record);

        // Assert
        verify(enterpriseEventMessageProcessor, never()).processRecord(any());
    }

    @Test
    void testListenMarsEvents_FeatureToggleDisabled() {
        // Arrange
        SaleEvent saleEvent = new SaleEvent();
        saleEvent.setSaleEventGUID("guid-456");
        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        when(featureToggleClient.isFeatureFlagEnabled(anyString())).thenReturn(true);

        // Act
        kafkaMessageConsumer.listenMarsEvents(record);

        // Assert
        verify(enterpriseEventMessageProcessor, times(1)).processRecord(record);
    }

    @Test
    void testListenMarsEvents_ExceptionHandling() {
        // Arrange
        SaleEvent saleEvent = new SaleEvent();
        saleEvent.setSaleEventGUID("guid-789");
        ConsumerRecord<String, SaleEvent> record = new ConsumerRecord<>("topic", 0, 0L, "key", saleEvent);

        when(featureToggleClient.isFeatureFlagEnabled(anyString())).thenReturn(true);
        doThrow(new RuntimeException("Processing error")).when(enterpriseEventMessageProcessor).processRecord(any());

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () -> kafkaMessageConsumer.listenMarsEvents(record));
        assertTrue(exception.getMessage().contains("Error while  consuming enterprise sale event"));
    }
}
