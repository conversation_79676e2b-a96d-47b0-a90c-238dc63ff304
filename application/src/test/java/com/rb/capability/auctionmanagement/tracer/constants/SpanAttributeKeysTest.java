package com.rb.capability.auctionmanagement.tracer.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class SpanAttributeKeysTest {

    @Test
    void testConstructor() {
        assertDoesNotThrow(() -> new SpanAttributeKeys());

        // Test that we can create multiple instances
        SpanAttributeKeys instance1 = new SpanAttributeKeys();
        SpanAttributeKeys instance2 = new SpanAttributeKeys();
        assertNotNull(instance1);
        assertNotNull(instance2);
    }

    @Test
    void testConstantValues() {
        assertEquals("app.am.status", SpanAttributeKeys.APP_STATUS);
        assertEquals("app.am.error", SpanAttributeKeys.APP_ERROR);
        assertEquals("app.am.action_name", SpanAttributeKeys.APP_ACTION_NAME);
        assertEquals("app.am.request", SpanAttributeKeys.APP_REQUEST);
        assertEquals("app.am.response", SpanAttributeKeys.APP_RESPONSE);
        assertEquals("app.am.external.exception", SpanAttributeKeys.APP_EXTERNAL_EXCEPTION);
    }
}