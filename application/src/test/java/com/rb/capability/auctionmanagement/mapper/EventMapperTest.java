package com.rb.capability.auctionmanagement.mapper;

import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class EventMapperTest {

    private final EventMapper mapper = EventMapper.INSTANCE;

    @Test
    void convertToStartTimeInUtc_WithValidInput() {
        String dateStr = "2025-03-23";
        String timezone = "America/New_York";
        
        Timestamp result = mapper.convertToStartTimeInUtc(dateStr, timezone);
        
        assertNotNull(result);
        ZonedDateTime expectedDate = LocalDate.parse(dateStr)
                .atTime(8, 0)
                .atZone(ZoneId.of(timezone));
        assertEquals(Timestamp.from(expectedDate.toInstant()), result);
    }

    @Test
    void convertToStartTimeInUtc_WithInvalidDate() {
        String dateStr = "2025-13-23"; // Invalid month
        String timezone = "America/New_York";
        
        assertThrows(IllegalArgumentException.class,
                () -> mapper.convertToStartTimeInUtc(dateStr, timezone));
    }

    @ParameterizedTest
    @NullSource
    void convertToStartTimeInUtc_WithNullInputs(String input) {
        assertNull(mapper.convertToStartTimeInUtc(input, "America/New_York"));
        assertNull(mapper.convertToStartTimeInUtc("2025-03-23", input));
    }

    @Test
    void convertToEndTimeInUtc_WithValidInput() {
        String dateStr = "2025-03-23";
        String timezone = "America/New_York";
        
        Timestamp result = mapper.convertToEndTimeInUtc(dateStr, timezone);
        
        assertNotNull(result);
        ZonedDateTime expectedDate = LocalDate.parse(dateStr)
                .atTime(19, 0)
                .atZone(ZoneId.of(timezone));
        assertEquals(Timestamp.from(expectedDate.toInstant()), result);
    }

    @Test
    void convertToEndTimeInUtc_WithInvalidDate() {
        String dateStr = "2025-13-23"; // Invalid month
        String timezone = "America/New_York";
        
        assertThrows(IllegalArgumentException.class,
                () -> mapper.convertToEndTimeInUtc(dateStr, timezone));
    }

    @ParameterizedTest
    @NullSource
    void convertToEndTimeInUtc_WithNullInputs(String input) {
        assertNull(mapper.convertToEndTimeInUtc(input, "America/New_York"));
        assertNull(mapper.convertToEndTimeInUtc("2025-03-23", input));
    }

    @Test
    void additionalClassificationsToString_WithValidList() {
        List<String> classifications = Arrays.asList("Industrial", "Equipment", "Heavy");
        String result = mapper.additionalClassificationsToString(classifications);
        assertEquals("Industrial,Equipment,Heavy", result);
    }

    @Test
    void additionalClassificationsToString_WithEmptyList() {
        List<String> classifications = Collections.emptyList();
        assertNull(mapper.additionalClassificationsToString(classifications));
    }

    @Test
    void additionalClassificationsToString_WithNullList() {
        assertNull(mapper.additionalClassificationsToString(null));
    }

    @Test
    void participationMethodsToString_WithValidList() {
        List<ParticipationMethod> methods = Arrays.asList(
                ParticipationMethod.ONLINE,
                ParticipationMethod.ONSITE
        );
        String result = mapper.participationMethodsToString(methods);
        assertEquals("ONLINE,ONSITE", result);
    }

    @Test
    void participationMethodsToString_WithEmptyList() {
        List<ParticipationMethod> methods = Collections.emptyList();
        assertNull(mapper.participationMethodsToString(methods));
    }

    @Test
    void participationMethodsToString_WithNullList() {
        assertNull(mapper.participationMethodsToString(null));
    }

    @Test
    void sellingFormatsToString_WithValidList() {
        List<SellingFormat> formats = Arrays.asList(
                SellingFormat.TAL,
                SellingFormat.OLR
        );
        String result = mapper.sellingFormatsToString(formats);
        assertEquals("TAL,OLR", result);
    }

    @Test
    void sellingFormatsToString_WithEmptyList() {
        List<SellingFormat> formats = Collections.emptyList();
        assertNull(mapper.sellingFormatsToString(formats));
    }

    @Test
    void sellingFormatsToString_WithNullList() {
        assertNull(mapper.sellingFormatsToString(null));
    }

    @Test
    void currencyListToString_WithValidList() {
        List<String> currencies = Arrays.asList("USD", "CAD", "EUR");
        String result = mapper.currencyListToString(currencies);
        assertEquals("USD,CAD,EUR", result);
    }

    @Test
    void currencyListToString_WithEmptyList() {
        List<String> currencies = Collections.emptyList();
        assertNull(mapper.currencyListToString(currencies));
    }

    @Test
    void currencyListToString_WithNullList() {
        assertNull(mapper.currencyListToString(null));
    }


    @Test
    void convertToCurrentTime_ShouldReturnCurrentTime_WhenFlagTrueAndDateIsNull() {
        String timezone = "America/Los_Angeles";
        Timestamp result = mapper.convertToCurrentTime(null, true, timezone);

        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timezone));
        Timestamp expected = Timestamp.from(now.toInstant());

        assertNotNull(result);

        assertTrue(Math.abs(result.getTime() - expected.getTime()) < 1000);
    }

    @Test
    void convertToCurrentTime_ShouldReturnCurrentTime_WhenFlagTrueAndDateIsZero() {
        String timezone = "America/Los_Angeles";
        Timestamp zeroTimestamp = new Timestamp(0);

        Timestamp result = mapper.convertToCurrentTime(zeroTimestamp, true, timezone);

        assertNotEquals(0, result.getTime());
    }

    @Test
    void convertToCurrentTime_ShouldReturnInputDate_WhenFlagFalse() {
        Timestamp input = new Timestamp(System.currentTimeMillis() - 10000);
        String timezone = "America/New_York";

        Timestamp result = mapper.convertToCurrentTime(input, false, timezone);

        assertEquals(input, result);
    }

    @Test
    void convertToCurrentTime_ShouldReturnNull_WhenFlagFalseAndInputIsNull() {
        String timezone = "Asia/Kolkata";

        Timestamp result = mapper.convertToCurrentTime(null, false, timezone);

        assertNull(result);
    }

    @Test
    void convertToCurrentTime_ShouldReturnNull_WhenFlagNullAndInputIsNull() {
        String timezone = "UTC";

        Timestamp result = mapper.convertToCurrentTime(null, null, timezone);

        assertNull(result);
    }
}