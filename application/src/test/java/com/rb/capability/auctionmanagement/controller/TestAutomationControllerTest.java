package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.request.CreateEventRequest;
import com.rb.capability.auctionmanagement.resource.response.CheckEventResponse;
import com.rb.capability.auctionmanagement.resource.response.CreateEventResponse;
import com.rb.capability.auctionmanagement.service.TestAutomationService;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TestAutomationControllerTest {

    @Mock
    private TestAutomationService testAutomationService;

    @InjectMocks
    private TestAutomationController testAutomationController;

    @Test
    void checkEventAvailability_Success_WithSpan() {
        // Given
        String saleNumber = "2025101";
        CheckEventResponse mockResponse = CheckEventResponse.builder()
                .eventFound(true)
                .build();
        
        when(testAutomationService.checkSaleNumberAvailability(saleNumber)).thenReturn(mockResponse);

        try (MockedStatic<Span> mockedSpan = Mockito.mockStatic(Span.class)) {
            Span span = mock(Span.class);
            mockedSpan.when(Span::current).thenReturn(span);
            when(span.setAttribute(anyString(), anyString())).thenReturn(span);

            // When
            ResponseEntity<CheckEventResponse> response = testAutomationController.checkEventAvailability(saleNumber);

            // Then
            assertTrue(response.getBody().eventFound());
            assertEquals(200, response.getStatusCodeValue());

            // Verify span attributes
            verify(span).setAttribute(eq(APP_ACTION_NAME), eq("check_event_availability"));
            verify(span).setAttribute(eq(APP_REQUEST), eq(saleNumber));
            verify(span).setAttribute(eq(APP_RESPONSE), anyString());
            verify(span).setAttribute(eq(APP_STATUS), eq("SUCCESS"));
        }
    }

    @Test
    void checkEventAvailability_Error_WithSpan() {
        // Given
        String saleNumber = "2025101";
        String errorMessage = "Failed to check event availability";

        when(testAutomationService.checkSaleNumberAvailability(saleNumber))
                .thenThrow(new RuntimeException(errorMessage));

        try (MockedStatic<Span> mockedSpan = Mockito.mockStatic(Span.class)) {
            Span span = mock(Span.class);
            mockedSpan.when(Span::current).thenReturn(span);
            when(span.setAttribute(anyString(), anyString())).thenReturn(span);

            // When & Then
            BusinessException exception = assertThrows(
                BusinessException.class,
                () -> testAutomationController.checkEventAvailability(saleNumber)
            );

            assertEquals(errorMessage, exception.getMessage());

            // Verify span attributes
            verify(span).setAttribute(eq(APP_ACTION_NAME), eq("check_event_availability"));
            verify(span).setAttribute(eq(APP_REQUEST), eq(saleNumber));
            verify(span).setAttribute(eq(APP_EXTERNAL_EXCEPTION), eq(errorMessage));
            verify(span).setAttribute(eq(APP_STATUS), eq("ERROR"));
            verify(span).setAttribute(eq(APP_ERROR), eq(errorMessage));
        }
    }

    @Test
    void checkEventAvailability_NotFound_WithSpan() {
        // Given
        String saleNumber = "2025101";
        CheckEventResponse mockResponse = CheckEventResponse.builder()
                .eventFound(false)
                .build();
        
        when(testAutomationService.checkSaleNumberAvailability(saleNumber)).thenReturn(mockResponse);

        try (MockedStatic<Span> mockedSpan = Mockito.mockStatic(Span.class)) {
            Span span = mock(Span.class);
            mockedSpan.when(Span::current).thenReturn(span);
            when(span.setAttribute(anyString(), anyString())).thenReturn(span);

            // When
            ResponseEntity<CheckEventResponse> response = testAutomationController.checkEventAvailability(saleNumber);

            // Then
            assertFalse(response.getBody().eventFound());
            assertEquals(200, response.getStatusCodeValue());

            // Verify span attributes
            verify(span).setAttribute(eq(APP_ACTION_NAME), eq("check_event_availability"));
            verify(span).setAttribute(eq(APP_REQUEST), eq(saleNumber));
            verify(span).setAttribute(eq(APP_RESPONSE), anyString());
            verify(span).setAttribute(eq(APP_STATUS), eq("SUCCESS"));
        }
    }

    @Test
    void cloneEvent_Success() {
        // Given
        CreateEventRequest request = CreateEventRequest.builder()
                .requestId("req-123")
                .eventParameters(List.of(
                    CreateEventRequest.EventParameters.builder()
                        .saleNumber("2024101")
                        .newSaleNumber("2024102")
                        .eventStartDate("2024-03-15 00:00:00")
                        .eventEndDate("2024-03-30 00:00:00")
                        .build()
                ))
                .build();

        CreateEventResponse expectedResponse = CreateEventResponse.builder()
                .requestId("req-123")
                .clonedEvents(List.of(
                    CreateEventResponse.ClonedEvents.builder()
                        .saleNumber("2024102")
                        .id("1")
                        .gid("new-guid-123")
                        .eventStartDate("2024-03-15 00:00:00")
                        .eventEndDate("2024-03-30 00:00:00")
                        .build()
                ))
                .build();

        when(testAutomationService.cloneEventForTestAutomation(request)).thenReturn(expectedResponse);

        // When
        ResponseEntity<CreateEventResponse> response = testAutomationController.cloneEventForTestAutomation(request);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedResponse, response.getBody());
        verify(testAutomationService).cloneEventForTestAutomation(request);
    }

    @Test
    void cloneEvent_WhenServiceThrowsBusinessException() {
        // Given
        CreateEventRequest request = CreateEventRequest.builder()
                .requestId("req-123")
                .eventParameters(Collections.emptyList())
                .build();

        when(testAutomationService.cloneEventForTestAutomation(request))
                .thenThrow(new BusinessException("Event parameters are empty"));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> testAutomationController.cloneEventForTestAutomation(request));

        assertEquals("Event parameters are empty", exception.getMessage());
        verify(testAutomationService).cloneEventForTestAutomation(request);
    }
}
