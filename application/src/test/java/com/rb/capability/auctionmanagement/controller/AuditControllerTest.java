package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.resource.response.AuditDetailsResponse;
import com.rb.capability.auctionmanagement.service.AuditService;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class AuditControllerTest {

    private MockMvc mockMvc;

    @InjectMocks
    private AuditController auditController;

    @Mock
    private AuditService auditService;

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(auditController).build();
    }


    @Test
    void shouldReturnAuditDetails_whenValidGuidProvided() throws Exception {
        String guid = "auction123";
        List<AuditDetailsResponse> mockResponse = List.of(new AuditDetailsResponse(guid,   "schedulingStatus",
                "Confirmed",
                "Published",
                "adminUser",
                "2024-04-04T12:00:00Z"
        ));

        when(auditService.getAuditDetails(guid)).thenReturn(mockResponse);

        mockMvc.perform(get("/events/{auction_id}/history", guid)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].fieldName").value("schedulingStatus"))
                .andExpect(jsonPath("$[0].oldValue").value("Confirmed"))
                .andExpect(jsonPath("$[0].newValue").value("Published"))
                .andExpect(jsonPath("$[0].changedBy").value("adminUser"));

    }

    @Test
    void shouldReturnEmptyList_whenNoAuditRecordsExist() {
        String guid = "some-guid";

        when(auditService.getAuditDetails(guid)).thenReturn(Collections.emptyList());

        List<AuditDetailsResponse> result = auditService.getAuditDetails(guid);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAuditDetails_WhenMalformedRequest() throws Exception {
        mockMvc.perform(get("/events/history/")  // Missing required path variable
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());  // 404 for missing path variable
    }

    @Test
    void getAuditDetails_shouldThrowBusinessException_whenServiceFails() {
        String guid = "event123";
        when(auditService.getAuditDetails(guid)).thenThrow(new RuntimeException("DB down"));

        try (MockedStatic<Span> mockedSpan = Mockito.mockStatic(Span.class)) {
            Span span = mock(Span.class);
            mockedSpan.when(Span::current).thenReturn(span);

            BusinessException exception = assertThrows(
                    BusinessException.class,
                    () -> auditController.getAuditDetails(guid)
            );

            assertEquals("DB down", exception.getMessage());
            verify(auditService).getAuditDetails(guid);
            verify(span).setAttribute("app.am.external.exception", "DB down");

        }
    }
}



