package com.rb.capability.auctionmanagement.mapper;

import com.rb.capability.auctionmanagement.mapper.EventDetailsResponseMapper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class ResponseMapperTest {

    // Anonymous implementation of the interface so we can test the default method
    private final EventDetailsResponseMapper responseMapper = EventDetailsResponseMapper.INSTANCE;

    @Test
    void testNullInput() {
        List<String> result = responseMapper.stringToAdditionalClassification(null);
        assertNull(result);
    }

    @Test
    void testEmptyString() {
        List<String> result = responseMapper.stringToAdditionalClassification("");
        assertNull(result);
    }

    @Test
    void testSingleValue() {
        List<String> result = responseMapper.stringToAdditionalClassification("OnlyOne");
        assertEquals(List.of("OnlyOne"), result);
    }

    @Test
    void testCommaSeparatedString() {
        List<String> result = responseMapper.stringToAdditionalClassification("A,B,C");
        assertEquals(Arrays.asList("A", "B", "C"), result);
    }

    @Test
    void testCommaSeparatedStringWithSpaces() {
        List<String> result = responseMapper.stringToAdditionalClassification(" A , B , C ");
        assertEquals(Arrays.asList(" A ", " B ", " C "), result); // whitespace preserved
    }
}
