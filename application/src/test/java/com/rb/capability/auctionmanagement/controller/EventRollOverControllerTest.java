package com.rb.capability.auctionmanagement.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload.EventRolloverRequest;
import com.rb.capability.auctionmanagement.resource.response.EventRolloverResponse;
import com.rb.capability.auctionmanagement.resource.response.ValidationResponse;
import com.rb.capability.auctionmanagement.service.EventRollOverService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class EventRollOverControllerTest {

    @Mock
    private EventRollOverService eventRollOverService;

    @InjectMocks
    private EventRollOverController controller;

    private MockMvc mockMvc;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void testConfirmRollOver_success() throws Exception {
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                List.of(new EventRolloverRequest(null, 2025001)),
                "tester"
        );

        ValidationResponse response = new ValidationResponse(true, "Validation passed");
        when(eventRollOverService.validateRolloverBatch(any())).thenReturn(response);

        mockMvc.perform(post("/events/rollover/confirm-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Validation passed"));
    }

    @Test
    void testBatchRollOver_success() throws Exception {
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                List.of(new EventRolloverRequest(null, 2025002)),
                "admin"
        );

        EventRolloverResponse mockResponse = new EventRolloverResponse(1, 1, List.of());
        when(eventRollOverService.prepareEventRollOver(any())).thenReturn(mockResponse);

        mockMvc.perform(post("/events/rollover/batch-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.successCount").value(1))
                .andExpect(jsonPath("$.totalCount").value(1))
                .andExpect(jsonPath("$.failedAuctions").isEmpty());
    }
}
