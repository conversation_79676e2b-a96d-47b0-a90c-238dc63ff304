package com.rb.capability.auctionmanagement.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload.EventRolloverRequest;
import com.rb.capability.auctionmanagement.resource.response.EventRolloverResponse;
import com.rb.capability.auctionmanagement.resource.response.ValidationResponse;
import com.rb.capability.auctionmanagement.service.EventRollOverService;
import com.rb.capability.common.exception.GlobalExceptionHandler;
import com.rb.essentials.capability.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class EventRollOverControllerTest {

    @Mock
    private EventRollOverService eventRollOverService;

    @InjectMocks
    private EventRollOverController controller;

    private MockMvc mockMvc;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders
                .standaloneSetup(controller)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();

        objectMapper = new ObjectMapper();
    }

    @Test
    void testConfirmRollOver_success() throws Exception {
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                List.of(new EventRolloverRequest("123", 2025001)),
                "tester"
        );

        ValidationResponse response = new ValidationResponse(true, "Validation passed");
        when(eventRollOverService.validateRolloverBatch(any())).thenReturn(response);

        mockMvc.perform(post("/events/rollover/confirm-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Validation passed"));
    }


    @Test
    void testBatchRollOver_success() throws Exception {
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                List.of(new EventRolloverRequest("123", 2025002)),
                "admin"
        );

        EventRolloverResponse mockResponse = new EventRolloverResponse(1, 1, List.of());
        when(eventRollOverService.prepareEventRollOver(any())).thenReturn(mockResponse);

        mockMvc.perform(post("/events/rollover/batch-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.successCount").value(1))
                .andExpect(jsonPath("$.totalCount").value(1))
                .andExpect(jsonPath("$.failedAuctions").isEmpty());
    }

    @Test
    void confirmRollOver_WhenServiceThrowsBusinessException() throws Exception {
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                List.of(new EventRolloverRequest("123", 2025002)),
                "admin"
        );

        when(eventRollOverService.validateRolloverBatch(any(EventRolloverBatchPayload.class)))
                .thenThrow(new BusinessException("Error validating rollover batch"));

        mockMvc.perform(post("/events/rollover/confirm-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Error validating rollover batch"));

        verify(eventRollOverService).validateRolloverBatch(any(EventRolloverBatchPayload.class));
    }

    @Test
    void confirmRollOver_WhenAuctionsListIsEmpty_ShouldReturnBadRequest() throws Exception {

        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                Collections.emptyList(),
                "admin"
        );

        mockMvc.perform(post("/events/rollover/confirm-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void batchRollOver_WhenServiceThrowsBusinessException() throws Exception {
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(
                List.of(new EventRolloverRequest("123", 2025002)),
                "admin"
        );

        when(eventRollOverService.prepareEventRollOver(any(EventRolloverBatchPayload.class)))
                .thenThrow(new BusinessException("Error Occured in batch rollover"));

        mockMvc.perform(post("/events/rollover/batch-rollover")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Error Occured in batch rollover"));

        verify(eventRollOverService).prepareEventRollOver(any(EventRolloverBatchPayload.class));
    }

}
