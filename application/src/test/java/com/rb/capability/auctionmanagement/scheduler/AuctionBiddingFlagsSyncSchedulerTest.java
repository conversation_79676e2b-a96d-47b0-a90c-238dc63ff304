package com.rb.capability.auctionmanagement.scheduler;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.service.AuctionBiddingFlagsCheckService;
import io.opentelemetry.api.trace.Span;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuctionBiddingFlagsSyncSchedulerTest {

    @Mock
    private EventRepository eventRepository;

    @Mock
    private AuctionBiddingFlagsCheckService auctionBiddingFlagsCheckService;

    @Mock
    private Span span;

    @InjectMocks
    private AuctionBiddingFlagsSyncScheduler scheduler;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(scheduler, "isAuctionSyncBiddingFlagsEnabled", true);
    }

    @Test
    void autoSync_WhenSyncIsDisabled_ShouldReturnEarly() {
        // Given
        ReflectionTestUtils.setField(scheduler, "isAuctionSyncBiddingFlagsEnabled", false);

        // When
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);
            scheduler.autoSync();
        }

        // Then
        verify(eventRepository, never()).findAllUpcomingEventsBiddingFlags();
        verify(auctionBiddingFlagsCheckService, never()).syncBiddingFlagsUnMatchedData(anyList());
    }

    @Test
    void autoSync_WhenFeatureFlagEnabled_ShouldNotBeChecked() {
        // Given
        // No feature flag check in the actual implementation

        // When
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);
            scheduler.autoSync();
        }

        // Then
        // We expect the repository to be called since there's no feature flag check
        verify(eventRepository).findAllUpcomingEventsBiddingFlags();
    }

    @Test
    void autoSync_WhenNoEventsFound_ShouldNotProcessAnything() {
        // Given
        // Remove the unnecessary feature flag stubbing
        when(eventRepository.findAllUpcomingEventsBiddingFlags()).thenReturn(new ArrayList<>());

        // When
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);
            scheduler.autoSync();
        }

        // Then
        verify(eventRepository).findAllUpcomingEventsBiddingFlags();
        verify(auctionBiddingFlagsCheckService, never()).syncBiddingFlagsUnMatchedData(anyList());
    }

    @Test
    void autoSync_WhenEventsFound_ShouldProcessThem() throws Exception {
        // Given
        List<EventEntity> mockEvents = new ArrayList<>();
        mockEvents.add(new EventEntity());
        when(eventRepository.findAllUpcomingEventsBiddingFlags()).thenReturn(mockEvents);
        
        // Mock ExecutorService to avoid actual thread creation in tests
        ExecutorService mockExecutorService = mock(ExecutorService.class);
        // Only keep necessary stubbings
        when(mockExecutorService.awaitTermination(anyLong(), any(TimeUnit.class))).thenReturn(true);
        
        try (MockedStatic<Executors> executorsMockedStatic = mockStatic(Executors.class);
             MockedStatic<Span> spanMockedStatic = mockStatic(Span.class)) {
            
            executorsMockedStatic.when(() -> Executors.newFixedThreadPool(10))
                    .thenReturn(mockExecutorService);
            spanMockedStatic.when(Span::current).thenReturn(span);
            
            // When
            scheduler.autoSync();
            
            // Then
            verify(eventRepository).findAllUpcomingEventsBiddingFlags();
            verify(mockExecutorService).submit(any(Runnable.class));
            verify(mockExecutorService).shutdown();
            verify(mockExecutorService).awaitTermination(30, TimeUnit.MINUTES);
        }
    }

    @Test
    void autoSync_WhenExceptionOccurs_ShouldHandleGracefully() {
        // Given
        when(eventRepository.findAllUpcomingEventsBiddingFlags()).thenThrow(new RuntimeException("Test exception"));

        // When
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);
            scheduler.autoSync();
            
            // Then
            verify(span).setAttribute("app.scheduler.error", "Test exception");
            verify(span).recordException(any(RuntimeException.class));
        }
    }
}