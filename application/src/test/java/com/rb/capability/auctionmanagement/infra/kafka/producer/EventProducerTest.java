package com.rb.capability.auctionmanagement.infra.kafka.producer;

import com.rb.capability.auctionmanagement.client.ContactType;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.LegalEntityServiceClient;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.model.LegalEntity;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.resource.response.*;
import com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys;
import com.rb.capability.common.utils.EventDateUtils;
import com.rbauction.enterprise.events.models.marketplace.capability.auctionmanagement.Auction;
import com.rbauction.enterprise.events.models.marketplace.capability.auctionmanagement.AuctionContent;
import io.opentelemetry.api.trace.Span;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventProducerTest {

    @Mock
    private KafkaTemplate<String, Object> auctionEventKafkaTemplate;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private LegalEntityServiceClient legalEntityServiceClient;

    @Mock
    private Span span;

    @InjectMocks
    private EventProducer eventProducer;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(eventProducer, "topic", "test-topic");
    }


    

    
    @Test
    void publishAuctionEvent_Success() {
        // Arrange
        EventDetailsResponse eventResponse = createTestEventDetailsResponse();
        CompletableFuture<SendResult<String, Object>> future = new CompletableFuture<>();

        when(auctionEventKafkaTemplate.send(anyString(), anyString(), any(Auction.class))).thenReturn(future);
        
        // Act
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(span);
            
            eventProducer.publishAuctionEvent(eventResponse);
            
            // Assert
            verify(auctionEventKafkaTemplate).send(eq("test-topic"), eq(eventResponse.eventGuid()), any(Auction.class));
        }
    }

    @Test
    void publishAuctionEvent_HandlesNullResponse() {
        // Arrange
        EventDetailsResponse eventResponse = null;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> eventProducer.publishAuctionEvent(eventResponse));
    }

    @Test
    void publishAuctionEvent_HandlesEmptyGuid() {
        // Arrange
        EventDetailsResponse baseResponse = createTestEventDetailsResponse();
        EventDetailsResponse eventResponse = new EventDetailsResponse(
                "",  // empty GUID
                baseResponse.locationId(),
                baseResponse.siteId(),
                baseResponse.saleNumber(),
                baseResponse.sourceAuctionNumber(), // Add this field to match the 41 arguments
                baseResponse.name(),
                baseResponse.eventAdvertisedName(),
                baseResponse.numberOfDays(),
                baseResponse.brand(),
                baseResponse.timezone(),
                baseResponse.legalEntityId(),
                baseResponse.eventStartDate(),
                baseResponse.eventEndDate(),
                baseResponse.currency(),
                baseResponse.primaryClassification(),
                baseResponse.schedulingStatus(),
                baseResponse.status(),
                baseResponse.sellingFormats(),
                baseResponse.waysToParticipate(),
                baseResponse.registrationOpen(),
                baseResponse.registrationAutoOpenDate(),
                baseResponse.talBidOpen(),
                baseResponse.talBidAutoOpenDate(),
                baseResponse.priorityBidOpen(),
                baseResponse.priorityBidAutoOpenDate(),
                baseResponse.olrEnabled(),
                baseResponse.createdDate(),
                baseResponse.lastModifierName(),
                baseResponse.lastModifiedDate(),
                baseResponse.location(),
                baseResponse.site(),
                baseResponse.isRbMarketplaceSale(),
                baseResponse.businessUnit(),
                baseResponse.allowIpInspection(),
                baseResponse.additionalClassification(),
                baseResponse.locationType(),
                baseResponse.createdBy(),
                baseResponse.privateTreaty(),
                baseResponse.siteConfiguration(),
                baseResponse.notes(),
                baseResponse.initialAuctionSyncDate(),
                baseResponse.eventFinalizedDate()
        );

        // Mock Span
        Span mockSpan = mock(Span.class);
        when(mockSpan.setAttribute(anyString(), anyString())).thenReturn(mockSpan);
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(mockSpan);

            // Act
            eventProducer.publishAuctionEvent(eventResponse);

            // Assert
            verify(auctionEventKafkaTemplate).send(eq("test-topic"), eq(""), any(Auction.class));
            verify(mockSpan).setAttribute(eq(SpanAttributeKeys.EVENT_ID), eq(""));
        }
    }

    @Test
    void getEventDetailsToPublish_WhenCurrencyIsNull_ShouldReturnNull() {
        // Arrange
        EventDetailsResponse baseResponse = createTestEventDetailsResponse();
        EventDetailsResponse event = new EventDetailsResponse(
                baseResponse.eventGuid(),
                baseResponse.locationId(),
                baseResponse.siteId(),
                baseResponse.saleNumber(),
                baseResponse.sourceAuctionNumber(), // Add this field to match the 41 arguments
                baseResponse.name(),
                baseResponse.eventAdvertisedName(),
                baseResponse.numberOfDays(),
                baseResponse.brand(),
                baseResponse.timezone(),
                baseResponse.legalEntityId(),
                baseResponse.eventStartDate(),
                baseResponse.eventEndDate(),
                null,  // Setting currency to null
                baseResponse.primaryClassification(),
                baseResponse.schedulingStatus(),
                baseResponse.status(),
                baseResponse.sellingFormats(),
                baseResponse.waysToParticipate(),
                baseResponse.registrationOpen(),
                baseResponse.registrationAutoOpenDate(),
                baseResponse.talBidOpen(),
                baseResponse.talBidAutoOpenDate(),
                baseResponse.priorityBidOpen(),
                baseResponse.priorityBidAutoOpenDate(),
                baseResponse.olrEnabled(),
                baseResponse.createdDate(),
                baseResponse.lastModifierName(),
                baseResponse.lastModifiedDate(),
                baseResponse.location(),
                baseResponse.site(),
                baseResponse.isRbMarketplaceSale(),
                baseResponse.businessUnit(),
                baseResponse.allowIpInspection(),
                baseResponse.additionalClassification(),
                baseResponse.locationType(),
                baseResponse.createdBy(),
                baseResponse.privateTreaty(),
                baseResponse.siteConfiguration(),
                baseResponse.notes(),
                new Timestamp(System.currentTimeMillis()),
                new Timestamp(System.currentTimeMillis())
        );

        // Act
        Auction result = eventProducer.getEventDetailsToPublish(event);

        // Assert
        assertNull(result);
    }
    
    @Test
    void getEventDetailsToPublish_WhenCurrencyIsEmpty_ShouldReturnNull() {
        // Arrange
        EventDetailsResponse baseResponse = createTestEventDetailsResponse();
        EventDetailsResponse event = new EventDetailsResponse(
                baseResponse.eventGuid(),
                baseResponse.locationId(),
                baseResponse.siteId(),
                baseResponse.saleNumber(),
                baseResponse.sourceAuctionNumber(), // Add this field to match the 41 arguments
                baseResponse.name(),
                baseResponse.eventAdvertisedName(),
                baseResponse.numberOfDays(),
                baseResponse.brand(),
                baseResponse.timezone(),
                baseResponse.legalEntityId(),
                baseResponse.eventStartDate(),
                baseResponse.eventEndDate(),
                List.of(),  // Setting currency to empty list
                baseResponse.primaryClassification(),
                baseResponse.schedulingStatus(),
                baseResponse.status(),
                baseResponse.sellingFormats(),
                baseResponse.waysToParticipate(),
                baseResponse.registrationOpen(),
                baseResponse.registrationAutoOpenDate(),
                baseResponse.talBidOpen(),
                baseResponse.talBidAutoOpenDate(),
                baseResponse.priorityBidOpen(),
                baseResponse.priorityBidAutoOpenDate(),
                baseResponse.olrEnabled(),
                baseResponse.createdDate(),
                baseResponse.lastModifierName(),
                baseResponse.lastModifiedDate(),
                baseResponse.location(),
                baseResponse.site(),
                baseResponse.isRbMarketplaceSale(),
                baseResponse.businessUnit(),
                baseResponse.allowIpInspection(),
                baseResponse.additionalClassification(),
                baseResponse.locationType(),
                baseResponse.createdBy(),
                baseResponse.privateTreaty(),
                baseResponse.siteConfiguration(),
                baseResponse.notes(),
                new Timestamp(System.currentTimeMillis()),
                new Timestamp(System.currentTimeMillis())
        );
        
        // Act
        Auction result = eventProducer.getEventDetailsToPublish(event);
        
        // Assert
        assertNull(result);
    }
    
    // Helper method to create a test event entity
    private EventEntity createTestEventEntity() {
        EventEntity event = new EventEntity();
        event.setGuid("test-guid");
        event.setSaleNumber("20250001");
        event.setEventAdvertisedName("Test Auction");
        event.setBrand("RB");
        event.setTimezone("America/New_York");
        event.setEventStartDate(new Timestamp(System.currentTimeMillis()));
        event.setEventEndDate(new Timestamp(System.currentTimeMillis() + 86400000));
        event.setPrimaryClassification("Equipment");
        event.setCurrency("USD");
        event.setSchedulingStatus(SchedulingStatus.Published);
        event.setStatus("NEW");
        event.setSellingFormats("Online,Live");
        event.setWaysToParticipate("Online,In-Person");
        event.setIsRegistrationOpen(true);
        event.setSiteId("site-123");
        event.setLocationId("location-123");
        event.setBusinessUnitCode("123");
        event.setBusinessUnitName("Test Business Unit");
        event.setLegalEntityId("legal-123");
        return event;
    }

    @Test
    void buildLocationInfo_WhenLocationResponseExists_ShouldReturnLocationInfo() {
        // Arrange
        EventEntity event = new EventEntity();
        event.setLocationId("location-123");
        event.setLegalEntityId("legal-123");
        event.setCurrency("USD");
        event.setTimezone("America/New_York");
        event.setEventStartDate(new Timestamp(System.currentTimeMillis()));
        event.setEventEndDate(new Timestamp(System.currentTimeMillis() + 86400000));
        event.setSchedulingStatus(SchedulingStatus.Published);
        event.setStatus("NEW");
        event.setSellingFormats("Online,Live");
        event.setWaysToParticipate("Online,In-Person");
        event.setAdditionalClassification("Industrial,Equipment"); // Set additionalClassification

        LocationResponse locationResponse = new LocationResponse(
                "location-123",
                "PERMANENT_SITE",
                "Test Location",
                "123 Test St",
                "Test City",
                "TS",
                "Test State",
                "TC",
                "Test Country",
                "12345",
                BigDecimal.valueOf(33.4255),
                BigDecimal.valueOf(-112.1711),
                List.of(ContactDetailsResponse.builder()
                        .type(ContactType.PRIMARY)
                        .firstName("John")
                        .lastName("Doe")
                        .phoneNumber("************")
                        .faxNumber("************")
                        .email("<EMAIL>")
                        .build())
        );

        // Setup legal entity mock
        LegalEntity.Profile profile = new LegalEntity.Profile("legal-123", 101L, "Test Legal Entity");
        LegalEntity legalEntity = new LegalEntity(profile);
        when(legalEntityServiceClient.getLegalEntities()).thenReturn(List.of(legalEntity));

        // Create and configure the span mock
        Span mockSpan = mock(Span.class);
        when(mockSpan.setAttribute(eq("app.location.id"), anyString())).thenReturn(mockSpan);

        // Act
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(mockSpan);

            Auction auction = eventProducer.getEventDetailsToPublish(new EventDetailsResponse(
                    event.getGuid(),
                    event.getLocationId(),
                    event.getSiteId(),
                    event.getSaleNumber(),
                    "sourceAuctionNumber", // Add sourceAuctionNumber
                    event.getName(),
                    event.getEventAdvertisedName(),
                    EventDateUtils.mapNumberOfDays(event),
                    event.getBrand(),
                    event.getTimezone(),
                    event.getLegalEntityId(),
                    event.getEventStartDate(),
                    event.getEventEndDate(),
                    List.of("USD"),
                    event.getPrimaryClassification(),
                    event.getSchedulingStatus() != null ? event.getSchedulingStatus().name() : "NEW",
                    event.getStatus(),
                    List.of(SellingFormat.TAL),
                    List.of(ParticipationMethod.ONLINE, ParticipationMethod.ONSITE),
                    event.getIsRegistrationOpen(),
                    event.getRegistrationAutoOpenDate(),
                    event.getIsTalBidOpen(),
                    event.getTalBidAutoOpenDate(),
                    event.getIsPriorityBidOpen(),
                    event.getPriorityBidAutoOpenDate(),
                    event.getIsOlrEnabled(),
                    event.getCreatedDate(),
                    event.getLastModifierName(),
                    event.getLastModifiedDate(),
                    locationResponse,
                    null,
                    event.getIsRbMarketplaceSale(),
                    new BusinessUnit(event.getBusinessUnitName(), event.getBusinessUnitCode()),
                    event.getAllowIpInspection(),
                    event.getAdditionalClassification() != null
                            ? Arrays.asList(event.getAdditionalClassification().split(",")) : null,
                    event.getLocationType(),
                    event.getCreatedBy(),
                    event.getIsPrivateTreaty(),
                    event.getSiteConfiguration(),
                    event.getNotes(),
                    event.getInitialAuctionSyncDate(),
                    event.getCatalogExportDate()
            ));

            // Assert
            assertNotNull(auction);
            assertNotNull(auction.getContent().getLocation());
            assertEquals("location-123", auction.getContent().getLocation().getId());
            assertEquals("PERMANENT_SITE", auction.getContent().getLocation().getType());
            assertEquals("Test Location", auction.getContent().getLocation().getName());
            assertEquals("123 Test St", auction.getContent().getLocation().getStreetAddress1());
            assertEquals("Test City", auction.getContent().getLocation().getCity());
            assertEquals("TS", auction.getContent().getLocation().getStateProvinceCode());
            assertEquals("Test State", auction.getContent().getLocation().getStateProvince());
            assertEquals("TC", auction.getContent().getLocation().getCountryCode());
            assertEquals("Test Country", auction.getContent().getLocation().getCountry());
            assertEquals("12345", auction.getContent().getLocation().getPostalCode());
            assertEquals(33.4255, auction.getContent().getLocation().getLatitude());
            assertEquals(-112.1711, auction.getContent().getLocation().getLongitude());

            // Verify contacts
            assertNotNull(auction.getContent().getLocation().getContacts());
            assertEquals(1, auction.getContent().getLocation().getContacts().size());
            assertEquals("PRIMARY", auction.getContent().getLocation().getContacts().get(0).getType());
            assertEquals("John", auction.getContent().getLocation().getContacts().get(0).getFirstName());
            assertEquals("Doe", auction.getContent().getLocation().getContacts().get(0).getLastName());
            assertEquals("************", auction.getContent().getLocation().getContacts().get(0).getPhoneNumber());
            assertEquals("************", auction.getContent().getLocation().getContacts().get(0).getFaxNumber());
            assertEquals("<EMAIL>", auction.getContent().getLocation().getContacts().get(0).getEmail());
            assertEquals("Test Legal Entity", auction.getContent().getLegalEntityName());

            // Verify span attribute was set
            verify(mockSpan).setAttribute("app.location.id", "location-123");
        }
    }

    @Test
    void getBusinessNameFromLegalEntityId_WhenIdIsNullOrBlank_ShouldReturnNull() {
        String result1 = ReflectionTestUtils.invokeMethod(eventProducer, "getBusinessNameFromLegalEntityId", (String) null);
        assertNull(result1);

        String result2 = ReflectionTestUtils.invokeMethod(eventProducer, "getBusinessNameFromLegalEntityId", "");
        assertNull(result2);
    }

    @Test
    void getBusinessNameFromLegalEntityId_WhenExceptionOccurs_ShouldReturnNull() {
        when(legalEntityServiceClient.getLegalEntities()).thenThrow(new RuntimeException("Simulated failure"));

        String result = ReflectionTestUtils.invokeMethod(eventProducer, "getBusinessNameFromLegalEntityId", "legal-123");
        assertNull(result);
    }

    // Helper method to create test EventDetailsResponse
    private EventDetailsResponse createTestEventDetailsResponse() {
        LocationResponse locationResponse = new LocationResponse(
                "location-123",
                "PERMANENT_SITE",
                "Test Location",
                "123 Test St",
                "Test City",
                "TS",
                "Test State",
                "TC",
                "Test Country",
                "12345",
                BigDecimal.valueOf(33.4255),
                BigDecimal.valueOf(-112.1711),
                List.of(ContactDetailsResponse.builder()
                        .type(ContactType.PRIMARY)
                        .firstName("John")
                        .lastName("Doe")
                        .phoneNumber("************")
                        .faxNumber("************")
                        .email("<EMAIL>")
                        .build())
        );

        SiteResponse siteResponse = new SiteResponse("site-123", "Test Site", "Test Site");

        return new EventDetailsResponse(
                "test-guid",                            // 1. eventGuid
                "location-123",                         // 2. locationId
                "site-123",                             // 3. siteId
                "20250001",                             // 4. saleNumber
                "20250001-SOURCE",                      // 5. sourceAuctionNumber (added)
                "Test Event",                           // 6. name
                "Test Auction",                         // 7. eventAdvertisedName
                1,                                      // 8. numberOfDays
                "RB",                                   // 9. brand
                "America/New_York",                     // 10. timezone
                "legal-123",                            // 11. legalEntityId
                new Timestamp(System.currentTimeMillis()), // 12. eventStartDate
                new Timestamp(System.currentTimeMillis() + 86400000), // 13. eventEndDate
                List.of("USD"),                         // 14. currency
                "Equipment",                            // 15. primaryClassification
                SchedulingStatus.Published.name(),      // 16. schedulingStatus
                "NEW",                                  // 17. status
                List.of(SellingFormat.TAL),             // 18. sellingFormats
                List.of(ParticipationMethod.ONLINE, ParticipationMethod.ONSITE), // 19. waysToParticipate
                true,                                   // 20. registrationOpen
                null,                                   // 21. registrationAutoOpenDate
                true,                                   // 22. talBidOpen
                null,                                   // 23. talBidAutoOpenDate
                true,                                   // 24. priorityBidOpen
                null,                                   // 25. priorityBidAutoOpenDate
                true,                                   // 26. olrEnabled
                new Timestamp(System.currentTimeMillis()), // 27. createdDate
                "Test User",                            // 28. lastModifierName
                new Timestamp(System.currentTimeMillis()), // 29. lastModifiedDate
                locationResponse,                       // 30. location
                siteResponse,                           // 31. site
                true,                                   // 32. isRbMarketplaceSale
                BusinessUnit.builder()                  // 33. businessUnit
                        .code("123")
                        .name("Test Business Unit")
                        .build(),
                true,                                   // 34. allowIpInspection
                List.of("Additional Class"),            // 35. additionalClassification
                EventLocationType.PERMANENT,             // 36. locationType
                "Test Creator",                         // 37. createdBy
                false,                                  // 38. privateTreaty
                "Test Site Configuration",              // 39. siteConfiguration
                "notes",                                // 40. notes
                new Timestamp(System.currentTimeMillis()), // 41. initialAuctionSyncDate
                new Timestamp(System.currentTimeMillis()) // 41. eventFinalizedDate
        );
    }

    @Test
    void logSuccess_WithValidResult() {
        // Arrange
        AuctionContent content = AuctionContent.newBuilder()
                .setAuctionId("auction-123")
                .setName("Test Auction")
                .build();

        Auction auction = Auction.newBuilder()
                .setMessageId("msg-123")
                .setContent(content)
                .setSourceSystem("AUCTION_MANAGEMENT")
                .build();

        ProducerRecord<String, Object> producerRecord = new ProducerRecord<>("test-topic", auction);
        SendResult<String, Object> result = new SendResult<>(producerRecord, new RecordMetadata(
                new TopicPartition("test-topic", 0),
                0L, 0L, 0L, 0L, 0, 0));

        // Act
        eventProducer.logSuccess(result);

        // Verify logs were created - no exceptions thrown
        // Note: Since we're testing a logging method, we mainly verify it executes without throwing exceptions
    }

    @Test
    void logSuccess_WithNullResult() {
        // Act
        eventProducer.logSuccess(null);

        // Verify no exceptions thrown and method handles null gracefully
    }

    @Test
    void logFailed_WithValidAuction() {
        // Arrange
        AuctionContent content = AuctionContent.newBuilder()
                .setAuctionId("auction-456")
                .setName("Failed Auction")
                .build();

        Auction auction = Auction.newBuilder()
                .setMessageId("msg-456")
                .setContent(content)
                .setSourceSystem("AUCTION_MANAGEMENT")
                .build();

        RuntimeException testException = new RuntimeException("Test error message");

        // Act
        eventProducer.logFailed(auction, testException);

        // Verify no exceptions thrown and method handles null gracefully
    }
}
