package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.infra.jpa.entity.SaleNumberAllocation;
import com.rb.capability.auctionmanagement.infra.jpa.repository.SaleNumberAllocationRepository;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.essentials.capability.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.rb.capability.common.util.DefaultUtils.createDefaultEventRequest;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SaleNumberAllocationServiceTest {

    @Mock
    private SaleNumberAllocationRepository saleNumberAllocationRepository;

    @InjectMocks
    private SaleNumberAllocationService saleNumberAllocationService;

    @Test
    void generateSaleNumber_CharityEvent_Success() {
        int year = 2025;
        Integer expectedSaleNumber = 2025901;
        EventRequest charityEventRequest = createDefaultEventRequest().primaryClassification("Charity").build();
        
        when(saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, "Charity"))
                .thenReturn(expectedSaleNumber);
        
        Integer result = saleNumberAllocationService.generateSaleNumber(year, charityEventRequest);
        
        assertEquals(expectedSaleNumber, result);
        verify(saleNumberAllocationRepository).findNextSaleNumberByYearAndType(year, "Charity");
    }

    @Test
    void generateSaleNumber_AgricultureEvent_Success() {
        int year = 2025;
        Integer expectedSaleNumber = 2025502;
        EventRequest agricultureEventRequest = createDefaultEventRequest().primaryClassification("Agriculture").build();

        when(saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, "Agriculture"))
                .thenReturn(expectedSaleNumber);
        
        Integer result = saleNumberAllocationService.generateSaleNumber(year, agricultureEventRequest);
        
        assertEquals(expectedSaleNumber, result);
        verify(saleNumberAllocationRepository).findNextSaleNumberByYearAndType(year, "Agriculture");
    }

    @Test
    void generateSaleNumber_RegularEvent_Success() {
        int year = 2025;
        Integer expectedSaleNumber = 2025101;
        EventRequest regularEventRequest = createDefaultEventRequest().build();

        when(saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, "Non Agriculture Non Charity"))
                .thenReturn(expectedSaleNumber);
        
        Integer result = saleNumberAllocationService.generateSaleNumber(year, regularEventRequest);
        
        assertEquals(expectedSaleNumber, result);
        verify(saleNumberAllocationRepository).findNextSaleNumberByYearAndType(year, "Non Agriculture Non Charity");
    }

    @Test
    void generateSaleNumber_NoAvailableSaleNumbers() {
        int year = 2025;
        EventRequest regularEventRequest = createDefaultEventRequest().build();
        
        when(saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, "Non Agriculture Non Charity"))
                .thenReturn(null);
        
        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () -> 
                saleNumberAllocationService.generateSaleNumber(year, regularEventRequest));
        
        assertEquals("No sale numbers available for the specified year and type.", exception.getMessage());
    }

    @Test
    void generateSaleNumber_WithAdditionalClassifications() {
        int year = 2025;
        Integer expectedSaleNumber = 2025502;

        EventRequest eventWithMultipleClassifications = createDefaultEventRequest()
                .primaryClassification("Industrial")
                .additionalClassification(List.of("Agriculture"))
                .build();
        
        when(saleNumberAllocationRepository.findNextSaleNumberByYearAndType(year, "Agriculture"))
                .thenReturn(expectedSaleNumber);
        
        Integer result = saleNumberAllocationService.generateSaleNumber(year, eventWithMultipleClassifications);
        
        assertEquals(expectedSaleNumber, result);
        verify(saleNumberAllocationRepository).findNextSaleNumberByYearAndType(year, "Agriculture");
    }
    
    @Test
    void markSaleNumberInUse_WithExistingAllocation() {
        EventEntity eventEntity = EventEntity.builder()
                .saleNumber("2025101")
                .primaryClassification("Regular")
                .build();
                
        SaleNumberAllocation existingAllocation = SaleNumberAllocation.builder()
                .saleNumber(2025101)
                .inUse(false)
                .year(2025)
                .type("Non Agriculture Non Charity")
                .build();
                
        when(saleNumberAllocationRepository.findBySaleNumber(2025101))
                .thenReturn(Optional.of(existingAllocation));
        
        saleNumberAllocationService.markSaleNumberInUse(eventEntity);
        
        assertTrue(existingAllocation.isInUse());
        verify(saleNumberAllocationRepository).save(existingAllocation);
    }
    
    @Test
    void markSaleNumberInUse_WithoutExistingAllocation() {
        EventEntity eventEntity = EventEntity.builder()
                .saleNumber("2025101")
                .primaryClassification("Regular")
                .build();
                
        when(saleNumberAllocationRepository.findBySaleNumber(2025101))
                .thenReturn(Optional.empty());
                
        ArgumentCaptor<SaleNumberAllocation> allocationCaptor = ArgumentCaptor.forClass(SaleNumberAllocation.class);
        
        saleNumberAllocationService.markSaleNumberInUse(eventEntity);
        
        verify(saleNumberAllocationRepository).save(allocationCaptor.capture());
        SaleNumberAllocation savedAllocation = allocationCaptor.getValue();
        
        assertEquals(2025101, savedAllocation.getSaleNumber());
        assertEquals(2025, savedAllocation.getYear());
        assertEquals("Non Agriculture Non Charity", savedAllocation.getType());
        assertTrue(savedAllocation.isInUse());
    }
    
    @Test
    void markSaleNumberInUse_CharityEvent() {
        EventEntity charityEvent = EventEntity.builder()
                .saleNumber("2025901")
                .primaryClassification("Charity")
                .build();
                
        when(saleNumberAllocationRepository.findBySaleNumber(2025901))
                .thenReturn(Optional.empty());
                
        ArgumentCaptor<SaleNumberAllocation> allocationCaptor = ArgumentCaptor.forClass(SaleNumberAllocation.class);
        
        saleNumberAllocationService.markSaleNumberInUse(charityEvent);
        
        verify(saleNumberAllocationRepository).save(allocationCaptor.capture());
        SaleNumberAllocation savedAllocation = allocationCaptor.getValue();
        
        assertEquals("Charity", savedAllocation.getType());
    }
    
    @Test
    void markSaleNumberInUse_AgricultureEvent() {
        EventEntity agricultureEvent = EventEntity.builder()
                .saleNumber("2025502")
                .primaryClassification("Agriculture")
                .build();
                
        when(saleNumberAllocationRepository.findBySaleNumber(2025502))
                .thenReturn(Optional.empty());
                
        ArgumentCaptor<SaleNumberAllocation> allocationCaptor = ArgumentCaptor.forClass(SaleNumberAllocation.class);
        
        saleNumberAllocationService.markSaleNumberInUse(agricultureEvent);
        
        verify(saleNumberAllocationRepository).save(allocationCaptor.capture());
        SaleNumberAllocation savedAllocation = allocationCaptor.getValue();
        
        assertEquals("Agriculture", savedAllocation.getType());
    }
    
    @Test
    void markSaleNumberInUse_WithAdditionalClassifications() {
        EventEntity eventWithMultipleClassifications = EventEntity.builder()
                .saleNumber("2025502")
                .primaryClassification("Industrial")
                .additionalClassification("Agriculture")
                .build();
                
        when(saleNumberAllocationRepository.findBySaleNumber(2025502))
                .thenReturn(Optional.empty());
                
        ArgumentCaptor<SaleNumberAllocation> allocationCaptor = ArgumentCaptor.forClass(SaleNumberAllocation.class);
        
        saleNumberAllocationService.markSaleNumberInUse(eventWithMultipleClassifications);
        
        verify(saleNumberAllocationRepository).save(allocationCaptor.capture());
        SaleNumberAllocation savedAllocation = allocationCaptor.getValue();
        
        assertEquals("Agriculture", savedAllocation.getType());
    }

    @Test
    void isSaleNumberUsed_WhenSaleNumberExists_ReturnsTrue() {
        Integer saleNumber = 2025101;
        SaleNumberAllocation allocation = SaleNumberAllocation.builder()
                .saleNumber(saleNumber)
                .inUse(true)
                .year(2025)
                .type("Non Agriculture Non Charity")
                .build();

        when(saleNumberAllocationRepository.findBySaleNumberAndInUseTrue(saleNumber))
                .thenReturn(Optional.of(allocation));

        boolean result = saleNumberAllocationService.isSaleNumberUsed(saleNumber);

        assertTrue(result);
        verify(saleNumberAllocationRepository).findBySaleNumberAndInUseTrue(saleNumber);
    }

    @Test
    void isSaleNumberUsed_WhenSaleNumberDoesNotExist_ReturnsFalse() {
        Integer saleNumber = 2025102;
        when(saleNumberAllocationRepository.findBySaleNumberAndInUseTrue(saleNumber))
                .thenReturn(Optional.empty());

        boolean result = saleNumberAllocationService.isSaleNumberUsed(saleNumber);

        assertFalse(result);
        verify(saleNumberAllocationRepository).findBySaleNumberAndInUseTrue(saleNumber);
    }

    @Test
    void testCountBySaleNumberBetweenAndInUseFalse() {
        int year = 2026;
        int end = 2025500;

        when(saleNumberAllocationRepository.countBySaleNumberBetweenAndInUseFalse(year, "Non Agriculture Non Charity")).thenReturn(25);

        Integer result = saleNumberAllocationService.countBySaleNumberBetweenAndInUseFalse(year, "Non Agriculture Non Charity");

        assertEquals(25, result);
    }
}
