package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.resource.request.CreateEventRequest;
import com.rb.capability.auctionmanagement.resource.response.CheckEventResponse;
import com.rb.capability.auctionmanagement.resource.response.CreateEventResponse;
import com.rb.capability.auctionmanagement.resource.response.LocationResponse;
import com.rb.essentials.capability.exception.BusinessException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TestAutomationServiceTest {

    @InjectMocks
    private TestAutomationService testAutomationService;

    @Mock
    private EventRepository eventRepository;

    @Mock
    private AsyncEventService asyncEventService;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private SaleNumberAllocationService saleNumberAllocationService;

    @Test
    void checkEventAvailability_WhenExists_ReturnsTrue() {
        Integer saleNumber = 2025101;
        when(saleNumberAllocationService.isSaleNumberUsed(saleNumber))
                .thenReturn(true);

        CheckEventResponse response = testAutomationService.checkSaleNumberAvailability(String.valueOf(saleNumber));

        assertTrue(response.eventFound());
    }

    @Test
    void checkEventAvailability_WhenNotExists_ReturnsFalse() {
        Integer saleNumber = 2025102;
        when(saleNumberAllocationService.isSaleNumberUsed(saleNumber))
                .thenReturn(false);

        CheckEventResponse response = testAutomationService.checkSaleNumberAvailability(String.valueOf(saleNumber));

        assertFalse(response.eventFound());
    }


    @Test
    void cloneEvent_ForTestAutomation_Success() {
        // Given
        String sourceSaleNumber = "2024101";
        CreateEventRequest request = CreateEventRequest.builder()
                .requestId("req-123")
                .eventParameters(List.of(
                        CreateEventRequest.EventParameters.builder()
                                .saleNumber(sourceSaleNumber)
                                .newSaleNumber("2024102")
                                .eventStartDate("2024-03-15")
                                .eventEndDate("2024-03-30")
                                .build()
                ))
                .build();

        EventEntity sourceEvent = EventEntity.builder()
                .id(1L)
                .guid("source-guid")
                .saleNumber(sourceSaleNumber)
                .eventAdvertisedName("Test Event")
                .eventStartDate(Timestamp.valueOf("2024-02-15 00:00:00"))
                .eventEndDate(Timestamp.valueOf("2024-02-28 00:00:00"))
                .timezone("America/Chicago")
                .currency("USD")
                .brand("RB")
                .sellingFormats("ONLINE_AUCTION")
                .waysToParticipate("ONLINE")
                .status("DRAFT")
                .locationId("loc123")
                .locationType(EventLocationType.PERMANENT)
                .additionalClassification("INDUSTRIAL")
                .schedulingStatus(SchedulingStatus.Published)
                .primaryClassification("INDUSTRIAL")
                .siteConfiguration("Test Site Configuration")
                .allowIpInspection(false)
                .build();

        EventEntity savedEvent = EventEntity.builder()
                .id(2L)
                .guid("new-guid-123")
                .saleNumber("2024102")
                .eventAdvertisedName("Test Event")
                .eventStartDate(Timestamp.valueOf("2024-03-15 00:00:00"))
                .eventEndDate(Timestamp.valueOf("2024-03-30 00:00:00"))
                .timezone("America/Chicago")
                .currency("USD")
                .brand("RB")
                .sellingFormats("ONLINE_AUCTION")
                .waysToParticipate("ONLINE")
                .status("DRAFT")
                .locationId("loc123")
                .locationType(EventLocationType.PERMANENT)
                .additionalClassification("INDUSTRIAL")
                .schedulingStatus(SchedulingStatus.Published)
                .primaryClassification("INDUSTRIAL")
                .siteConfiguration("Test Site Configuration")
                .allowIpInspection(false)
                .build();

        // Mock location service response
        LocationResponse locationResponse = LocationResponse.builder()
                .name("Test Location")
                .city("Test City")
                .stateProvinceCode("TS")
                .countryCode("TC")
                .build();
        when(placesServiceClient.getLocationByLocationGuid("loc123")).thenReturn(locationResponse);

        when(eventRepository.findBySaleNumber(sourceSaleNumber)).thenReturn(Optional.of(sourceEvent));
        when(eventRepository.save(any(EventEntity.class))).thenReturn(savedEvent);

        // When
        CreateEventResponse response = testAutomationService.cloneEventForTestAutomation(request);

        // Then
        assertNotNull(response);
        assertEquals("req-123", response.requestId());
        assertEquals(1, response.clonedEvents().size());

        CreateEventResponse.ClonedEvents clonedEvent = response.clonedEvents().get(0);
        assertEquals("2024102", clonedEvent.saleNumber());
        assertEquals("2", clonedEvent.id());
        assertEquals("new-guid-123", clonedEvent.gid());
        assertEquals("2024-03-15 00:00:00.0", clonedEvent.eventStartDate());
        assertEquals("2024-03-30 00:00:00.0", clonedEvent.eventEndDate());

        verify(eventRepository).findBySaleNumber(sourceSaleNumber);
        verify(eventRepository).save(any(EventEntity.class));
        verify(asyncEventService).processEventAsync(any(EventEntity.class));
        verify(placesServiceClient).getLocationByLocationGuid("loc123");
        verify(saleNumberAllocationService).markSaleNumberInUse(savedEvent);
    }

    @Test
    void cloneEvent_WhenSourceEventForTestAutomationNotFound() {
        // Given
        CreateEventRequest request = CreateEventRequest.builder()
                .requestId("req-123")
                .eventParameters(List.of(
                        CreateEventRequest.EventParameters.builder()
                                .saleNumber("2024101")
                                .newSaleNumber("2024102")
                                .eventStartDate("2024-03-15 00:00:00")
                                .eventEndDate("2024-03-30 00:00:00")
                                .build()
                ))
                .build();

        when(eventRepository.findBySaleNumber("2024101")).thenReturn(Optional.empty());

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> testAutomationService.cloneEventForTestAutomation(request));

        assertEquals("Source event not found with sale number: 2024101", exception.getMessage());
        verify(eventRepository).findBySaleNumber("2024101");
        verifyNoInteractions(asyncEventService);
    }

    @Test
    void cloneEvent_WhenEventForTestAutomationParametersEmpty() {
        // Given
        CreateEventRequest request = CreateEventRequest.builder()
                .requestId("req-123")
                .eventParameters(Collections.emptyList())
                .build();

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> testAutomationService.cloneEventForTestAutomation(request));

        assertEquals("Event parameters are empty", exception.getMessage());
        verifyNoInteractions(eventRepository, asyncEventService);
    }

    @Test
    void cloneEvent_WhenClonedEventsEmpty_ThrowsException() {
        // Given
        CreateEventRequest request = CreateEventRequest.builder()
                .requestId("req-123")
                .eventParameters(List.of(
                        CreateEventRequest.EventParameters.builder()
                                .saleNumber("2024101")
                                .newSaleNumber("2024102")
                                .eventStartDate("2024-03-15")
                                .eventEndDate("2024-03-30")
                                .build()
                ))
                .build();

        EventEntity sourceEvent = EventEntity.builder()
                .id(1L)
                .guid("source-guid")
                .saleNumber("2024101")
                .eventAdvertisedName("Test Event")
                .eventStartDate(Timestamp.valueOf("2024-02-15 00:00:00"))
                .eventEndDate(Timestamp.valueOf("2024-02-28 00:00:00"))
                .timezone("America/Chicago")
                .currency("USD")
                .brand("RB")
                .sellingFormats("ONLINE_AUCTION")
                .waysToParticipate("ONLINE")
                .status("DRAFT")
                .locationId("loc123")
                .locationType(EventLocationType.PERMANENT)
                .additionalClassification("INDUSTRIAL")
                .schedulingStatus(SchedulingStatus.Published)
                .primaryClassification("INDUSTRIAL")
                .siteConfiguration("Test Site Configuration")
                .allowIpInspection(false)
                .build();

        // Mock repository to return source event
        when(eventRepository.findBySaleNumber("2024101")).thenReturn(Optional.of(sourceEvent));

        // Mock repository save to return null, which will result in empty clonedEvents list
        when(eventRepository.save(any(EventEntity.class))).thenReturn(null);

        // Mock location service to avoid NPE in generateEventName
        LocationResponse locationResponse = LocationResponse.builder()
                .name("Test Location")
                .city("Test City")
                .stateProvinceCode("TS")
                .countryCode("TC")
                .build();
        when(placesServiceClient.getLocationByLocationGuid(any())).thenReturn(locationResponse);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> testAutomationService.cloneEventForTestAutomation(request));

        assertEquals("Failed to clone event", exception.getMessage());
        verify(eventRepository).findBySaleNumber("2024101");
        verify(eventRepository).save(any(EventEntity.class));
    }
}
