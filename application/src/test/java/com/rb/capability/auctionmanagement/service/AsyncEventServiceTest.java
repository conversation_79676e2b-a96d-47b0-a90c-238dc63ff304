package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.client.SalesforceServiceClient;
import com.rb.capability.auctionmanagement.domain.enums.SchedulingStatus;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.kafka.producer.EventProducer;
import com.rb.capability.auctionmanagement.resource.request.SalesforceEventRequest;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.resource.response.LocationResponse;
import com.rb.capability.auctionmanagement.resource.response.SalesforceEventResponse;
import com.rb.capability.auctionmanagement.resource.response.SiteResponse;
import com.rb.capability.common.launchdarkly.FeatureToggleClient;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AsyncEventServiceTest {

    @Mock
    private EventProducer eventProducer;

    @Mock
    private SalesforceServiceClient salesforceServiceClient;

    @Mock
    private LegalEntityService legalEntityService;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private  FeatureToggleClient featureToggleClient;

    @InjectMocks
    private AsyncEventService asyncEventService;

    private EventEntity testEventEntity;
    private EventDetailsResponse testEventResponse;

    @BeforeEach
    void setUp() {
        testEventEntity = EventEntity.builder()
                .guid("test-guid")
                .saleNumber("2024101")
                .eventAdvertisedName("Test Event")
                .createdDate(new Timestamp(System.currentTimeMillis()))
                .eventStartDate(new Timestamp(System.currentTimeMillis()))
                .eventEndDate(new Timestamp(System.currentTimeMillis()))
                .timezone("America/New_York")
                .businessUnitCode("TEST_BU")
                .businessUnitName("Test Business Unit")
                .siteId("SITE-123")
                .locationId("LOC-123")  // Adding locationId
                .siteConfiguration("Test Site Configuration")
                .build();

        // Mock the PlacesServiceClient responses
        SiteResponse mockSiteResponse = SiteResponse.builder()
                .siteId("SITE-123")
                .advertisedName("Test Site")
                .build();

        LocationResponse mockLocationResponse = LocationResponse.builder()
                .id("LOC-123")
                .name("Test Location")
                .streetAddress1("123 Test St")
                .city("Test City")
                .stateProvince("Test State")
                .stateProvinceCode("TS")
                .country("Test Country")
                .countryCode("TC")
                .postalCode("12345")
                .latitude(BigDecimal.valueOf(12.34))
                .longitude(BigDecimal.valueOf(56.78))
                .build();

        when(placesServiceClient.getLocationByLocationGuid("LOC-123")).thenReturn(mockLocationResponse);
        when(placesServiceClient.getSiteBySiteGuid("SITE-123")).thenReturn(mockSiteResponse);

        testEventResponse = EventDetailsResponse.fromEntity(testEventEntity, placesServiceClient);
    }

    @Test
    void processEventAsync_SkipsProposedEvents() {
        // Given
        EventEntity proposedEvent = EventEntity.builder()
                .guid("proposed-event-guid")
                .saleNumber("2024101")
                .eventAdvertisedName("Proposed Test Event")
                .schedulingStatus(SchedulingStatus.Proposed)
                .build();

        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class);
             MockedStatic<GlobalOpenTelemetry> mockedOpenTelemetry = mockStatic(GlobalOpenTelemetry.class)) {

            // Mock span
            Span span = mock(Span.class);
            mockedStatic.when(Span::current).thenReturn(span);

            // Mock tracer (to ensure we don't reach the code after the early return)
            Tracer tracer = mock(Tracer.class);
            mockedOpenTelemetry.when(() -> GlobalOpenTelemetry.getTracer(anyString())).thenReturn(tracer);

            // When
            asyncEventService.processEventAsync(proposedEvent);

            // Then
            verify(span).setAttribute("app.skip.process-event", "proposed-event-guid");
            verify(eventProducer, never()).publishAuctionEvent(any());
            verify(salesforceServiceClient, never()).createEvent(any());

            // Verify that we didn't create a new span (which would happen after the early return)
            verify(tracer, never()).spanBuilder(anyString());
        }
    }


    @Test
    void processEventAsync_Success() {
        when(featureToggleClient.isFeatureFlagEnabled(anyString())).thenReturn(true);
        asyncEventService.processEventAsync(testEventEntity);


        verify(eventProducer).publishAuctionEvent(any(EventDetailsResponse.class));
        verify(salesforceServiceClient).createEvent(any(SalesforceEventRequest.class));
    }

    @Test
    void publishAuctionEventAsync_Success() {
        CompletableFuture<Void> future = asyncEventService.publishAuctionEventAsync(testEventResponse);

        assertNotNull(future);
        verify(eventProducer).publishAuctionEvent(testEventResponse);
    }

    @Test
    void publishAuctionEventAsync_HandlesException() {
        doThrow(new RuntimeException("Kafka error")).when(eventProducer).publishAuctionEvent(any());

        CompletableFuture<Void> future = asyncEventService.publishAuctionEventAsync(testEventResponse);

        assertNotNull(future);
        verify(eventProducer).publishAuctionEvent(testEventResponse);
    }

    @Test
    void callSalesforceAsync_Success() {
        when(featureToggleClient.isFeatureFlagEnabled(anyString())).thenReturn(true);
        SalesforceEventResponse mockResponse = new SalesforceEventResponse();
        mockResponse.setId("SF-123");
        when(salesforceServiceClient.createEvent(any())).thenReturn(mockResponse);
        // Removing unnecessary stubbing:
        // when(legalEntityService.getBusinessNameFromLegalEntityId(any())).thenReturn("Test Business");

        CompletableFuture<Void> future = asyncEventService.callSalesforceAsync(testEventResponse);

        assertNotNull(future);
        verify(salesforceServiceClient).createEvent(any());
    }

    @Test
    void callSalesforceAsync_HandlesException() {
        when(featureToggleClient.isFeatureFlagEnabled(anyString())).thenReturn(true);
        when(salesforceServiceClient.createEvent(any())).thenThrow(new RuntimeException("Salesforce error"));

        CompletableFuture<Void> future = asyncEventService.callSalesforceAsync(testEventResponse);

        assertNotNull(future);
        verify(salesforceServiceClient).createEvent(any());
    }

    @Test
    void callSalesforce_Success() {
        SalesforceEventResponse mockResponse = new SalesforceEventResponse();
        mockResponse.setId("SF-123");
        when(salesforceServiceClient.createEvent(any())).thenReturn(mockResponse);
        // Remove this line since it's not being used:
        // when(legalEntityService.getBusinessNameFromLegalEntityId(any())).thenReturn("Test Business");

        asyncEventService.callSalesforce(testEventResponse);

        verify(salesforceServiceClient).createEvent(any());
        // Remove this verification since it's not being called:
        // verify(legalEntityService).getBusinessNameFromLegalEntityId(any());
    }

    @Test
    void callSalesforce_ThrowsBusinessException() {
        // Given
        when(salesforceServiceClient.createEvent(any())).thenThrow(new RuntimeException("Salesforce error"));

        // Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> asyncEventService.callSalesforce(testEventResponse));
        assertEquals("Salesforce error", exception.getMessage());
    }

    @Test
    void callSalesforce_HandlesNullResponse() {
        asyncEventService.callSalesforce(null);

        verify(salesforceServiceClient, never()).createEvent(any());
        verify(legalEntityService, never()).getBusinessNameFromLegalEntityId(any());
    }

    @Test
    void callSalesforce_ThrowsException() {
        // Given
        when(salesforceServiceClient.createEvent(any())).thenThrow(new RuntimeException("Salesforce error"));

        // Then
        RuntimeException exception = assertThrows(RuntimeException.class,
            () -> asyncEventService.callSalesforce(testEventResponse));
        assertEquals("Salesforce error", exception.getMessage());
    }
}
