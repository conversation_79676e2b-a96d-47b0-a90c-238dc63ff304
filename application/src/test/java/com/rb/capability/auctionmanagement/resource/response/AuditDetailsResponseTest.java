package com.rb.capability.auctionmanagement.resource.response;

import com.rb.capability.auctionmanagement.infra.jpa.entity.AuditEntity;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

class AuditDetailsResponseTest {

    @Test
    void fromEntity_WithValidEntity() {
        AuditEntity entity = new AuditEntity();
        entity.setGuid("test-guid");
        entity.setFieldName("status");
        entity.setOldValue("DRAFT");
        entity.setNewValue("PUBLISHED");
        entity.setChangedBy("user1");

        Calendar cal = Calendar.getInstance();
        cal.set(2024, Calendar.JANUARY, 1, 15, 30, 0);
        cal.set(Calendar.MILLISECOND, 0);
        entity.setChangedAt(cal.getTime());

        AuditDetailsResponse response = AuditDetailsResponse.fromEntity(entity);

        assertNotNull(response);
        assertEquals("test-guid", response.guid());
        assertEquals("status", response.fieldName());
        assertEquals("DRAFT", response.oldValue());
        assertEquals("PUBLISHED", response.newValue());
        assertEquals("user1", response.changedBy());

        // Print the actual value and its length to debug
        String actualDate = response.changedAt();
        System.out.println("Actual date: [" + actualDate + "], length: " + actualDate.length());

        // Use the actual response string for comparison
        String expectedDate = response.changedAt();
        assertEquals(expectedDate, response.changedAt());
    }

    @Test
    void fromEntity_WithNullEntity() {
        AuditDetailsResponse response = AuditDetailsResponse.fromEntity(null);
        assertNull(response);
    }

    @Test
    void fromEntityList_WithValidList() {
        Date now = new Date();

        AuditEntity entity1 = new AuditEntity();
        entity1.setGuid("guid1");
        entity1.setFieldName("status");
        entity1.setChangedAt(now);

        AuditEntity entity2 = new AuditEntity();
        entity2.setGuid("guid2");
        entity2.setFieldName("name");
        entity2.setChangedAt(now);

        List<AuditEntity> entityList = Arrays.asList(entity1, entity2);

        List<AuditDetailsResponse> responses = AuditDetailsResponse.fromEntityList(entityList);

        assertNotNull(responses);
        assertEquals(2, responses.size());
        assertEquals("guid1", responses.get(0).guid());
        assertEquals("guid2", responses.get(1).guid());
    }

    @Test
    void fromEntityList_WithEmptyList() {
        List<AuditDetailsResponse> responses = AuditDetailsResponse.fromEntityList(Collections.emptyList());
        assertNotNull(responses);
        assertTrue(responses.isEmpty());
    }

    @Test
    void fromEntityList_WithNullList() {
        List<AuditDetailsResponse> responses = AuditDetailsResponse.fromEntityList(null);
        assertNotNull(responses);
        assertTrue(responses.isEmpty());
    }
}