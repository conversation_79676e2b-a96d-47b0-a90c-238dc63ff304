package com.rb.capability.auctionmanagement.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rb.capability.auctionmanagement.client.ContactType;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.exception.DataNotFoundException;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.Reason;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.auctionmanagement.resource.request.EventStatusRequest;
import com.rb.capability.auctionmanagement.resource.response.*;
import com.rb.capability.auctionmanagement.service.EventService;
import com.rb.capability.common.exception.GlobalExceptionHandler;
import com.rb.essentials.capability.exception.BusinessException;
import io.opentelemetry.api.trace.Span;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import static com.rb.capability.auctionmanagement.tracer.constants.SpanAttributeKeys.APP_RESPONSE;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class EventControllerTest {

    private MockMvc mockMvc;

    @Mock
    private EventService eventService;

    @InjectMocks
    private EventController eventController;

    private ObjectMapper objectMapper;
    private EventRequest validEventRequest;
    private EventResponse eventResponse;
    private static final String VALID_PAST_DATE = "2025-01-01T00:00:01Z";
    private static final String VALID_FUTURE_DATE = "2025-05-03T00:00:01-07:00";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .standaloneSetup(eventController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();

        objectMapper = new ObjectMapper();

        validEventRequest = EventRequest.builder()
                .name("Test Event")
                .auctionAdvertisedName("Test Event")
                .brand("RBA")
                .timezone("UTC")
                .startDateTime("2025-03-23")
                .endDateTime("2025-03-25")
                .currency(List.of("USD"))
                .locationId("E2DDE423-5762-849C-E053-85180F0A144A")
                .siteId("905034B4-64A0-4460-9090-705424A050D0")
                .primaryClassification("INDUSTRIAL")
                .additionalClassification(List.of("INDUSTRIAL"))
                .schedulingStatus("Proposed")
                .auctionFormats(List.of(SellingFormat.TAL))
                .waysToParticipate(List.of(ParticipationMethod.ONSITE))
                .registrationOpen(true)
                .registrationAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .talBidOpen(true)
                .talBidAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .priorityBidOpen(true)
                .priorityBidAutoOpenDate(new Timestamp(System.currentTimeMillis()))
                .legalEntityId("1234567890")
                .businessUnit(BusinessUnit.builder().code("123").name("Test BU").build())
                .olrEnabled(true)
                .sourceAuctionNumber("2024101")
                .build();

        eventResponse = new EventResponse("12345", "SALE-67890");
    }

    @Test
    void createEvent_Success() throws Exception {
        // Given
        EventResponse successResponse = EventResponse.success("123e4567-e89b-12d3-a456-************", "20240101");
        when(eventService.createEvent(any(EventRequest.class))).thenReturn(successResponse);

        // When & Then
        mockMvc.perform(post("/events")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validEventRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.eventGuid").value("123e4567-e89b-12d3-a456-************"))
                .andExpect(jsonPath("$.saleNumber").value("20240101"));
    }

    @Test
    void createEvent_InvalidRequest() throws Exception {
        // Given
        EventRequest invalidRequest = EventRequest.builder()
                .auctionAdvertisedName("")  // invalid empty event name
                .brand(null)  // invalid null brand
                .timezone("UTC")
                .startDateTime("2025-03-23")
                .endDateTime("2025-03-23")
                .currency(List.of("USD"))
                .locationId("123dsad")
                .siteId("343442")
                .primaryClassification("INDUSTRIAL")
                .additionalClassification(List.of("INDUSTRIAL"))
                .schedulingStatus("SCHEDULED")
                .auctionFormats(List.of(SellingFormat.TAL))
                .waysToParticipate(List.of(ParticipationMethod.ONSITE))
                .registrationOpen(true)
                .registrationAutoOpenDate(null)
                .talBidOpen(true)
                .talBidAutoOpenDate(null)
                .priorityBidOpen(true)
                .priorityBidAutoOpenDate(null)
                .olrEnabled(true)
                .build();

        // When & Then
        mockMvc.perform(post("/events")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void createEvent_WhenServiceThrowsBusinessException() throws Exception {
        // Given
        when(eventService.createEvent(any(EventRequest.class)))
                .thenThrow(new BusinessException("Event with sale number already exists"));

        // When & Then
        mockMvc.perform(post("/events")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validEventRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Event with sale number already exists"));

        verify(eventService).createEvent(any(EventRequest.class));
    }

    @Test
    void createEvent_WhenServiceThrowsRuntimeException() throws Exception {
        // Given
        String errorMessage = "Database connection failed";
        when(eventService.createEvent(any(EventRequest.class)))
                .thenThrow(new RuntimeException(errorMessage));

        // When & Then
        mockMvc.perform(post("/events")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validEventRequest)))
                .andExpect(status().isOk())         // Changed back to isOk since BusinessException is handled
                .andExpect(jsonPath("$.code").value(400))    // Changed back to 400 for BusinessException
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(eventService).createEvent(any(EventRequest.class));
    }

    @Test
    void createEvent_WhenRequestBodyIsMalformed() throws Exception {
        // When & Then
        mockMvc.perform(post("/events")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{invalid json}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void createEvent_WhenContentTypeIsNotJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/events")
                        .contentType(MediaType.TEXT_PLAIN)
                        .content("plain text"))
                .andExpect(status().isUnsupportedMediaType());
    }

    @Test
    void getEventDetails_Success() throws Exception {
        // Given
        String eventGuid = "test-eventGuid";
        EventDetailsResponse mockResponse = createMockEventDetailsResponse(eventGuid);

        when(eventService.getEventDetails(eventGuid)).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(get("/events/{event_guid}", eventGuid)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void getEventDetails_WhenServiceThrowsBusinessException() throws Exception {
        // Given
        String eventGuid = "invalid-eventGuid";
        String errorMessage = "Event not found with GUID: " + eventGuid;
        when(eventService.getEventDetails(eventGuid))
                .thenThrow(new BusinessException(errorMessage));

        // When & Then
        mockMvc.perform(get("/events/{event_guid}", eventGuid)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value(errorMessage))
                .andDo(print());  // Optional: helps with debugging

        verify(eventService).getEventDetails(eventGuid);
    }

    @Test
    void getEventDetails_WhenMalformedRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/events/")  // Missing required path variable
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());  // 404 for missing path variable
    }

    @Test
    void getEventDetails_WithSpanAttributes() throws Exception {
        // Given
        String eventGuid = "test-eventGuid";
        EventDetailsResponse mockResponse = createMockEventDetailsResponse(eventGuid);
        when(eventService.getEventDetails(eventGuid)).thenReturn(mockResponse);

        // Create mock span for testing span attributes
        Span mockSpan = mock(Span.class);
        try (MockedStatic<Span> mockedStatic = mockStatic(Span.class)) {
            mockedStatic.when(Span::current).thenReturn(mockSpan);

            // When
            mockMvc.perform(get("/events/{event_guid}", eventGuid)
                            .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());

            // Then
            verify(mockSpan).setAttribute(eq(APP_RESPONSE), anyString());
        }
    }

    private EventDetailsResponse createMockEventDetailsResponse(String guid) {
        LocationResponse response = new LocationResponse(
                "123e4567-e89b-12d3-a456-************", // id
                "PERMANENT_SITE",                        // type
                "Test Location",                         // name
                "123 Test St",                           // streetAddress1
                "Test City",                             // city
                "TS",                                    // stateProvinceCode
                "Test State",                            // stateProvince
                "TC",                                    // countryCode
                "Test Country",                          // country
                "12345",                                 // postalCode
                BigDecimal.valueOf(33.4255),                                 // latitude
                BigDecimal.valueOf(-112.1711),                               // longitude
                List.of(new ContactDetailsResponse(ContactType.ALTERNATE_INSPECTION, "John", "Doe", "123",
                        "456", "7890", "<EMAIL>"))
        );

        SiteResponse siteResponse = new SiteResponse("site-123", "Test Site", "Test Site");

        return new EventDetailsResponse(
                guid,                                   // 1. eventGuid (String)
                "Test Location",                        // 2. locationId (String)
                "Test Site",                            // 3. siteId (String)
                "20240101",                             // 4. saleNumber (String)
                "Test Event",                           // 5. name (String)
                "Test Event",                           // 6. eventAdvertisedName (String)
                "Test Description",                     // 7. description (String) - added based on required types
                2,                                      // 8. numberOfDays (Integer)
                "RB",                                   // 9. brand (String)
                "UTC",                                  // 10. timezone (String)
                "123",                                  // 11. legalEntityId (String)
                new Timestamp(System.currentTimeMillis()), // 12. eventStartDate (Timestamp)
                new Timestamp(System.currentTimeMillis()), // 13. eventEndDate (Timestamp)
                List.of("USD"),                         // 14. currency (List<String>)
                "INDUSTRIAL",                           // 15. primaryClassification (String)
                "SCHEDULED",                            // 16. schedulingStatus (String)
                "New",                                  // 17. status (String)
                List.of(SellingFormat.TAL),             // 18. sellingFormats (List<SellingFormat>)
                List.of(ParticipationMethod.ONSITE),    // 19. waysToParticipate (List<ParticipationMethod>)
                true,                                   // 20. registrationOpen (Boolean)
                new Timestamp(System.currentTimeMillis()), // 21. registrationAutoOpenDate (Timestamp)
                true,                                   // 22. talBidOpen (Boolean)
                new Timestamp(System.currentTimeMillis()), // 23. talBidAutoOpenDate (Timestamp)
                true,                                   // 24. priorityBidOpen (Boolean)
                new Timestamp(System.currentTimeMillis()), // 25. priorityBidAutoOpenDate (Timestamp)
                true,                                   // 26. olrEnabled (Boolean)
                new Timestamp(System.currentTimeMillis()), // 27. createdDate (Timestamp)
                "testUser",                             // 28. lastModifierName (String)
                new Timestamp(System.currentTimeMillis()), // 29. lastModifiedDate (Timestamp)
                response,                               // 30. location (LocationResponse)
                siteResponse,                           // 31. site (SiteResponse)
                false,                                  // 32. isRbMarketplaceSale (Boolean)
                new BusinessUnit("123", "123 Test"),    // 33. businessUnit (BusinessUnit)
                true,                                   // 34. allowIpInspection (Boolean)
                List.of("Industrial"),                  // 35. additionalClassification (List<String>)
                EventLocationType.ONLINE_WEB_BASED,      // 36. locationType (EventLocationType)
                "John,Doe",                             // 37. createdBy (String)
                true,                                   // 38. privateTreaty (Boolean)
                "Test Site Configuration",              // 39. siteConfiguration (String)
                "notes",                                // 40. notes (String)
                new Timestamp(System.currentTimeMillis()), // 41. initialAuctionSyncDate (Timestamp)
                new Timestamp(System.currentTimeMillis()), // 42. eventFinalizedDate (Timestamp)
                false,
                false


        );
    }

    private EventSearchResponse createMockEventSearchResponse(String guid) {
        return new EventSearchResponse(
                guid,
                "20250101",
                "Test Event",
                "Test Event"
        );
    }

    // Remove or use these fields
    // If they're not used anywhere in the test class, we can safely remove them:

    // Remove these unused fields
    // private LocationResponse response;
    // private SiteResponse siteResponse;

    // Alternatively, if they should be used in tests but aren't currently,
    // you might want to use them in a test method or initialize them properly

    @Test
    void testGetEvents_Success() {
        String[] schedulingStatus = {"SCHEDULED"};
        String[] primaryTypes = {"Industrial"};
        String[] locationTypes = {"Permanent"};
        String eventGuid = "test-eventGuid";
        Boolean rolledOver = false;
        List<EventDetailsResponse> mockResponse = List.of(createMockEventDetailsResponse(eventGuid));

        when(eventService.findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, rolledOver))
                .thenReturn(mockResponse);

        ResponseEntity<List<EventDetailsResponse>> response =
                eventController.getEvents(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, rolledOver);

        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertEquals(mockResponse, response.getBody());

        verify(eventService).findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, rolledOver);
    }


    @Test
    void testGetEvents_NoSchedulingStatus_Success() {
        String[] schedulingStatus = null;
        String[] primaryTypes = {"Dealer Dismantler"};
        String[] locationTypes = {"Regional"};
        String eventGuid = "test-eventGuid";
        Boolean rolledOver = false;
        List<EventDetailsResponse> mockResponse = List.of(createMockEventDetailsResponse(eventGuid));

        when(eventService.findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, null, primaryTypes, locationTypes, rolledOver))
                .thenReturn(mockResponse);

        ResponseEntity<List<EventDetailsResponse>> response =
                eventController.getEvents(VALID_PAST_DATE, VALID_FUTURE_DATE, null, primaryTypes, locationTypes, rolledOver);

        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertEquals(mockResponse, response.getBody());

        verify(eventService).findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, null, primaryTypes, locationTypes, rolledOver);
    }

    @Test
    void testGetEvents_OnlySchedulingStatusProvided() {
        String[] schedulingStatus = {"PUBLISHED"};
        String[] primaryTypes = null;
        String[] locationTypes = null;
        Boolean rolledOver = false;
        List<EventDetailsResponse> mockResponse = List.of(createMockEventDetailsResponse("event123"));

        when(eventService.findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, rolledOver))
                .thenReturn(mockResponse);

        ResponseEntity<List<EventDetailsResponse>> response =
                eventController.getEvents(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, rolledOver);

        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertEquals(mockResponse, response.getBody());

        verify(eventService).findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, null, null, rolledOver);
    }

    @Test
    void testGetEvents_AllFiltersNull() {
        Boolean rolledOver = false;
        List<EventDetailsResponse> mockResponse = List.of(createMockEventDetailsResponse("eventXYZ"));

        when(eventService.findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, null, null, null, rolledOver))
                .thenReturn(mockResponse);

        ResponseEntity<List<EventDetailsResponse>> response =
                eventController.getEvents(VALID_PAST_DATE, VALID_FUTURE_DATE, null, null, null, rolledOver);

        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertEquals(mockResponse, response.getBody());

        verify(eventService).findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, null, null, null, rolledOver);
    }


    @Test
    void testGetEvents_InvalidTimestampFormat() {
        String invalidPastDate = "2025-13-01"; // Invalid month
        String[] schedulingStatus = {"SCHEDULED"};
        String errorMessage = "Invalid date format";

        when(eventService.findEventsWithFilters(invalidPastDate, VALID_FUTURE_DATE, schedulingStatus, null, null, false))
                .thenThrow(new IllegalArgumentException(errorMessage));

        BusinessException exception = assertThrows(BusinessException.class, () ->
                eventController.getEvents(invalidPastDate, VALID_FUTURE_DATE, schedulingStatus, null, null, false)
        );

        assertEquals(errorMessage, exception.getMessage());
        verify(eventService).findEventsWithFilters(invalidPastDate, VALID_FUTURE_DATE, schedulingStatus, null, null, false);
    }


    @Test
    void testGetEvents_ServiceException() {
        String[] schedulingStatus = {"SCHEDULED"};
        String[] primaryTypes = null;
        String[] locationTypes = null;
        String errorMessage = "Database connection failed";

        when(eventService.findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, false))
                .thenThrow(new RuntimeException(errorMessage));

        BusinessException exception = assertThrows(BusinessException.class, () ->
                eventController.getEvents(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, false)
        );

        assertEquals(errorMessage, exception.getMessage());
        verify(eventService).findEventsWithFilters(VALID_PAST_DATE, VALID_FUTURE_DATE, schedulingStatus, primaryTypes, locationTypes, false);
    }

    @Test
    void testGetEvents_FutureDateBeforePastDate() {
        String futureBeforePast = "2024-12-31T23:59:59Z"; // Before VALID_PAST_DATE
        String[] schedulingStatus = {"PUBLISHED"};
        String[] primaryTypes = null;
        String[] locationTypes = null;
        String errorMessage = "Future date cannot be before past date";

        when(eventService.findEventsWithFilters(VALID_PAST_DATE, futureBeforePast, schedulingStatus, primaryTypes, locationTypes, false))
                .thenThrow(new IllegalArgumentException(errorMessage));

        BusinessException exception = assertThrows(BusinessException.class, () ->
                eventController.getEvents(VALID_PAST_DATE, futureBeforePast, schedulingStatus, primaryTypes, locationTypes, false)
        );

        assertEquals(errorMessage, exception.getMessage());
        verify(eventService).findEventsWithFilters(VALID_PAST_DATE, futureBeforePast, schedulingStatus, primaryTypes, locationTypes, false);
    }

    @Test
    void testUpdateEvent_Success() throws Exception {
        String auctionId = "123";
        when(eventService.updateEvent(auctionId, validEventRequest))
                .thenReturn(eventResponse);
        mockMvc.perform(put("/events/{auction_id}", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validEventRequest)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(eventResponse)));

        verify(eventService, times(1)).updateEvent(auctionId, validEventRequest);
    }

    @Test
    void updateEvent_WhenRequestBodyIsMalformed() throws Exception {
        mockMvc.perform(put("/events/12345")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{invalid json}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateEventStatus_Success() throws Exception {
        // Given
        String auctionId = "test-auction-123";
        EventStatusRequest statusRequest = new EventStatusRequest("NEW", null, 0);
        EventResponse expectedResponse = new EventResponse(auctionId, "SALE-67890");

        when(eventService.updateEventStatus(auctionId, statusRequest))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.eventGuid").value(auctionId))
                .andExpect(jsonPath("$.saleNumber").value("SALE-67890"));

        verify(eventService).updateEventStatus(auctionId, statusRequest);
    }

    @Test
    void updateEventStatus_WhenEventNotFound() throws Exception {
        // Given
        String auctionId = "non-existent-auction";
        EventStatusRequest statusRequest = new EventStatusRequest("NEW", null, 0);
        String errorMessage = "Event not found with ID: " + auctionId;

        when(eventService.updateEventStatus(auctionId, statusRequest))
                .thenThrow(new BusinessException(errorMessage));

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(eventService).updateEventStatus(auctionId, statusRequest);
    }

    @Test
    void updateEventStatus_WhenInvalidStatus() throws Exception {
        // Given
        String auctionId = "test-auction-123";
        EventStatusRequest statusRequest = new EventStatusRequest("INVALID_STATUS", null, 0);
        String errorMessage = "Invalid status: INVALID_STATUS";

        when(eventService.updateEventStatus(auctionId, statusRequest))
                .thenThrow(new BusinessException(errorMessage));

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(eventService).updateEventStatus(auctionId, statusRequest);
    }

    @Test
    void updateEventStatus_WhenMalformedJson() throws Exception {
        // Given
        String auctionId = "test-auction-123";
        String malformedJson = "{invalid-json}";

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(malformedJson))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateEventStatus_WhenInvalidContentType() throws Exception {
        // Given
        String auctionId = "test-auction-123";
        EventStatusRequest statusRequest = new EventStatusRequest("NEW", null, 0);

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.TEXT_PLAIN)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isUnsupportedMediaType());
    }

    @Test
    void updateEventStatus_WhenNullStatus() throws Exception {
        // Given
        String auctionId = "test-auction-123";
        EventStatusRequest statusRequest = new EventStatusRequest(null, null, 0);
        String errorMessage = "Status cannot be null";

        when(eventService.updateEventStatus(auctionId, statusRequest))
                .thenThrow(new BusinessException(errorMessage));

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(eventService).updateEventStatus(auctionId, statusRequest);
    }

    @Test
    void updateEventStatus_WhenAuctionIdContainsCrlf() throws Exception {
        // Given
        String auctionId = "test\r\nauction\n123";
        EventStatusRequest statusRequest = new EventStatusRequest("NEW", null, 0);
        EventResponse expectedResponse = new EventResponse("test_auction_123", "SALE-67890");

        when(eventService.updateEventStatus(anyString(), eq(statusRequest)))
                .thenReturn(expectedResponse);

        // When & Then
        mockMvc.perform(patch("/events/{auction_id}/status", auctionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.eventGuid").value("test_auction_123"))
                .andExpect(jsonPath("$.saleNumber").value("SALE-67890"));
    }

    @Test
    void updateEvent_throwsBusinessException_whenServiceFails() {
        String auctionId = "A123";

        when(eventService.updateEvent(anyString(), any()))
                .thenThrow(new BusinessException("Something went wrong"));

        try (MockedStatic<Span> spanMock = Mockito.mockStatic(Span.class)) {
            Span mockSpan = mock(Span.class);
            spanMock.when(Span::current).thenReturn(mockSpan);

            when(mockSpan.setAttribute(anyString(), anyString())).thenReturn(mockSpan);
            BusinessException ex = assertThrows(BusinessException.class, () ->
                    eventController.updateEvent(auctionId, validEventRequest));

            assertEquals("Something went wrong", ex.getMessage());

        }
    }

    @Test
    void shouldReturnListOfEvents_whenSearchEventIsValid() throws Exception {
        String searchEvent = "12345";
        String eventGuid = "test-eventGuid";
        List<EventSearchResponse> mockResponse = List.of(
                createMockEventSearchResponse(eventGuid)
        );

        Mockito.when(eventService.findEvents(searchEvent)).thenReturn(mockResponse);

        mockMvc.perform(get("/events/search")
                        .param("search_param", searchEvent))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].name").value("Test Event"));
    }

    @Test
    void shouldThrowBusinessException_whenServiceThrowsException() throws Exception {
        String searchEvent = "";
        mockMvc.perform(get("/events/search/{search_event}", searchEvent))
                .andExpect(status().isNotFound()); // or .isBadRequest() depending on your @ControllerAdvice
    }

    @Test
    void getEventDetailsByAuctionNumber_Success() throws Exception {
        // Given
        String auctionNumber = "2025101";
        EventDetailsResponse mockResponse = createMockEventDetailsResponse("test-eventGuid");

        // Create a new instance with the updated sale number
        EventDetailsResponse updatedResponse = new EventDetailsResponse(
                mockResponse.eventGuid(),
                mockResponse.locationId(),
                mockResponse.siteId(),
                auctionNumber,  // Updated sale number
                "sourceAuctionNumber", // Added sourceAuctionNumber field
                mockResponse.name(),
                mockResponse.eventAdvertisedName(),
                mockResponse.numberOfDays(),
                mockResponse.brand(),
                mockResponse.timezone(),
                mockResponse.legalEntityId(),
                mockResponse.eventStartDate(),
                mockResponse.eventEndDate(),
                mockResponse.currency(),
                mockResponse.primaryClassification(),
                mockResponse.schedulingStatus(),
                mockResponse.status(),
                mockResponse.sellingFormats(),
                mockResponse.waysToParticipate(),
                mockResponse.registrationOpen(),
                mockResponse.registrationAutoOpenDate(),
                mockResponse.talBidOpen(),
                mockResponse.talBidAutoOpenDate(),
                mockResponse.priorityBidOpen(),
                mockResponse.priorityBidAutoOpenDate(),
                mockResponse.olrEnabled(),
                mockResponse.createdDate(),
                mockResponse.lastModifierName(),
                mockResponse.lastModifiedDate(),
                mockResponse.location(),
                mockResponse.site(),
                mockResponse.isRbMarketplaceSale(),
                mockResponse.businessUnit(),
                mockResponse.allowIpInspection(),
                mockResponse.additionalClassification(),
                mockResponse.locationType(),
                mockResponse.createdBy(),
                mockResponse.privateTreaty(),
                mockResponse.siteConfiguration(),
                mockResponse.notes(),
                mockResponse.initialAuctionSyncDate(),
                mockResponse.eventFinalizedDate(),
                mockResponse.rolledOver(),
                mockResponse.doNotUse()


        );

        when(eventService.getEventDetailsByAuctionNumber(auctionNumber)).thenReturn(updatedResponse);

        // When & Then
        mockMvc.perform(get("/events/auction/{auction_number}", auctionNumber)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.auction_number").value(auctionNumber));

        verify(eventService).getEventDetailsByAuctionNumber(auctionNumber);
    }

    @Test
    void getEventDetailsByAuctionNumber_NotFound() throws Exception {
        // Given
        String auctionNumber = "non-existent-auction-number";
        String errorMessage = "Event not found with auction number: " + auctionNumber;

        when(eventService.getEventDetailsByAuctionNumber(auctionNumber))
                .thenThrow(new DataNotFoundException(Reason.NOT_FOUND, errorMessage));

        // When & Then
        mockMvc.perform(get("/events/auction/{auction_number}", auctionNumber)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))  // Changed from 404 to 400
                .andExpect(jsonPath("$.message").value(errorMessage));

        verify(eventService).getEventDetailsByAuctionNumber(auctionNumber);
    }
}
