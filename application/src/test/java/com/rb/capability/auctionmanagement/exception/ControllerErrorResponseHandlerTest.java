package com.rb.capability.auctionmanagement.exception;

import com.rb.capability.auctionmanagement.client.exception.ControllerErrorResponseHandler;
import com.rb.capability.auctionmanagement.client.exception.DataNotFoundException;
import com.rb.capability.auctionmanagement.client.exception.SalesforceAuthenticationException;
import com.rb.capability.auctionmanagement.controller.EventController;
import com.rb.capability.auctionmanagement.domain.enums.Reason;
import com.rb.capability.auctionmanagement.resource.request.EventRequest;
import com.rb.capability.common.exception.GlobalExceptionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

class ControllerErrorResponseHandlerTest {

    private ControllerErrorResponseHandler handler;
    private NativeWebRequest webRequest;

    @BeforeEach
    void setUp() {
        handler = new ControllerErrorResponseHandler();
        webRequest = mock(NativeWebRequest.class);
    }

    @Test
    void handleIllegalArgumentException_ShouldReturnBadRequest() {
        // Given
        String errorMessage = "Invalid argument provided";
        IllegalArgumentException exception = new IllegalArgumentException(errorMessage);

        // When
        ResponseEntity<Problem> response = handler.handleIllegalArgumentException(exception, webRequest);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getTitle()).isEqualTo(errorMessage);
        assertThat(response.getBody().getStatus()).isEqualTo(Status.BAD_REQUEST);
    }

    @Test
    void handleHttpServerErrorException_ShouldReturnCorrectStatus() {
        // Given
        HttpServerErrorException exception = HttpServerErrorException
            .create(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error", null, null, null);

        // When
        ResponseEntity<Problem> response = handler.handleHttpServerErrorException(exception, webRequest);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus().getStatusCode()).isEqualTo(500);
        assertThat(response.getBody().getTitle()).isEqualTo("Internal Server Error");
    }

    @Test
    void extractErrorDetails_WithValidJsonResponse() {
        // Given
        String jsonResponse = "{\"detail\":\"Detailed error message\"}";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, jsonResponse.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo("Detailed error message");
    }

    @Test
    void extractErrorDetails_WithInvalidJsonResponse() {
        // Given
        String invalidJson = "Invalid JSON";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, invalidJson.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo(exception.getMessage());
    }

    @Test
    void extractErrorDetails_WithEmptyResponse() {
        // Given
        WebClientResponseException exception = WebClientResponseException
            .create(400, "Bad Request", null, null, null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo(exception.getMessage());
    }

    @Test
    void buildProblem_ShouldCreateProblemWithCorrectAttributes() {
        // Given
        String detail = "Error detail";
        String title = "Error title";
        Status status = Status.BAD_REQUEST;

        // When
        Problem problem = handler.buildProblem(status, detail, title);

        // Then
        assertThat(problem).isNotNull();
        assertThat(problem.getTitle()).isEqualTo(title);
        assertThat(problem.getStatus()).isEqualTo(Status.BAD_REQUEST);
        assertThat(problem.getDetail()).isEqualTo(detail);
    }

    @Test
    void buildProblem_WithEmptyTitle_ShouldUseStatusReasonPhrase() {
        // Given
        String detail = "Error detail";
        String title = "";
        Status status = Status.BAD_REQUEST;

        // When
        Problem problem = handler.buildProblem(status, detail, title);

        // Then
        assertThat(problem).isNotNull();
        assertThat(problem.getTitle()).isEqualTo(status.getReasonPhrase());
        assertThat(problem.getStatus()).isEqualTo(Status.BAD_REQUEST);
        assertThat(problem.getDetail()).isEqualTo(detail);
    }

    @Test
    void extractErrorDetails_WithNullDetailField() {
        // Given
        String jsonResponse = "{\"detail\":null}";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, jsonResponse.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo(exception.getMessage());
    }

    @Test
    void extractErrorDetails_WithBlankDetailField() {
        // Given
        String jsonResponse = "{\"detail\":\"   \"}";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, jsonResponse.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo(exception.getMessage());
    }

    @Test
    void extractErrorDetails_WithEmptyDetailField() {
        // Given
        String jsonResponse = "{\"detail\":\"\"}";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, jsonResponse.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo(exception.getMessage());
    }

    @Test
    void extractErrorDetails_WithValidDetailField() {
        // Given
        String jsonResponse = "{\"detail\":\"Detailed error message\"}";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, jsonResponse.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo("Detailed error message");
    }

    @Test
    void extractErrorDetails_WithMissingDetailField() {
        // Given
        String jsonResponse = "{\"message\":\"Some other message\"}";
        WebClientResponseException exception = WebClientResponseException
                .create(400, "Bad Request", null, jsonResponse.getBytes(StandardCharsets.UTF_8), null);

        // When
        String errorDetails = ControllerErrorResponseHandler.extractErrorDetails(exception);

        // Then
        assertThat(errorDetails).isEqualTo(exception.getMessage());
    }

    @Test
    void dataNotFoundException_ShouldReturnNotFound() {
        DataNotFoundException exception = new DataNotFoundException(Reason.NOT_FOUND, "Not Found");

        ResponseEntity<Problem> response = new ControllerErrorResponseHandler()
                .dataNotFoundException(exception, mock(NativeWebRequest.class));

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody().getDetail()).isEqualTo("Not Found");
    }

    @Test
    void handleSalesforceAuthenticationException_ShouldReturnUnauthorized() {
        SalesforceAuthenticationException exception = new SalesforceAuthenticationException(Reason.UNAUTHENTICATED, "Salesforce Authentication Issue");

        ResponseEntity<Problem> response = new ControllerErrorResponseHandler().handleSalesforceAuthenticationException(exception, mock(NativeWebRequest.class));

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody().getDetail()).isEqualTo("Unauthorized");
    }

    @Test
    void handleMethodArgumentNotValid_ShouldReturnBadRequestWithValidationErrors() throws NoSuchMethodException {

        Method method = EventController.class.getMethod("updateEvent", String.class, EventRequest.class);
        MethodParameter methodParameter = new MethodParameter(method, 1);

        EventRequest eventRequest = mock(EventRequest.class);

        FieldError fieldError = new FieldError("eventRequest", "name", "must not be blank");
        BindingResult bindingResult = new BeanPropertyBindingResult(eventRequest, "eventRequest");
        bindingResult.addError(fieldError);

        MethodArgumentNotValidException exception = new MethodArgumentNotValidException(methodParameter, bindingResult);

        ControllerErrorResponseHandler handler = new ControllerErrorResponseHandler();
        ResponseEntity<Problem> response = handler.handleMethodArgumentNotValid(exception, mock(NativeWebRequest.class));

        assertThat(response.getStatusCode().is4xxClientError());
        Map<String, Object> parameters = response.getBody().getParameters();
        assertThat(parameters).containsKey("errors");

    }
}



