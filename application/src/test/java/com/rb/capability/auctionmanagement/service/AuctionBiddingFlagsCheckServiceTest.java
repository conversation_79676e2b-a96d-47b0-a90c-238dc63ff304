package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.kafka.producer.EventProducer;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.MockedStatic;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuctionBiddingFlagsCheckServiceTest {

    @Mock
    private EventService eventService;

    @Mock
    private EventProducer eventProducer;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private AuditService auditService;

    @InjectMocks
    private AuctionBiddingFlagsCheckService service;

    @Captor
    private ArgumentCaptor<List<EventEntity>> eventsCaptor;

    private EventEntity createEventEntity(String guid, Boolean isPriorityBidOpen, Boolean isTalBidOpen, 
                                         Boolean isRegistrationOpen, Timestamp priorityDate, 
                                         Timestamp talDate, Timestamp registrationDate) {
        LocalDateTime now = LocalDateTime.now();
        return EventEntity.builder()
                .guid(guid)
                .saleNumber("2025101")
                .name("Test Event")
                .eventAdvertisedName("Test Advertised Event")
                .isPriorityBidOpen(isPriorityBidOpen)
                .isTalBidOpen(isTalBidOpen)
                .isRegistrationOpen(isRegistrationOpen)
                .priorityBidAutoOpenDate(priorityDate)
                .talBidAutoOpenDate(talDate)
                .registrationAutoOpenDate(registrationDate)
                .timezone("UTC") // Add timezone to prevent NPE
                .eventStartDate(Timestamp.valueOf(now.plusDays(30))) // Add event start date
                .eventEndDate(Timestamp.valueOf(now.plusDays(31))) // Add event end date
                .build();
    }

    @Test
    void syncBiddingFlagsUnMatchedData_NoEventsToUpdate() {
        // Given
        LocalDateTime futureTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Timestamp futureTimestamp = Timestamp.valueOf(futureTime);
        
        EventEntity event = createEventEntity(
            "test-guid-1", 
            true, true, true,
            futureTimestamp, futureTimestamp, futureTimestamp
        );
        
        List<EventEntity> events = List.of(event);
        
        // When
        service.syncBiddingFlagsUnMatchedData(events);
        
        // Then
        verify(eventService, never()).saveEvents(any());
        verify(eventProducer, never()).publishAuctionEvent(any());
    }

    @Test
    void syncBiddingFlagsUnMatchedData_UpdatesPriorityBidding() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        LocalDateTime futureTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Timestamp futureTimestamp = Timestamp.valueOf(futureTime);
        
        EventEntity event = createEventEntity(
            "test-guid-1", 
            false, true, true,
            pastTimestamp, futureTimestamp, futureTimestamp
        );
        
        List<EventEntity> events = List.of(event);
        
        // Mock EventDetailsResponse.fromEntity
        try (MockedStatic<EventDetailsResponse> mockedResponse = mockStatic(EventDetailsResponse.class)) {
            EventDetailsResponse mockResponse = mock(EventDetailsResponse.class);
            mockedResponse.when(() -> EventDetailsResponse.fromEntity(any(), any())).thenReturn(mockResponse);

            // When
            service.syncBiddingFlagsUnMatchedData(events);

            // Then
            verify(eventService).saveEvents(eventsCaptor.capture());
            List<EventEntity> savedEvents = eventsCaptor.getValue();

            assertEquals(1, savedEvents.size());
            assertTrue(savedEvents.get(0).getIsPriorityBidOpen());

            // Verify the audit service was called
            ArgumentCaptor<EventEntity> entityCaptor = ArgumentCaptor.forClass(EventEntity.class);
            verify(auditService).auditUpdate(entityCaptor.capture());
            EventEntity capturedEntity = entityCaptor.getValue();

            // Verify old values were set
            assertNotNull(capturedEntity.getOldValues());
            assertEquals(false, capturedEntity.getOldValues().get("isPriorityBidOpen"));

            verify(eventProducer).publishAuctionEvent(any());
        }
    }

    @Test
    void syncBiddingFlagsUnMatchedData_UpdatesTalBidding() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        LocalDateTime futureTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Timestamp futureTimestamp = Timestamp.valueOf(futureTime);
        
        EventEntity event = createEventEntity(
            "test-guid-1", 
            true, false, true,
            futureTimestamp, pastTimestamp, futureTimestamp
        );
        
        List<EventEntity> events = List.of(event);
        
        // Mock EventDetailsResponse.fromEntity
        try (MockedStatic<EventDetailsResponse> mockedStatic = mockStatic(EventDetailsResponse.class)) {
            EventDetailsResponse mockResponse = mock(EventDetailsResponse.class);
            mockedStatic.when(() -> EventDetailsResponse.fromEntity(any(), any())).thenReturn(mockResponse);

            // When
            service.syncBiddingFlagsUnMatchedData(events);

            // Then
            verify(eventService).saveEvents(eventsCaptor.capture());
            List<EventEntity> savedEvents = eventsCaptor.getValue();

            assertEquals(1, savedEvents.size());
            assertTrue(savedEvents.get(0).getIsTalBidOpen());
            verify(auditService).auditUpdate(any(EventEntity.class));
            verify(eventProducer).publishAuctionEvent(any());
        }
    }

    @Test
    void syncBiddingFlagsUnMatchedData_UpdatesRegistration() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        LocalDateTime futureTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Timestamp futureTimestamp = Timestamp.valueOf(futureTime);
        
        EventEntity event = createEventEntity(
            "test-guid-1", 
            true, true, false,
            futureTimestamp, futureTimestamp, pastTimestamp
        );
        
        List<EventEntity> events = List.of(event);
        
        // Mock EventDetailsResponse.fromEntity
        try (MockedStatic<EventDetailsResponse> mockedStatic = mockStatic(EventDetailsResponse.class)) {
            EventDetailsResponse mockResponse = mock(EventDetailsResponse.class);
            mockedStatic.when(() -> EventDetailsResponse.fromEntity(any(), any())).thenReturn(mockResponse);

            // When
            service.syncBiddingFlagsUnMatchedData(events);

            // Then
            verify(eventService).saveEvents(eventsCaptor.capture());
            List<EventEntity> savedEvents = eventsCaptor.getValue();

            assertEquals(1, savedEvents.size());
            assertTrue(savedEvents.get(0).getIsRegistrationOpen());
            verify(auditService).auditUpdate(any(EventEntity.class));
            verify(eventProducer).publishAuctionEvent(any());
        }
    }

    @Test
    void syncBiddingFlagsUnMatchedData_UpdatesMultipleFlags() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        EventEntity event = createEventEntity(
            "test-guid-1", 
            false, false, false,
            pastTimestamp, pastTimestamp, pastTimestamp
        );
        
        List<EventEntity> events = List.of(event);
        
        // Mock EventDetailsResponse.fromEntity
        try (MockedStatic<EventDetailsResponse> mockedStatic = mockStatic(EventDetailsResponse.class)) {
            EventDetailsResponse mockResponse = mock(EventDetailsResponse.class);
            mockedStatic.when(() -> EventDetailsResponse.fromEntity(any(), any())).thenReturn(mockResponse);

            // When
            service.syncBiddingFlagsUnMatchedData(events);

            // Then
            verify(eventService).saveEvents(eventsCaptor.capture());
            List<EventEntity> savedEvents = eventsCaptor.getValue();

            assertEquals(1, savedEvents.size());
            assertTrue(savedEvents.get(0).getIsPriorityBidOpen());
            assertTrue(savedEvents.get(0).getIsTalBidOpen());
            assertTrue(savedEvents.get(0).getIsRegistrationOpen());
            verify(auditService).auditUpdate(any(EventEntity.class));
            verify(eventProducer).publishAuctionEvent(any());
        }
    }

    @Test
    void syncBiddingFlagsUnMatchedData_HandlesMultipleEvents() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        LocalDateTime futureTime = LocalDateTime.now().plus(1, ChronoUnit.DAYS);
        Timestamp futureTimestamp = Timestamp.valueOf(futureTime);
        
        EventEntity event1 = createEventEntity(
            "test-guid-1", 
            false, true, true,
            pastTimestamp, futureTimestamp, futureTimestamp
        );
        
        EventEntity event2 = createEventEntity(
            "test-guid-2", 
            true, false, true,
            futureTimestamp, pastTimestamp, futureTimestamp
        );
        
        EventEntity event3 = createEventEntity(
            "test-guid-3", 
            true, true, false,
            futureTimestamp, futureTimestamp, pastTimestamp
        );
        
        List<EventEntity> events = Arrays.asList(event1, event2, event3);
        
        // Mock EventDetailsResponse.fromEntity
        try (MockedStatic<EventDetailsResponse> mockedStatic = mockStatic(EventDetailsResponse.class)) {
            EventDetailsResponse mockResponse = mock(EventDetailsResponse.class);
            mockedStatic.when(() -> EventDetailsResponse.fromEntity(any(), any())).thenReturn(mockResponse);

            // When
            service.syncBiddingFlagsUnMatchedData(events);

            // Then
            verify(eventService).saveEvents(eventsCaptor.capture());
            List<EventEntity> savedEvents = eventsCaptor.getValue();

            assertEquals(3, savedEvents.size());
            verify(auditService, times(3)).auditUpdate(any(EventEntity.class));
            verify(eventProducer, times(3)).publishAuctionEvent(any());
        }
    }

    @Test
    void syncBiddingFlagsUnMatchedData_HandlesExceptionDuringProcessing() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        // Create a valid event that will be processed successfully
        EventEntity event1 = createEventEntity(
            "test-guid-1", 
            false, true, true,
            pastTimestamp, null, null
        );
        
        // Create an event that will cause an exception during processing
        // Instead of using null, create an event with missing required fields
        EventEntity event2 = EventEntity.builder()
                .guid("test-guid-2")
                .build(); // Missing most fields will cause NPE during processing

        List<EventEntity> events = Arrays.asList(event1, event2);
        
        // Mock the EventDetailsResponse.fromEntity method
        try (MockedStatic<EventDetailsResponse> mockedStatic = mockStatic(EventDetailsResponse.class)) {
            EventDetailsResponse mockResponse = mock(EventDetailsResponse.class);
            mockedStatic.when(() -> EventDetailsResponse.fromEntity(any(), any())).thenReturn(mockResponse);

            // When
            service.syncBiddingFlagsUnMatchedData(events);

            // Then
            // Verify that at least the first event was processed and saved
            verify(eventService).saveEvents(any());
            verify(eventProducer).publishAuctionEvent(any());
        }
    }

    @Test
    void syncBiddingFlagsUnMatchedData_HandlesExceptionDuringPublishing() {
        // Given
        LocalDateTime pastTime = LocalDateTime.now().minus(1, ChronoUnit.DAYS);
        Timestamp pastTimestamp = Timestamp.valueOf(pastTime);
        
        EventEntity event = createEventEntity(
            "test-guid-1", 
            false, true, true,
            pastTimestamp, null, null
        );
        
        List<EventEntity> events = List.of(event);
        
        doThrow(new RuntimeException("Test exception")).when(eventProducer).publishAuctionEvent(any());
        
        // When
        service.syncBiddingFlagsUnMatchedData(events);
        
        // Then
        verify(eventService).saveEvents(any());
        verify(eventProducer).publishAuctionEvent(any());
        // Test passes if no exception is thrown from the service method
    }
}