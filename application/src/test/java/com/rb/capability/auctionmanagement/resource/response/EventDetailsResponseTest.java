package com.rb.capability.auctionmanagement.resource.response;

import com.rb.capability.auctionmanagement.client.ContactType;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

public class EventDetailsResponseTest {

    private PlacesServiceClient placesServiceClient;

    @BeforeEach
    void setup() {
        placesServiceClient = mock(PlacesServiceClient.class);
    }

    @Test
    void testFromEntity() {
        EventEntity eventEntity = new EventEntity();
        eventEntity.setEventStartDate(new Timestamp(System.currentTimeMillis()));
        eventEntity.setEventEndDate(new Timestamp(System.currentTimeMillis()));
        String eventGuid = "test-eventGuid";
        eventEntity.setGuid(eventGuid);
        eventEntity.setTimezone("UTC");
        EventDetailsResponse expectedResponse = createMockEventDetailsResponse(eventGuid);
        EventDetailsResponse response = EventDetailsResponse.fromEntity(eventEntity, placesServiceClient);

        // Instead of comparing exact timestamps, verify they're not null
        assertNotNull(response.eventStartDate());
        assertNotNull(expectedResponse.eventStartDate());

        // Or compare only the date part, ignoring milliseconds
        assertEquals(
            response.eventStartDate().toString().substring(0, 19),
            expectedResponse.eventStartDate().toString().substring(0, 19)
        );
    }

    private EventDetailsResponse createMockEventDetailsResponse(String guid) {
        return new EventDetailsResponse(
                guid,                                   // 1. eventGuid
                "Test Location",                        // 2. locationId
                "Test Site",                            // 3. siteId
                "20240101",                             // 4. saleNumber
                "20240101-SOURCE",                      // 5. sourceAuctionNumber (added)
                "Test Event",                           // 6. name
                "Test Event",                           // 7. eventAdvertisedName
                2,                                      // 8. numberOfDays
                "RB",                                   // 9. brand
                "UTC",                                  // 10. timezone
                "123",                                  // 11. legalEntityId
                new Timestamp(System.currentTimeMillis()), // 12. eventStartDate
                new Timestamp(System.currentTimeMillis()), // 13. eventEndDate
                List.of("USD"),                         // 14. currency
                "INDUSTRIAL",                           // 15. primaryClassification
                "SCHEDULED",                            // 16. schedulingStatus
                "New",                                  // 17. status
                List.of(SellingFormat.TAL),             // 18. sellingFormats
                List.of(ParticipationMethod.ONSITE),    // 19. waysToParticipate
                true,                                   // 20. registrationOpen
                new Timestamp(System.currentTimeMillis()), // 21. registrationAutoOpenDate
                true,                                   // 22. talBidOpen
                new Timestamp(System.currentTimeMillis()), // 23. talBidAutoOpenDate
                true,                                   // 24. priorityBidOpen
                new Timestamp(System.currentTimeMillis()), // 25. priorityBidAutoOpenDate
                true,                                   // 26. olrEnabled
                new Timestamp(System.currentTimeMillis()), // 27. createdDate
                "testUser",                             // 28. lastModifierName
                new Timestamp(System.currentTimeMillis()), // 29. lastModifiedDate
                response,                               // 30. location
                siteResponse,                           // 31. site
                false,                                  // 32. isRbMarketplaceSale
                new BusinessUnit("123", "123 Test"),    // 33. businessUnit
                true,                                   // 34. allowIpInspection
                List.of("Industrial"),                  // 35. additionalClassification
                EventLocationType.ONLINE_WEB_BASED,      // 36. locationType
                "John,Doe",                             // 37. createdBy
                true,                                   // 38. privateTreaty
                "Test Site Configuration",              // 39. siteConfiguration
                "notes",                                // 40. notes
                new Timestamp(System.currentTimeMillis()), // 41. initialAuctionSyncDate
                new Timestamp(System.currentTimeMillis()), // 42. eventFinalizedDate
                false,
                false
        );
    }

    LocationResponse response = new LocationResponse(
            "123e4567-e89b-12d3-a456-426614174000", // id
            "PERMANENT_SITE",                        // type
            "Test Location",                         // name
            "123 Test St",                           // streetAddress1
            "Test City",                             // city
            "TS",                                    // stateProvinceCode
            "Test State",                            // stateProvince
            "TC",                                    // countryCode
            "Test Country",                          // country
            "12345",                                 // postalCode
            BigDecimal.valueOf(33.4255),                                 // latitude
            BigDecimal.valueOf(-112.1711),                               // longitude
            List.of(new ContactDetailsResponse(ContactType.ALTERNATE_INSPECTION, "John", "Doe", "123",
                    "456", "7890", "<EMAIL>"))                            // contactDetails (empty list if needed)
    );

    SiteResponse siteResponse = new SiteResponse("site-123", "Test Site", "Test Site", "", null);

}
