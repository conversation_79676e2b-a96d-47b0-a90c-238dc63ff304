package com.rb.capability.auctionmanagement.service;

import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.infra.jpa.repository.SaleNumberAllocationRepository;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload;
import com.rb.capability.auctionmanagement.resource.request.EventRolloverBatchPayload.EventRolloverRequest;
import com.rb.capability.auctionmanagement.resource.response.EventRolloverResponse;
import com.rb.capability.auctionmanagement.resource.response.SiteResponse;
import com.rb.capability.auctionmanagement.resource.response.ValidationResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.Year;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventRollOverServiceTest {

    @Mock
    private EventRepository eventRepository;

    @Mock
    private SaleNumberAllocationService saleNumberAllocationService;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @Mock
    private SaleNumberAllocationRepository saleNumberAllocationRepository;

    @InjectMocks
    private EventRollOverService eventRollOverService;

    private EventEntity sampleEvent;
    private final int year = Year.now().plusYears(1).getValue();

    @BeforeEach
    void setup() {
        sampleEvent = new EventEntity();
        sampleEvent.setSaleNumber("2025001");
        sampleEvent.setTimezone("UTC");
        sampleEvent.setSiteId("SITE123");
        sampleEvent.setEventStartDate(Timestamp.from(Instant.now()));
        sampleEvent.setEventEndDate(Timestamp.from(Instant.now().plusSeconds(86400 * 2)));
        sampleEvent.setLocationType(EventLocationType.PERMANENT);
    }

    @Test
    void testValidateRolloverBatch_success() {
        List<EventRolloverRequest> auctionList = List.of(
                new EventRolloverRequest(null, 2025001)
        );
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(auctionList, "user1");

        int nextYear = Year.now().plusYears(1).getValue();
        int start = Integer.parseInt(nextYear + "101");
        int end = Integer.parseInt(nextYear + "501");

        when(saleNumberAllocationService.countBySaleNumberBetweenAndInUseFalse(start, end)).thenReturn(5);

        ValidationResponse response = eventRollOverService.validateRolloverBatch(payload);

        assertTrue(response.isSuccess());
        assertTrue(response.getMessage().contains("Please confirm that"));
    }

    @Test
    void testValidateRolloverBatch_failure_dueToInsufficientSaleNumbers() {
        List<EventRolloverRequest> auctionList = List.of(
                new EventRolloverRequest(null, 2025001),
                new EventRolloverRequest(null, 2025002)
        );
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(auctionList, "user1");

        when(saleNumberAllocationService.countBySaleNumberBetweenAndInUseFalse(anyInt(), anyInt())).thenReturn(1);

        ValidationResponse response = eventRollOverService.validateRolloverBatch(payload);

        assertFalse(response.isSuccess());
        assertTrue(response.getMessage().contains("not enough unused auctions"));
    }

    @Test
    void testPrepareEventRollOver_successfulInsert() {
        EventRolloverRequest req = new EventRolloverRequest(null, 2025001);
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(List.of(req), "creator");

        when(eventRepository.findBySaleNumber("2025001")).thenReturn(Optional.of(sampleEvent));
        when(eventRepository.checkAuctionWithStartDateAndSiteId(any(), eq("SITE123"))).thenReturn(Optional.empty());
        when(saleNumberAllocationRepository.findNextSaleNumberByYearAndType(eq(year), anyString())).thenReturn(2025999);
        when(placesServiceClient.getSiteBySiteGuid("SITE123")).thenReturn(new SiteResponse("1", "Sample Site", null, null, null));
        when(eventRepository.save(any(EventEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));

        EventRolloverResponse response = eventRollOverService.prepareEventRollOver(payload);

        assertEquals(1, response.successCount());
        assertEquals(1, response.totalCount());
        assertTrue(response.failedAuctions().isEmpty());
    }

    @Test
    void testPrepareEventRollOver_eventNotFound() {
        EventRolloverRequest req = new EventRolloverRequest(null, 9999999);
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(List.of(req), "creator");

        when(eventRepository.findBySaleNumber("9999999")).thenReturn(Optional.empty());

        EventRolloverResponse response = eventRollOverService.prepareEventRollOver(payload);

        assertEquals(0, response.successCount());
        assertEquals(1, response.totalCount());
        assertEquals(List.of("9999999"), response.failedAuctions());
    }

    @Test
    void testPrepareEventRollOver_duplicateCheckFails() {
        EventRolloverRequest req = new EventRolloverRequest(null, 2025001);
        EventRolloverBatchPayload payload = new EventRolloverBatchPayload(List.of(req), "creator");

        when(eventRepository.findBySaleNumber("2025001")).thenReturn(Optional.of(sampleEvent));
        when(eventRepository.checkAuctionWithStartDateAndSiteId(any(), eq("SITE123"))).thenReturn(Optional.of("DUPLICATE"));

        EventRolloverResponse response = eventRollOverService.prepareEventRollOver(payload);

        assertEquals(0, response.successCount());
        assertEquals(1, response.totalCount());
        assertEquals(List.of("2025001"), response.failedAuctions());
    }
}
