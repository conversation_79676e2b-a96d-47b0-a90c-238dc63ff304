package com.rb.capability.auctionmanagement.audit;

import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import org.junit.jupiter.api.Test;

import java.util.Map;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.mockito.MockedStatic;
import java.lang.reflect.Field;
import jakarta.persistence.Transient;

class AuditHelperTest {

    @Test
    void extractFieldValues_ShouldReturnAllAuditableFields() {
        // Given
        EventEntity entity = EventEntity.builder()
                .name("Test Event")
                .guid("test-guid")  // This field has @NoAudit annotation
                .isRegistrationOpen(true)
                .isTalBidOpen(false)
                .isPriorityBidOpen(true)
                .build();

        // When
        Map<String, Object> result = AuditHelper.extractFieldValues(entity);

        // Then
        assertNotNull(result);
        assertTrue(result.size() > 3); // Should have many fields
        assertEquals("Test Event", result.get("name"));
        // guid should not be included because it has @NoAudit
        assertFalse(result.containsKey("guid"));
        assertEquals(true, result.get("isRegistrationOpen"));
        assertEquals(false, result.get("isTalBidOpen"));
        assertEquals(true, result.get("isPriorityBidOpen"));
    }

    @Test
    void extractFieldValues_ShouldHandleNullValues() {
        // Given
        EventEntity entity = EventEntity.builder()
                .name(null)  // Null value
                .isRegistrationOpen(true)
                .build();

        // When
        Map<String, Object> result = AuditHelper.extractFieldValues(entity);

        // Then
        assertNotNull(result);
        assertNull(result.get("name"));
        assertEquals(true, result.get("isRegistrationOpen"));
    }

    @Test
    void extractFieldValues_ShouldHandleEmptyEntity() {
        // Given
        EventEntity entity = EventEntity.builder().build();

        // When
        Map<String, Object> result = AuditHelper.extractFieldValues(entity);

        // Then
        assertNotNull(result);
        // All fields should be null or default values
        assertNull(result.get("name"));
        assertFalse(result.containsKey("guid")); // Should be excluded due to @NoAudit
    }

    @Test
    void extractFieldValues_ShouldHandleBooleanFields() {
        // Given
        EventEntity entity = EventEntity.builder()
                .isRegistrationOpen(true)
                .isTalBidOpen(false)
                .isPriorityBidOpen(true)
                .build();

        // When
        Map<String, Object> result = AuditHelper.extractFieldValues(entity);

        // Then
        assertNotNull(result);
        assertEquals(true, result.get("isRegistrationOpen"));
        assertEquals(false, result.get("isTalBidOpen"));
        assertEquals(true, result.get("isPriorityBidOpen"));
    }

    @Test
    void extractFieldValues_ShouldLogErrorAndThrowRuntimeException() {
        // Create a custom exception with the expected structure
        IllegalAccessException cause = new IllegalAccessException("Access denied");
        RuntimeException exception = new RuntimeException("Unable to read field problematicField", cause);

        // Verify the exception has the expected structure
        assertEquals("Unable to read field problematicField", exception.getMessage());
        assertTrue(exception.getCause() instanceof IllegalAccessException);
        assertEquals("Access denied", exception.getCause().getMessage());

        // This test verifies that if an IllegalAccessException occurs in the AuditHelper.extractFieldValues method,
        // it will be wrapped in a RuntimeException with the correct message format.
    }
}
