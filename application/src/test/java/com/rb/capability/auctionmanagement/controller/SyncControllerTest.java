package com.rb.capability.auctionmanagement.controller;

import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.resource.response.EventDetailsResponse;
import com.rb.capability.auctionmanagement.service.AsyncEventService;
import com.rb.capability.auctionmanagement.service.EventService;
import com.rb.capability.common.exception.GlobalExceptionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.sql.Timestamp;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class SyncControllerTest {

    private MockMvc mockMvc;

    @Mock
    private EventService eventService;

    @Mock
    private AsyncEventService asyncEventService;

    @Mock
    private PlacesServiceClient placesServiceClient;

    @InjectMocks
    private SyncController syncController;

    private static final String EVENT_GUID = "test-guid-123";
    private EventEntity testEvent;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .standaloneSetup(syncController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();

        testEvent = EventEntity.builder()
                .guid(EVENT_GUID)
                .saleNumber("SALE-123")
                .timezone("America/New_York")
                .eventStartDate(new Timestamp(System.currentTimeMillis()))
                .eventEndDate(new Timestamp(System.currentTimeMillis() + 86400000)) // next day
                .brand("RB")
                .eventAdvertisedName("Test Event")
                .primaryClassification("Equipment")
                .status("NEW")
                .build();
    }

    @Test
    void retriggerSalesforceCall_Success() throws Exception {
        // Given
        EventEntity testEvent = EventEntity.builder()
                .guid(EVENT_GUID)
                .saleNumber("SALE-123")
                .timezone("America/New_York")
                .eventStartDate(new Timestamp(System.currentTimeMillis()))
                .eventEndDate(new Timestamp(System.currentTimeMillis() + 86400000))
                .brand("RB")
                .eventAdvertisedName("Test Event")
                .primaryClassification("Equipment")
                .status("NEW")
                .build();

        when(eventService.findEventByGuid(EVENT_GUID)).thenReturn(Optional.of(testEvent));
        when(asyncEventService.callSalesforceAsync(any(EventDetailsResponse.class)))
                .thenReturn(CompletableFuture.completedFuture(null));

        // When & Then
        mockMvc.perform(post("/retry/salesforce/{eventGuid}", EVENT_GUID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Salesforce API call retriggered successfully for event: " + EVENT_GUID));

        verify(eventService).findEventByGuid(EVENT_GUID);
        verify(asyncEventService).callSalesforceAsync(any(EventDetailsResponse.class));
    }

    @Test
    void retriggerSalesforceCall_EventNotFound() throws Exception {
        // Given
        when(eventService.findEventByGuid(EVENT_GUID)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(post("/retry/salesforce/{eventGuid}", EVENT_GUID))
                .andExpect(status().isNotFound());

        verify(eventService).findEventByGuid(EVENT_GUID);
        verify(asyncEventService, never()).callSalesforceAsync(any());
    }

    @Test
    void retriggerSalesforceCall_ThrowsException() throws Exception {
        // Given
        when(eventService.findEventByGuid(EVENT_GUID)).thenReturn(Optional.of(testEvent));
        when(asyncEventService.callSalesforceAsync(any(EventDetailsResponse.class)))
                .thenThrow(new RuntimeException("Salesforce API error"));

        // When & Then
        mockMvc.perform(post("/retry/salesforce/{eventGuid}", EVENT_GUID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("Salesforce API error"));

        verify(eventService).findEventByGuid(EVENT_GUID);
        verify(asyncEventService).callSalesforceAsync(any(EventDetailsResponse.class));
    }

    @Test
    void retriggerKafkaPublish_Success() throws Exception {
        // Given
        when(eventService.findEventByGuid(EVENT_GUID)).thenReturn(Optional.of(testEvent));
        when(placesServiceClient.getLocationByLocationGuid(any())).thenReturn(null);
        when(placesServiceClient.getSiteBySiteGuid(any())).thenReturn(null);
        when(asyncEventService.publishAuctionEventAsync(any(EventDetailsResponse.class)))
            .thenReturn(CompletableFuture.completedFuture(null));

        // When & Then
        mockMvc.perform(post("/retry/publish/{eventGuid}", EVENT_GUID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Kafka event publishing retriggered successfully for event: " + EVENT_GUID));

        verify(eventService).findEventByGuid(EVENT_GUID);
        verify(asyncEventService).publishAuctionEventAsync(any(EventDetailsResponse.class));
    }

    @Test
    void retriggerKafkaPublish_EventNotFound() throws Exception {
        // Given
        when(eventService.findEventByGuid(EVENT_GUID)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(post("/retry/publish/{eventGuid}", EVENT_GUID))
                .andExpect(status().isNotFound());

        verify(eventService).findEventByGuid(EVENT_GUID);
        verify(asyncEventService, never()).publishAuctionEventAsync(any());
    }

    @Test
    void retriggerSalesforceCall_WithCrlfInEventGuid() throws Exception {
        // Given
        String eventGuidWithCrlf = "test\r\nguid\n123";
        when(eventService.findEventByGuid(eventGuidWithCrlf)).thenReturn(Optional.of(testEvent));

        // When & Then
        mockMvc.perform(post("/retry/salesforce/{eventGuid}", eventGuidWithCrlf))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Salesforce API call retriggered successfully for event: " + eventGuidWithCrlf));

        verify(eventService).findEventByGuid(eventGuidWithCrlf);
        verify(asyncEventService).callSalesforceAsync(any());
    }

    @Test
    void retriggerKafkaPublish_WithCrlfInEventGuid() throws Exception {
        // Given
        String eventGuidWithCrlf = "test\r\nguid\n123";
        when(eventService.findEventByGuid(eventGuidWithCrlf)).thenReturn(Optional.of(testEvent));

        // When & Then
        mockMvc.perform(post("/retry/publish/{eventGuid}", eventGuidWithCrlf))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Kafka event publishing retriggered successfully for event: " + eventGuidWithCrlf));

        verify(eventService).findEventByGuid(eventGuidWithCrlf);
        verify(asyncEventService).publishAuctionEventAsync(any());
    }
}
