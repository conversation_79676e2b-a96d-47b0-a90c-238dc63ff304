package com.rb.capability.uitests;

import com.microsoft.playwright.Page;
import org.junit.jupiter.api.*;

import java.time.LocalDate;

import static com.microsoft.playwright.assertions.PlaywrightAssertions.assertThat;


@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class UiTest extends AuctionManagementActions {


    @Test
    @Order(1)
    void auctionCreate() {
        // Execute the entire auction creation test with retry capability
        executeTestWithRetryOnSuccessFailure(() -> {
            loginAuctionManagement();
            createNewAuction();
            hostSiteSelection("Corner Brook , NL, CAN - Jun 27, 2019");
            dateSelection();
            auctionlocationTypePermanent();
            page.locator("xpath=//p[text()='Next']").click();
            page.waitForTimeout(1000);
            createNewAuctionButton();
            advertisedName();
            additionalClassification();
            brandSelection();
            legalEntity();
            currencySelection();
            businessUnit();
            siteConfigration();
            auctionNotes("Permanent-This is a test note!@#$%^&*()/*&^5$#1234567890-=__+}{:?><,./;'][-");
            createNewAuctionButton();

            // Validate success message - if this fails, the entire test will be retried
            validateSuccessMessage();

            page.locator("xpath=//p[normalize-space()='Open Manage Auction']").click();
        }, "auctionCreate", 3);
    }

    @Test
    @Order(2)
    void auctionCreateOntheFarm() {
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Makwa, SK, CAN - Jun 20, 2019");
        dateSelection();
        auctionlocationTypeOntheFarm();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        addOffsiteLocation();
        auctionNotes("On the Farm-This is a test note!@#$%^&*()/*&^5$#1234567890-=__+}{:?><,./;'][-");
        createNewAuctionButton();

    }

    @Test
    @Order(3)
    void auctionCreateRegionalAuctionSite() {

        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Clandonald, AB, CAN - Apr 10, 2019");
        dateSelection();
        auctionlocationTypeRegionalAuctionSite();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        auctionNotes("Regional AuctionSite-This is a test note!@#$%^&*()/*&^5$#1234567890-=__+}{:?><,./;'][-");
        createNewAuctionButton();
        successMessageRequired();

    }

    @Test
    @Order(4)
    void auctionCreateOffsite() {

        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Elkview, WV, USA - Sep 3, 2019");
        dateSelection();
        auctionlocationTypeOffSite();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        addOffsiteLocation();
        auctionNotes("OffSite-This is a test note!@#$%^&*()/*&^5$#1234567890-=__+}{:?><,./;'][-");
        createNewAuctionButton();
        successMessageRequired();

    }

    @Test
    @Order(5)
    void manageAuction() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchAndSelectAuction("2026605");
            Assertions.assertEquals("2026605", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(1000);

            String expectedNameForManage = "Noonan, ND, USA - Apr 3, 2019, ND, USA - Jun 05, 2026";
            Assertions.assertEquals(expectedNameForManage, page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan76048256", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            page.waitForTimeout(1000);
            manageAuctionAssertions();
        }, 1);
    }

    @Test
    @Order(6)
    void manageAuctionUpdateScheduleStatus() {

        loginAuctionManagement();
        searchAndSelectAuction("2026605");
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-TAL']").click();
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-OLR']").click();
        submitbuttonClick();
        page.waitForTimeout(1000);
        searchAndSelectAuction("2026605");
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-OLR']").click();
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-TAL']").click();
        submitbuttonClick();
        page.waitForTimeout(8000);
    }


    @Test
    @Order(7)
    void manageAuctionName() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchAuctionName("Automation Testing Auction Name");
            page.waitForTimeout(3000);
            String auctionNumber = findFirstAuctionInResults();
            if (auctionNumber == null || auctionNumber.isEmpty()) {
                throw new RuntimeException("No auction found in search results for 'Automation Testing Auction Name'");
            }
            clickOnAuctionInResults(auctionNumber);
            page.waitForSelector("xpath=//input[@id='saleNumber--0']");
            String actualSaleNumber = page.locator("xpath=//input[@id='saleNumber--0']").inputValue();
            Assertions.assertEquals(auctionNumber, actualSaleNumber);
            waitForElementVisible(page, page.locator("xpath=//input[@id='updatedName--0']"));
            waitForElementVisible(page, page.locator("xpath=//input[@id='updatedAdvertisedname--0']"));
            page.waitForTimeout(2000);
            String actualName = page.locator("xpath=//input[@id='updatedName--0']").inputValue();
            String actualAdvertisedName = page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue();

            Assertions.assertTrue(actualName.contains("Automation Testing Auction Name"),
                    "Expected auction name to contain 'Automation Testing Auction Name', but was: " + actualName);
            Assertions.assertTrue(actualAdvertisedName.contains("Automation"),
                    "Expected advertised name to contain 'Automation', but was: " + actualAdvertisedName);
            hostSiteUpdate("Noonan, ND, USA - Apr 3, 2019");
            page.waitForTimeout(2000);
            closeModalIfPresent();
            searchAuctionName("Automation Testing Auction Name");
            page.waitForTimeout(3000);
            clickOnAuctionInResults(auctionNumber);
            page.waitForSelector("xpath=//input[@id='saleNumber--0']", new Page.WaitForSelectorOptions().setTimeout(10000));
            String verifyActualSaleNumber = page.locator("xpath=//input[@id='saleNumber--0']").inputValue();
            Assertions.assertEquals(auctionNumber, verifyActualSaleNumber);
            page.waitForTimeout(2000);
            String updatedHostSite = page.locator("xpath=//input[@id='hostSite--0']").inputValue();
            Assertions.assertEquals("Noonan, ND, USA - Apr 3, 2019", updatedHostSite);

        }, 1);
    }

    @Test
    @Order(8)
    void manageServerName() {
        loginAuctionManagement();
        retryOperation(() -> {

            searchAuctionNumber("2026605");
            searchServerName("Noonan, ND, USA - Apr 3, 2019");
            page.waitForSelector("xpath=//span[normalize-space()='2026605']");
            page.locator("xpath=//span[normalize-space()='2026605']").hover();
            page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
            page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
            Assertions.assertEquals("2026605", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(1000);

            String expectedNameForUpdate = "Noonan, ND, USA - Apr 3, 2019, ND, USA - Jun 05, 2026";
            Assertions.assertEquals(expectedNameForUpdate, page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan76048256", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            page.waitForTimeout(1000);
            manageAuctionAssertions();
        }, 1);
    }

    @Test
    @Order(9)
    void manageAuctionNumber() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchAuctionNumber("2026605");
            page.waitForSelector("xpath=//span[normalize-space()='2026605']");
            page.locator("xpath=//span[normalize-space()='2026605']").hover();
            page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
            page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
            Assertions.assertEquals("2026605", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(1000);
            String expectedNameForNumber = "Noonan, ND, USA - Apr 3, 2019, ND, USA - Jun 05, 2026";
            Assertions.assertEquals(expectedNameForNumber, page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan76048256", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            page.waitForTimeout(1000);
            manageAuctionAssertions();
        }, 1);
    }

    @Test
    @Order(10)
    void manageRegistrationTalDate() {
        loginAuctionManagement();
        searchAuctionNumber("2026630");
        page.waitForSelector("xpath=//span[normalize-space()='2026630']");
        page.locator("xpath=//span[normalize-space()='2026630']").hover();
        page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
        page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
        page.locator("xpath=//span[normalize-space()='Registration Open?']").click();
        page.locator("xpath=//input[@id='registrationDate--0']").click();
        LocalDate today = LocalDate.now();
        page.locator("xpath=//button[@aria-label='" + today + "']").click();
        page.locator("xpath=//input[@id='registrationTime--0']").click();
        page.locator("xpath=//input[@id='registrationTime--0']").fill("12:00");
        page.locator("xpath=//div[contains(@id,'registrationTime--0-time-option')]").nth(7).click();
        page.click("body");
        page.locator("xpath=//span[normalize-space()='TAL Bid Open?']").click();
        page.locator("xpath=//input[@id='talbidDate--0']").click();
        page.locator("xpath=//button[@aria-label='" + today + "']").click();
        page.locator("xpath=//input[@id='talbidTime--0']").click();
        page.locator("xpath=//input[@id='talbidTime--0']").fill("12:00");
        page.locator("xpath=//div[contains(@id,'talbidTime--0-time-option')]").nth(7).click();
        page.click("body");
        page.waitForTimeout(1000);
        page.locator("xpath=//span[normalize-space()='Priority Bidding Open?']").click();
        page.locator("xpath= //input[@id='priorityDate--0']").click();
        page.locator("xpath=//input[@id='priorityDate--0']").click();
        page.locator("xpath=//button[@aria-label='" + today + "']").click();
        page.locator("xpath=//input[@id='priorityTime--0']").click();
        page.locator("xpath=//input[@id='priorityTime--0']").fill("12:00");
        page.waitForTimeout(1000); // Wait for dropdown to appear
        page.locator("xpath=//div[contains(@id,'priorityTime--0-time-option')]").nth(7).click();
        page.click("body");
        submitbuttonClick();
    }

    @Test
    @Order(11)
    void viewAuctionDetails() {

        loginAuctionManagement();
        searchAuctionNumber("2026605");
        page.waitForSelector("xpath=//span[normalize-space()='2026605']");
        page.locator("xpath=//span[normalize-space()='2026605']").hover();
        page.waitForSelector("xpath=//button[@aria-label='View']//*[name()='svg']");
        page.locator("xpath=//button[@aria-label='View']//*[name()='svg']").click();
        page.waitForTimeout(1000);
        manageAuctionAssertions();
    }

    @Test
    @Order(12)
    void viewHistory() {

        loginAuctionManagement();
        searchAuctionNumber("2026605");
        page.waitForSelector("xpath=//span[normalize-space()='2026605']");
        page.locator("xpath=//span[normalize-space()='2026605']").hover();
        page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M.12,7.12a')]");
        page.locator("xpath=//*[name()='path' and contains(@d,'M.12,7.12a')]").click();
        page.waitForSelector("xpath=//h3[normalize-space()='History of Event - Noonan76048256']");
        Assertions.assertEquals("History of Event - Noonan76048256", page.locator("xpath=//h3[normalize-space()='History of Event - Noonan76048256']").innerText());
        page.waitForTimeout(1000);
        page.locator("xpath=//div[@id='EventAuditList--0']/div[@role='presentation']/div[@role='presentation']/div[@role='row']/div[1]").innerText();

    }

    @Test
    @Order(13)
    void auctionCreatePrivateTreaty() {
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Oklahoma City, OK - RB Energy");
        dateSelection();
        auctionlocationTypePermanent();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        privateTreaty();
        auctionNotes("Private Treaty-This is a test note!@#$%^&*()/*&^5$#1234567890-=__+}{:?><,./;'][-");
        createNewAuctionButton();
        successMessage();
        page.waitForTimeout(1000);
        page.locator("xpath=//p[normalize-space()='Open Manage Auction']").click();
        page.waitForTimeout(1000);
        page.locator("xpath=//h4[normalize-space()='Auction Details']").click();
        page.waitForTimeout(1000);
        assertThat(page.locator("xpath=//span[normalize-space()='Private Treaty']")).isVisible();
        assertThat(page.locator("xpath=//input[@id='auctionFormatsUpdate--0-TAL']")).isHidden();
        assertThat(page.locator("xpath=//input[@id='auctionFormatsUpdate--0-OLR']")).isHidden();
        assertThat(page.locator("xpath=//input[@id='auctionFormatsUpdate--0-ONLINE_AUCTION']")).isHidden();
        assertThat(page.locator("xpath=//input[@id='auctionFormatsUpdate--0-MARKETPLACE']")).isHidden();
        page.waitForTimeout(1000);
        page.locator("xpath=//div[contains(text(),'Confirmed')]").click();
        page.waitForTimeout(1000);
        submitbuttonClick();
        page.waitForTimeout(1000);
        assertThat(page.locator("xpath=//span[normalize-space()='Private Treaty']")).isDisabled();
        page.waitForTimeout(1000);
    }


    @Test
    @Order(14)
    void auctionCopy() {
        loginAuctionManagement();
        createNewCopyAuction();
        searchCopyAuctionNumber("2026142");
        dateCopySelection();
        page.keyboard().press("Tab");
        page.keyboard().press("Tab");
        page.keyboard().press("Tab");
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(2000);
        page.locator("xpath=//button[@role='button']//p[contains(text(),'Create Auction')]").click();
        page.waitForTimeout(5000);
        page.waitForSelector("xpath=//*[contains(text(),'Event Created Successfully')]");
        page.locator("xpath=//*[contains(text(),'Event Created Successfully')]").isVisible();
        page.waitForTimeout(5000);
        String message = page.locator("xpath=//*[contains(text(),'Event Created Successfully')]").innerText();
        String saleNumber = message.replaceAll("\\D+", "");
        page.waitForSelector("xpath=//input[@id='searchAuction--0']");
        page.locator("xpath=//button[@aria-label='Close']//div[@class='_main_c3auw_5 _center_c3auw_15']").click();
        searchAuctionNumber(saleNumber);
        page.waitForSelector("xpath=//span[normalize-space()='" + saleNumber + "']");
        page.locator("xpath=//span[normalize-space()='" + saleNumber + "']").hover();
        page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
        page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
        String expectedDate = getLastSelectedStartDateFormatted();
        String expectedName = "Sacramento, CA, USA - " + expectedDate;
        Assertions.assertEquals(expectedName, page.locator("xpath=//input[@id='updatedName--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals(expectedName, page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
        page.waitForTimeout(1000);
        manageCopyAuctionAssertions();

    }

    @Test
    @Order(15)
    void auctionDuplicateCheck() {

        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Corner Brook , NL, CAN - Jun 27, 2019");
        staticDateSelection();
        auctionlocationTypePermanent();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        createNewAuctionButton();
        errorMessage();

    }

}