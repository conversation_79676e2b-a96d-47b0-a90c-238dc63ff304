package com.rb.capability.uitests;

import org.junit.jupiter.api.extension.*;
import org.junit.platform.commons.logging.Logger;
import org.junit.platform.commons.logging.LoggerFactory;

import java.lang.reflect.Method;

/**
 * Custom JUnit 5 extension that retries tests when success message validation fails
 */
public class SuccessMessageRetryExtension implements TestExecutionExceptionHandler, InvocationInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(SuccessMessageRetryExtension.class);
    private static final String RETRY_COUNT_KEY = "retryCount";
    private static final String MAX_RETRIES_KEY = "maxRetries";
    
    @Override
    public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        // Check if this is a success message related failure
        if (isSuccessMessageFailure(throwable)) {
            int currentRetryCount = getRetryCount(context);
            int maxRetries = getMaxRetries(context);
            
            if (currentRetryCount < maxRetries) {
                logger.info(() -> String.format("Test failed due to success message issue. Retry %d of %d", 
                    currentRetryCount + 1, maxRetries));
                
                // Increment retry count
                context.getStore(ExtensionContext.Namespace.GLOBAL)
                    .put(RETRY_COUNT_KEY, currentRetryCount + 1);
                
                // Reset test state before retry
                resetTestState(context);
                
                // Don't propagate the exception - this will cause the test to be retried
                return;
            }
        }
        
        // If not a success message failure or max retries reached, propagate the exception
        throw throwable;
    }
    
    @Override
    public void interceptTestMethod(Invocation<Void> invocation, ReflectionTestDescriptor testDescriptor, 
                                   ExtensionContext extensionContext) throws Throwable {
        
        // Initialize retry count for this test
        initializeRetryCount(extensionContext);
        
        int maxRetries = getMaxRetries(extensionContext);
        int attempt = 0;
        Throwable lastException = null;
        
        while (attempt <= maxRetries) {
            try {
                logger.info(() -> String.format("Executing test attempt %d of %d", attempt + 1, maxRetries + 1));
                
                // Execute the test
                invocation.proceed();
                
                // If we reach here, test passed
                logger.info(() -> "Test passed on attempt " + (attempt + 1));
                return;
                
            } catch (Throwable throwable) {
                lastException = throwable;
                
                if (isSuccessMessageFailure(throwable) && attempt < maxRetries) {
                    logger.warn(() -> String.format("Test failed on attempt %d due to success message issue: %s", 
                        attempt + 1, throwable.getMessage()));
                    
                    // Reset test state before retry
                    resetTestState(extensionContext);
                    
                    // Wait before retry
                    try {
                        Thread.sleep(5000); // 5 second delay between retries
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Test retry interrupted", e);
                    }
                    
                    attempt++;
                } else {
                    // Not a success message failure or max retries reached
                    throw throwable;
                }
            }
        }
        
        // If we reach here, all retries failed
        logger.error(() -> String.format("Test failed after %d attempts", maxRetries + 1));
        throw lastException;
    }
    
    /**
     * Check if the throwable is related to success message failure
     */
    private boolean isSuccessMessageFailure(Throwable throwable) {
        if (throwable == null) return false;
        
        String message = throwable.getMessage();
        if (message == null) return false;
        
        // Check for success message related keywords
        return message.toLowerCase().contains("success message") ||
               message.toLowerCase().contains("event created successfully") ||
               message.toLowerCase().contains("success message not found") ||
               message.toLowerCase().contains("required success message") ||
               message.toLowerCase().contains("test incomplete");
    }
    
    /**
     * Initialize retry count for the test
     */
    private void initializeRetryCount(ExtensionContext context) {
        context.getStore(ExtensionContext.Namespace.GLOBAL).put(RETRY_COUNT_KEY, 0);
        
        // Get max retries from annotation or use default
        RetryOnSuccessMessageFailure annotation = context.getRequiredTestMethod()
            .getAnnotation(RetryOnSuccessMessageFailure.class);
        
        int maxRetries = annotation != null ? annotation.maxRetries() : 3;
        context.getStore(ExtensionContext.Namespace.GLOBAL).put(MAX_RETRIES_KEY, maxRetries);
    }
    
    /**
     * Get current retry count
     */
    private int getRetryCount(ExtensionContext context) {
        return context.getStore(ExtensionContext.Namespace.GLOBAL)
            .get(RETRY_COUNT_KEY, Integer.class);
    }
    
    /**
     * Get max retries
     */
    private int getMaxRetries(ExtensionContext context) {
        return context.getStore(ExtensionContext.Namespace.GLOBAL)
            .get(MAX_RETRIES_KEY, Integer.class);
    }
    
    /**
     * Reset test state before retry
     */
    private void resetTestState(ExtensionContext context) {
        try {
            logger.info(() -> "Resetting test state before retry...");
            
            // Get the test instance
            Object testInstance = context.getRequiredTestInstance();
            
            // If it's an AuctionManagementActions instance, reset its state
            if (testInstance instanceof AuctionManagementActions) {
                AuctionManagementActions actions = (AuctionManagementActions) testInstance;
                actions.resetTestState();
            }
            
            logger.info(() -> "Test state reset completed");
            
        } catch (Exception e) {
            logger.warn(() -> "Failed to reset test state: " + e.getMessage());
        }
    }
}
