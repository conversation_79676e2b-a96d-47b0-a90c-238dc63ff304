package com.rb.capability.uitests;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;

/**
 * Example test class demonstrating the usage of the integrated retry mechanism
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class RetryExampleTest extends AuctionManagementActions {

    /**
     * Example test with retry annotation - will retry up to 3 times if success message fails
     */
    @Test
    @Order(1)
    @AuctionManagementActions.RetryOnSuccessMessageFailure(maxRetries = 3, delayMs = 5000)
    void exampleAuctionCreateWithRetry() {
        System.out.println("=== Example Test with Retry Mechanism ===");
        
        // Standard test flow
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Corner Brook , NL, CAN - Jun 27, 2019");
        dateSelection();
        auctionlocationTypePermanent();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        auctionNotes("Example test with retry mechanism");
        createNewAuctionButton();
        
        // This will trigger retry if success message is not found
        validateSuccessMessage();
        
        page.locator("xpath=//p[normalize-space()='Open Manage Auction']").click();
    }

    /**
     * Example test using simple retry method
     */
    @Test
    @Order(2)
    void exampleWithSimpleRetry() {
        System.out.println("=== Example Test with Simple Retry Method ===");
        
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Makwa, SK, CAN - Jun 20, 2019");
        dateSelection();
        auctionlocationTypeOntheFarm();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        addOffsiteLocation();
        auctionNotes("Example with simple retry");
        createNewAuctionButton();
        
        // This will throw exception if success message is not found
        ensureSuccessOrRetry();
    }

    /**
     * Example test using basic success message validation (no retry)
     */
    @Test
    @Order(3)
    void exampleWithBasicValidation() {
        System.out.println("=== Example Test with Basic Validation (No Retry) ===");
        
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Noonan, ND, USA - Apr 3, 2019");
        dateSelection();
        auctionlocationTypeRegionalAuctionSite();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        auctionNotes("Example with basic validation");
        createNewAuctionButton();
        
        // Basic success message validation (will fail test if not found)
        successMessageRequired();
    }

    /**
     * Example test with custom retry configuration
     */
    @Test
    @Order(4)
    @AuctionManagementActions.RetryOnSuccessMessageFailure(maxRetries = 5, delayMs = 3000, takeScreenshots = true)
    void exampleWithCustomRetryConfig() {
        System.out.println("=== Example Test with Custom Retry Configuration ===");
        System.out.println("Max retries: 5, Delay: 3 seconds, Screenshots: enabled");
        
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Corner Brook , NL, CAN - Jun 27, 2019");
        dateSelection();
        auctionlocationTypeOffSite();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        addOffsiteLocation();
        auctionNotes("Example with custom retry config");
        createNewAuctionButton();
        
        // This will use the custom retry configuration
        validateSuccessMessage();
    }
}
