package com.rb.capability.uitests;

import com.rb.capability.base.BaseTest;
import org.junit.jupiter.api.Test;

/**
 * Helper class to test and debug SSO authentication issues
 */
public class AuthenticationTestHelper extends BaseTest {

    /**
     * Test method to validate and refresh authentication
     * Run this test first to establish fresh authentication
     */
    @Test
    void establishFreshAuthentication() {
        System.out.println("=== Authentication Test Helper ===");
        
        // Clear any existing storage state
        clearStorageState();
        System.out.println("Cleared existing storage state");
        
        // Create fresh browser context
        page.navigate(baseUrl);
        page.waitForTimeout(3000);
        
        // Check current authentication status
        boolean isAuthenticated = checkAuthenticationStatus();
        
        if (!isAuthenticated) {
            System.out.println("❌ Not authenticated - please complete SSO login manually");
            performManualSSOLogin();
        } else {
            System.out.println("✅ Already authenticated");
        }
        
        // Save the current state
        saveCurrentStorageState();
        System.out.println("✅ Authentication state saved for future tests");
    }

    /**
     * Test method to validate existing storage state
     */
    @Test
    void validateStorageState() {
        System.out.println("=== Validating Storage State ===");
        
        // Navigate to application
        page.navigate(baseUrl);
        page.waitForTimeout(3000);
        
        // Check authentication status
        boolean isAuthenticated = checkAuthenticationStatus();
        
        if (isAuthenticated) {
            System.out.println("✅ Storage state is valid - authentication working");
        } else {
            System.out.println("❌ Storage state is invalid - authentication failed");
            System.out.println("💡 Run 'establishFreshAuthentication' test to fix this");
        }
    }

    /**
     * Check if user is currently authenticated
     */
    private boolean checkAuthenticationStatus() {
        try {
            // Check for welcome message
            if (page.locator("xpath=//h3[normalize-space()='Welcome, Sasank Kumar']").count() > 0) {
                System.out.println("✅ Found welcome message - authenticated");
                return true;
            }
            
            // Check for dashboard
            if (page.locator("xpath=//h5[normalize-space()='Auction Management Dashboard v2']").count() > 0) {
                System.out.println("✅ Found dashboard - authenticated");
                return true;
            }
            
            // Check if on login page
            if (page.locator("xpath=//p[normalize-space()='Sign in with SSO']").count() > 0) {
                System.out.println("❌ On login page - not authenticated");
                return false;
            }
            
            System.out.println("❓ Authentication status unclear");
            return false;
            
        } catch (Exception e) {
            System.err.println("Error checking authentication: " + e.getMessage());
            return false;
        }
    }

    /**
     * Perform manual SSO login with extended timeout
     */
    private void performManualSSOLogin() {
        try {
            System.out.println("🔐 Starting manual SSO login process...");
            
            // Click SSO button
            page.waitForSelector("xpath=//p[normalize-space()='Sign in with SSO']", 
                new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(10000));
            page.locator("xpath=//p[normalize-space()='Sign in with SSO']").click();
            
            System.out.println("📝 Please complete SSO authentication in the browser...");
            System.out.println("⏳ Waiting up to 3 minutes for authentication to complete...");
            
            // Wait for authentication to complete (3 minutes timeout)
            page.waitForSelector("xpath=//h3[normalize-space()='Welcome, Sasank Kumar']", 
                new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(180000));
            
            System.out.println("✅ Manual SSO authentication completed successfully!");
            
            // Navigate to dashboard
            if (page.locator("xpath=//h5[normalize-space()='Auction Management Dashboard v2']").count() > 0) {
                page.locator("xpath=//h5[normalize-space()='Auction Management Dashboard v2']").click();
                System.out.println("✅ Navigated to dashboard");
            }
            
        } catch (Exception e) {
            System.err.println("❌ Manual SSO login failed: " + e.getMessage());
            throw new RuntimeException("Manual SSO authentication failed", e);
        }
    }
}
