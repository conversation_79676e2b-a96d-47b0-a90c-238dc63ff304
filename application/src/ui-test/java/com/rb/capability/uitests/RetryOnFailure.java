package com.rb.capability.uitests;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark tests that should be retried if they fail to reach success message
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RetryOnFailure {
    /**
     * Maximum number of retry attempts
     */
    int maxRetries() default 3;
    
    /**
     * Delay between retries in milliseconds
     */
    long delayMs() default 5000;
}
