package com.rb.capability.uitests;

import org.junit.jupiter.api.*;

/**
 * UI Test class containing only test methods.
 * This class extends AuctionManagementActionsExtended to inherit all common action methods.
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class UiTestNew extends AuctionManagementActionsExtended {

    @Test
    @Order(1)
    void auctionCreate() {
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Corner Brook , NL, CAN - Jun 27, 2019");
        dateSelection();
        auctionlocationTypePermanent();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        createNewAuctionButton();
        successMessage();
    }

    @Test
    @Order(2)
    void auctionCreateOntheFarm() {
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Noonan, ND, USA - Apr 3, 2019");
        dateSelection();
        auctionlocationTypeOntheFarm();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        addOffsiteLocation();
        createNewAuctionButton();
        successMessage();
    }

    @Test
    @Order(3)
    void auctionCreateRegionalAuctionSite() {
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Noonan, ND, USA - Apr 3, 2019");
        dateSelection();
        auctionlocationTypeRegionalAuctionSite();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        createNewAuctionButton();
        successMessage();
    }

    @Test
    @Order(4)
    void auctionCreateOffsite() {
        loginAuctionManagement();
        createNewAuction();
        hostSiteSelection("Noonan, ND, USA - Apr 3, 2019");
        dateSelection();
        auctionlocationTypeOffSite();
        page.locator("xpath=//p[text()='Next']").click();
        page.waitForTimeout(1000);
        createNewAuctionButton();
        advertisedName();
        additionalClassification();
        brandSelection();
        legalEntity();
        currencySelection();
        businessUnit();
        siteConfigration();
        addOffsiteLocation();
        createNewAuctionButton();
        successMessage();
    }

    @Test
    @Order(5)
    void manageAuction() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchAndSelectAuction("2026605");
            Assertions.assertEquals("2026605", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan, ND, USA - Apr 3, 2019, ND, USA - Jun 05, 2026", page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan76048256", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            page.waitForTimeout(1000);
            manageAuctionAssertions();
        }, 1);
    }

    @Test
    @Order(6)
    void manageAuctionUpdateScheduleStatus() {
        loginAuctionManagement();
        searchAndSelectAuction("2026605");
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-TAL']").click();
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-OLR']").click();
        submitbuttonClick();
        page.waitForTimeout(1000);
        searchAndSelectAuction("2026605");
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-OLR']").click();
        page.locator("xpath=//input[@id='auctionFormatsUpdate--0-TAL']").click();
        submitbuttonClick();
        page.waitForTimeout(8000);
    }

    @Test
    @Order(7)
    void manageAuctionName() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchAuctionName("Automation Testing Auction Name");
            page.locator("xpath=//span[normalize-space()='2026634']").hover();
            page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
            page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
            Assertions.assertEquals("2026634", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(2000);
            Assertions.assertEquals("Automation Testing Auction Name", page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(2000);
            Assertions.assertEquals("Automation Advertised Name", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            hostSiteUpdate("Noonan, ND, USA - Apr 3, 2019");
            page.waitForTimeout(2000);
            page.locator("xpath=//input[@id='updatedName--0']").clear();
            page.locator("xpath=//input[@id='updatedName--0']").fill("Automation Testing Auction Name Updated");
            page.locator("xpath=//input[@id='updatedAdvertisedname--0']").clear();
            page.locator("xpath=//input[@id='updatedAdvertisedname--0']").fill("Automation Advertised Name Updated");
            submitbuttonClick();
            page.waitForTimeout(2000);
        }, 1);
    }

    @Test
    @Order(8)
    void manageServerName() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchServerName("Automation Testing Server Name");
            page.locator("xpath=//span[normalize-space()='2026634']").hover();
            page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
            page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
            Assertions.assertEquals("2026634", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(2000);
            Assertions.assertEquals("Automation Testing Auction Name Updated", page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(2000);
            Assertions.assertEquals("Automation Advertised Name Updated", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            page.waitForTimeout(2000);
        }, 1);
    }

    @Test
    @Order(9)
    void manageAuctionNumber() {
        loginAuctionManagement();
        retryOperation(() -> {
            searchAuctionNumber("2026605");
            page.waitForSelector("xpath=//span[normalize-space()='2026605']");
            page.locator("xpath=//span[normalize-space()='2026605']").hover();
            page.waitForSelector("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]");
            page.locator("xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]").click();
            Assertions.assertEquals("2026605", page.locator("xpath=//input[@id='saleNumber--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan, ND, USA - Apr 3, 2019, ND, USA - Jun 05, 2026", page.locator("xpath=//input[@id='updatedName--0']").inputValue());
            page.waitForTimeout(1000);
            Assertions.assertEquals("Noonan76048256", page.locator("xpath=//input[@id='updatedAdvertisedname--0']").inputValue());
            page.waitForTimeout(1000);
        }, 1);
    }
}