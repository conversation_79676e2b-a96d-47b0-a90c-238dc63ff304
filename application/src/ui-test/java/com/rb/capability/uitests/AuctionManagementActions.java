package com.rb.capability.uitests;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.MouseButton;
import com.rb.capability.base.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.Month;
import java.util.Arrays;
import java.util.List;


public class AuctionManagementActions extends BaseTest {

    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @ExtendWith(SuccessMessageRetryExtension.class)
    public @interface RetryOnSuccessMessageFailure {
        int maxRetries() default 3;

        long delayMs() default 5000;


    }


    public static class SuccessMessageRetryExtension implements TestExecutionExceptionHandler {


        @Override
        public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {

            if (isSuccessMessageFailure(throwable)) {

                RetryOnSuccessMessageFailure annotation = context.getRequiredTestMethod()
                        .getAnnotation(RetryOnSuccessMessageFailure.class);

                if (annotation != null) {
                    int maxRetries = annotation.maxRetries();
                    long delayMs = annotation.delayMs();

                    ExtensionContext.Store store = context.getStore(ExtensionContext.Namespace.create(context.getRequiredTestMethod()));
                    Integer currentRetryCount = store.get("retryCount", Integer.class);
                    if (currentRetryCount == null) {
                        currentRetryCount = 0;
                    }

                    if (currentRetryCount < maxRetries) {
                        store.put("retryCount", currentRetryCount + 1);
                        resetTestState(context);

                        try {
                            Thread.sleep(delayMs);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("Test retry interrupted", e);
                        }
                        return;
                    }
                }
            }

            throw throwable;
        }


        private boolean isSuccessMessageFailure(Throwable throwable) {

            if (throwable == null) {
                return false;
            }
            String message = throwable.getMessage();
            if (message == null) {
                return false;
            }


            return message.toLowerCase().contains("success message")
                    || message.toLowerCase().contains("event created successfully")
                    || message.toLowerCase().contains("success message not found")
                    || message.toLowerCase().contains("required success message")
                    || message.toLowerCase().contains("test incomplete")
                    || message.toLowerCase().contains("failed to create event")
                    || message.toLowerCase().contains("event creation failed");
        }


        private void resetTestState(ExtensionContext context) {
            try {
                Object testInstance = context.getRequiredTestInstance();
                if (testInstance instanceof AuctionManagementActions) {
                    AuctionManagementActions actions = (AuctionManagementActions) testInstance;
                    actions.resetTestState();
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to reset test state before retry", e);
            }
        }
    }


    public static class SuccessMessageNotFoundException extends RuntimeException {
        public SuccessMessageNotFoundException(String message) {
            super(message);
        }

        public SuccessMessageNotFoundException(String message, Throwable cause) {
            super(message, cause);
        }
    }


    protected static final SecureRandom secRandom = new SecureRandom();
    static LocalDate lastSelectedStartDate;
    static LocalDate lastSelectedEndDate;

    protected static String getFormattedDateForAssertion(LocalDate date) {
        if (date == null) {
            return "Jun 05, 2026";
        }
        return date.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy"));
    }

    protected static String getLastSelectedStartDateFormatted() {
        return getFormattedDateForAssertion(lastSelectedStartDate);
    }

    protected String findFirstAuctionInResults() {
        try {

            page.waitForTimeout(2000);
            List<String> selectors = Arrays.asList(
                    "xpath=//span[contains(@class, 'auction-number') or contains(text(), '202')]",
                    "xpath=//div[@role='row']//span[matches(text(), '^\\d{7}$')]",
                    "xpath=//span[matches(text(), '^202\\d{4}$')]",
                    "xpath=//div[contains(@class, 'result')]//span[matches(text(), '^\\d{7}$')]"
            );

            for (String selector : selectors) {
                try {
                    Locator auctionElements = page.locator(selector);
                    if (auctionElements.count() > 0) {
                        String auctionNumber = auctionElements.first().innerText().trim();
                        if (auctionNumber.matches("\\d{7}")) { // 7-digit auction number
                            return auctionNumber;
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to find auction in results");
                }
            }

            try {
                Locator allSpans = page.locator("xpath=//span");
                int count = allSpans.count();
                for (int i = 0; i < count; i++) {
                    String text = allSpans.nth(i).innerText().trim();
                    if (text.matches("\\d{7}")) {
                        return text;
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to find auction in results");
            }

            return null;
        } catch (Exception e) {
            throw new RuntimeException("Failed to find auction in results");
        }
    }

    protected void auctionNotes(String notes) {
        retryOperation(() -> {
            Locator notesField = page.locator("xpath=//textarea[@id='auctionNotesCreate--0']");
            waitForElementEnabled(page, notesField);
            waitForElementVisible(page, notesField);
            notesField.clear();
            notesField.fill(notes);

        }, 1);
    }

    protected void clickOnAuctionInResults(String auctionNumber) {
        retryOperation(() -> {

            Locator auctionSpan = page.locator("xpath=//span[normalize-space()='" + auctionNumber + "']");
            waitForElementVisible(page, auctionSpan);
            auctionSpan.hover();
            page.waitForTimeout(1000);
            List<String> editButtonSelectors = Arrays.asList(
                    "xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]",
                    "xpath=//button[@aria-label='Edit']",
                    "xpath=//button[contains(@class, 'edit')]",
                    "xpath=//*[name()='svg' and contains(@class, 'edit')]",
                    "xpath=//div[contains(@class, 'edit-icon')]"
            );

            boolean clicked = false;
            for (String selector : editButtonSelectors) {
                try {
                    Locator editButton = page.locator(selector);
                    if (editButton.isVisible()) {
                        editButton.click();
                        clicked = true;
                        break;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to click on auction in results");
                }
            }

            if (!clicked) {

                auctionSpan.click();
            }

            page.waitForTimeout(2000);
        }, 2);
    }


    protected void closeModalIfPresent() {
        try {

            List<String> closeSelectors = Arrays.asList(
                    "xpath=//button[@aria-label='Close']",
                    "xpath=//button[contains(@class, 'close')]",
                    "xpath=//*[name()='svg' and contains(@class, 'close')]",
                    "xpath=//div[@class='_icon_c3auw_29']//*[name()='svg']",
                    "xpath=//button[@aria-label='Close']//div[@class='_icon_c3auw_29']//*[name()='svg']"
            );

            for (String selector : closeSelectors) {
                try {
                    Locator closeButton = page.locator(selector);
                    if (closeButton.isVisible()) {
                        closeButton.click();
                        page.waitForTimeout(1000);
                        return;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to close modal");
                }
            }
            page.keyboard().press("Escape");
            page.waitForTimeout(500);

        } catch (Exception e) {
            throw new RuntimeException("Failed to close modal");
        }
    }

    protected void loginAuctionManagement() {
        retryOperation(() -> {
            page.navigate(baseUrl);
            page.waitForURL(baseUrl);
            waitForElementVisible(page, page.locator("xpath=//p[normalize-space()='Sign in with SSO']"));
            if (page.locator("xpath=//p[normalize-space()='Sign in with SSO']").isVisible()) {
                page.locator("xpath=//p[normalize-space()='Sign in with SSO']").click();
            }

            page.waitForSelector("xpath=//h3[normalize-space()='Welcome, Sasank Kumar']");
            page.waitForSelector("xpath=//h5[normalize-space()='Auction Management Dashboard v2']");
            page.locator("xpath=//h5[normalize-space()='Auction Management Dashboard v2']").click();
            page.waitForTimeout(5000);
        }, 1);
    }

    void retryOperation(Runnable operation, int maxRetries) {
        int attempts = 0;
        boolean success = false;
        Exception lastException = null;

        while (attempts < maxRetries && !success) {
            try {
                operation.run();
                success = true;
            } catch (Exception e) {
                lastException = e;
                attempts++;
                page.waitForTimeout(1000);
            }
        }

        if (!success && lastException != null) {
            throw new RuntimeException("Operation failed after " + maxRetries + " attempts", lastException);
        }
    }

    void submitbuttonClick() {
        retryOperation(() -> {
            Locator submitButton = page.locator("xpath=//p[normalize-space()='Submit']");
            waitForElementVisible(page, submitButton);
            waitForElementEnabled(page, submitButton);
            page.waitForTimeout(2000);
            try {
                submitButton.click(new Locator.ClickOptions().setForce(true));
                page.waitForTimeout(5000);
                page.keyboard().press("Enter");
            } catch (Exception e) {

                page.evaluate("document.querySelector('button.nIczNC.NGAsrY.Fh7Sak').click()");
                page.waitForTimeout(5000);
                page.keyboard().press("Enter");
            }
        }, 1);
    }

    void searchAndSelectAuction(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='searchAuction--0']");
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
            page.waitForTimeout(2000); // Wait for search results to load
            Locator resultClick = page.getByTestId("ListBox::ListBoxItem::0").locator("div").first();
            waitForElementVisible(page, resultClick);
            resultClick.click();
        }, 1);
    }

    void searchAuctionNumber(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath= //input[@id='auction_number--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
        }, 1);
    }

    void searchCopyAuctionNumber(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='searchAuction2--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
            page.waitForTimeout(2000); // Wait for search results to load
            Locator resultClick = page.getByTestId("ListBox::ListBoxItem::0").locator("div").first();
            waitForElementVisible(page, resultClick);
            resultClick.click();
        }, 1);
    }

    void searchAuctionName(String auctionName) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='auction_number--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionName);

        }, 1);
    }

    void searchServerName(String serverName) {
        retryOperation(() -> {
            Locator searchServerField = page.locator("xpath=//input[@id='table1SearchFilter2--0']");
            locateElementAndFillWithValue(page, searchServerField);
            searchServerField.fill(serverName);
            page.waitForTimeout(2000);

        }, 1);
    }

    void createNewAuctionButton() {
        retryOperation(() -> {

            page.evaluate("() => { if (document.activeElement) document.activeElement.blur(); }");
            page.waitForTimeout(500);

            try {
                boolean clicked = (boolean) page.evaluate("() => { "
                        + "  try {"
                        + "    const button = document.querySelector('[data-testid=\"RetoolGrid:formButton2\"] button'); "
                        + "    if (button) { "
                        + "      console.log('Found button by data-testid, clicking...'); "
                        + "      button.scrollIntoView({behavior: 'auto', block: 'center'}); "
                        + "      button.focus(); "
                        + "      button.dispatchEvent(new MouseEvent('mousedown', {bubbles: true, cancelable: true})); "
                        + "      button.dispatchEvent(new MouseEvent('mouseup', {bubbles: true, cancelable: true})); "
                        + "      button.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true})); "
                        + "      button.click(); "
                        + "      return true; "
                        + "    } "
                        + "    return false;"
                        + "  } catch(e) { "
                        + "    console.error('Direct click failed:', e); "
                        + "    return false; "
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            try {
                boolean clicked = (boolean) page.evaluate("() => { "
                        + "  try {"
                        + "    const formButton = document.getElementById('formButton2--0'); "
                        + "    if (formButton) { "
                        + "      console.log('Found button container, clicking child button...'); "
                        + "      const button = formButton.querySelector('button'); "
                        + "      if (button) { "
                        + "        button.scrollIntoView({behavior: 'auto', block: 'center'}); "
                        + "        button.focus(); "
                        + "        button.dispatchEvent(new MouseEvent('mousedown', {bubbles: true, cancelable: true})); "
                        + "        button.dispatchEvent(new MouseEvent('mouseup', {bubbles: true, cancelable: true})); "
                        + "        button.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true})); "
                        + "        button.click(); "
                        + "        return true; "
                        + "      } "
                        + "    } "
                        + "    return false;"
                        + "  } catch(e) { "
                        + "    console.error('Container click failed:', e); "
                        + "    return false; "
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            try {
                boolean clicked = (boolean) page.evaluate("() => {"
                        + "  try {"
                        + "    // Find by text content"
                        + "    let buttons = Array.from(document.querySelectorAll('button, [role=\"button\"]'));"
                        + "    let button = buttons.find(b => b.textContent && b.textContent.includes('Create Auction'));"
                        + "    if (button) {"
                        + "      button.click();"
                        + "      return true;"
                        + "    }"
                        + "    return false;"
                        + "  } catch(e) {"
                        + "    console.error('Simple click failed:', e);"
                        + "    return false;"
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            List<String> selectors = Arrays.asList(
                    "[data-testid='RetoolGrid:formButton2'] button",
                    "button:has-text('Create Auction')",
                    "#formButton2--0 button",
                    "//button[contains(., 'Create Auction')]",
                    "//p[contains(text(),'Create Auction')]/.."
            );

            for (String selector : selectors) {
                try {
                    Locator button = page.locator(selector).first();
                    if (button.count() > 0) {
                        button.click(new Locator.ClickOptions().setForce(true).setButton(MouseButton.LEFT).setTimeout(5000));
                        page.waitForTimeout(1000);
                        page.keyboard().press("Enter");
                        return;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to click button with text: Create Auction");
                }
            }


            try {
                page.evaluate("() => {"
                        + "  document.querySelectorAll('form').forEach(form => {"
                        + "    try { form.submit(); } catch(e) { console.error('Form submit failed:', e); }"
                        + "  });"
                        + "}");
                page.waitForTimeout(1000);
                page.keyboard().press("Enter");
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }
            throw new RuntimeException("Failed to click button with text: Create Auction");
        }, 1);
    }

    void privateTreaty() {
        retryOperation(() -> {
            Locator privateTreatyButton = page.locator("xpath=//input[@id='switchGroup1--0-Private Treaty']");
            waitForElementVisible(page, privateTreatyButton);
            waitForElementEnabled(page, privateTreatyButton);
            privateTreatyButton.click();
        }, 1);
    }

    void skipDuplicateCheck() {

        Locator skipDuplicateCheckButton = page.locator("xpath=//span[normalize-space()='Skip Duplicate Check']");
        waitForElementVisible(page, skipDuplicateCheckButton);
        waitForElementEnabled(page, skipDuplicateCheckButton);
        skipDuplicateCheckButton.click();
    }

    protected void dateCopySelection() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='auction_dates_copy--0']");
            page.locator("xpath=//input[@id='auction_dates_copy--0']").elementHandle().isVisible();
            page.locator("xpath=//input[@id='auction_dates_copy--0']").click();
            page.waitForTimeout(1000);
            Locator nextMonthButton = page.locator("xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]");
            Locator nextMonthButton1 = page.locator("xpath=//button[@aria-label='Next month']//*[name()='svg']");

            for (int i = 0; i < 11; i++) {
                if (nextMonthButton.isVisible()) {
                    nextMonthButton.click(new Locator.ClickOptions().setForce(true));
                } else if (nextMonthButton1.isVisible()) {
                    nextMonthButton1.click(new Locator.ClickOptions().setForce(true));
                }
            }

            LocalDate currentDate = LocalDate.now();
            LocalDate baseDate = currentDate.plusYears(1);
            int randomDays = secRandom.nextInt(30) + 1;
            LocalDate specificDate = baseDate.plusDays(randomDays);
            String formattedDate = specificDate.toString();
            lastSelectedStartDate = specificDate;
            page.locator("xpath=//button[@aria-label='" + formattedDate + "']").click();
            LocalDate tenDaysAfter = specificDate.plusDays(10);
            String formattedEndDate = tenDaysAfter.toString();
            lastSelectedEndDate = tenDaysAfter;


            if (tenDaysAfter.getMonth() != specificDate.getMonth()) {
                Locator nextMonthButtonForEnd = page.locator("xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]");
                Locator nextMonthButton1ForEnd = page.locator("xpath=//button[@aria-label='Next month']//*[name()='svg']");

                if (nextMonthButtonForEnd.isVisible()) {
                    nextMonthButtonForEnd.click(new Locator.ClickOptions().setForce(true));
                } else if (nextMonthButton1ForEnd.isVisible()) {
                    nextMonthButton1ForEnd.click(new Locator.ClickOptions().setForce(true));
                }
                page.waitForTimeout(1000); // Wait for month transition
            }

            page.locator("xpath=//button[@aria-label='" + formattedEndDate + "']").click();
            page.click("body");
            page.waitForTimeout(1000);
        }, 1);
    }


    protected void dateSelection() {
        retryOperation(() -> {

            openDatePicker();


            LocalDate currentDate = LocalDate.now();
            LocalDate targetStartDate = generateSameYearFutureDate(currentDate);
            LocalDate targetEndDate = targetStartDate.plusDays(2); // 2-day duration


            if (targetEndDate.getYear() != targetStartDate.getYear()) {

                targetStartDate = targetStartDate.minusDays(3);
                targetEndDate = targetStartDate.plusDays(2);
            }


            navigateToDateAndSelect(targetStartDate, "start");
            lastSelectedStartDate = targetStartDate;

            if (targetEndDate.getMonth() == targetStartDate.getMonth()) {

                selectDateInPicker(targetEndDate);
            } else {

                navigateToDateAndSelect(targetEndDate, "end");
            }
            lastSelectedEndDate = targetEndDate;
            closeDatePicker();


        }, 3); // Increased retry count for date selection
    }


    private void openDatePicker() {
        try {

            page.waitForSelector("xpath=//input[@id='auctionDates2--0']",
                    new Page.WaitForSelectorOptions().setTimeout(10000));

            Locator dateInput = page.locator("xpath=//input[@id='auctionDates2--0']");
            if (!dateInput.isVisible()) {
                throw new RuntimeException("Date input field is not visible");
            }


            dateInput.click();
            page.waitForTimeout(2000); // Wait for date picker to open


            if (!isDatePickerOpen()) {
                throw new RuntimeException("Date picker failed to open");
            }


        } catch (Exception e) {

            throw new RuntimeException("Failed to open date picker", e);
        }
    }


    private boolean isDatePickerOpen() {
        try {
            // Check for common date picker elements
            List<String> datePickerSelectors = Arrays.asList(
                    "xpath=//div[contains(@class,'react-datepicker')]",
                    "xpath=//div[contains(@class,'datepicker')]",
                    "xpath=//div[@role='dialog']//button[@aria-label]",
                    "xpath=//button[@aria-label='Next month']",
                    "xpath=//button[@aria-label='Previous month']"
            );

            for (String selector : datePickerSelectors) {
                if (page.locator(selector).count() > 0 && page.locator(selector).first().isVisible()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }


    private LocalDate generateRandomFutureDate(LocalDate currentDate) {

        int monthsToAdd = 6 + secRandom.nextInt(12); // 6-18 months
        LocalDate baseDate = currentDate.plusMonths(monthsToAdd);
        int randomDays = 1 + secRandom.nextInt(28);
        return baseDate.withDayOfMonth(1).plusDays(randomDays - 1);
    }


    private LocalDate generateSameYearFutureDate(LocalDate currentDate) {

        int targetYear = currentDate.getYear() + 2;


        int randomMonth = 1 + secRandom.nextInt(12);


        int maxDay;
        if (randomMonth == 12) {
            maxDay = 28; // Leave room for 2-day duration in December
        } else {
            maxDay = 28; // Safe for all months
        }

        int randomDay = 1 + secRandom.nextInt(maxDay);

        LocalDate targetDate = LocalDate.of(targetYear, randomMonth, randomDay);


        LocalDate endDate = targetDate.plusDays(2);
        if (endDate.getYear() != targetDate.getYear()) {

            targetDate = LocalDate.of(targetYear, randomMonth, Math.min(randomDay, 28));
        }

        return targetDate;
    }


    private void navigateToDateAndSelect(LocalDate targetDate, String dateType) {
        try {

            LocalDate currentPickerDate = getCurrentPickerDate();
            navigateToTargetMonth(currentPickerDate, targetDate);
            selectDateInPicker(targetDate);

        } catch (Exception e) {

            throw new RuntimeException("Failed to select " + dateType + " date: " + targetDate, e);
        }
    }


    private LocalDate getCurrentPickerDate() {
        try {
            // Try different selectors for month/year display
            List<String> monthYearSelectors = Arrays.asList(
                    "xpath=//div[contains(@class,'react-datepicker__current-month')]",
                    "xpath=//div[contains(@class,'datepicker-header')]",
                    "xpath=//span[contains(@class,'month')]",
                    "xpath=//button[contains(@class,'month')]"
            );

            for (String selector : monthYearSelectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        String monthYearText = page.locator(selector).first().innerText();
                        return parseMonthYearText(monthYearText);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to get current picker date", e);
                }
            }


            return LocalDate.now();

        } catch (Exception e) {

            return LocalDate.now();
        }
    }


    private LocalDate parseMonthYearText(String monthYearText) {
        try {

            String[] parts = monthYearText.trim().split("\\s+");

            if (parts.length >= 2) {
                String monthPart = parts[0];
                String yearPart = parts[1];

                int year = Integer.parseInt(yearPart);
                int month = parseMonthName(monthPart);

                return LocalDate.of(year, month, 1);
            }
            return LocalDate.now();

        } catch (Exception e) {

            return LocalDate.now();
        }
    }


    private int parseMonthName(String monthName) {
        String month = monthName.toLowerCase();
        switch (month) {
            case "january":
            case "jan":
                return 1;
            case "february":
            case "feb":
                return 2;
            case "march":
            case "mar":
                return 3;
            case "april":
            case "apr":
                return 4;
            case "may":
                return 5;
            case "june":
            case "jun":
                return 6;
            case "july":
            case "jul":
                return 7;
            case "august":
            case "aug":
                return 8;
            case "september":
            case "sep":
                return 9;
            case "october":
            case "oct":
                return 10;
            case "november":
            case "nov":
                return 11;
            case "december":
            case "dec":
                return 12;
            default:
                return LocalDate.now().getMonthValue();
        }
    }


    private void navigateToTargetMonth(LocalDate currentDate, LocalDate targetDate) {
        try {
            int monthsToNavigate = calculateMonthsDifference(currentDate, targetDate);

            boolean navigateForward = monthsToNavigate > 0;
            int absoluteMonths = Math.abs(monthsToNavigate);

            for (int i = 0; i < absoluteMonths && i < 24; i++) { // Safety limit
                if (navigateForward) {
                    clickNextMonth();
                } else {
                    clickPreviousMonth();
                }
                page.waitForTimeout(500); // Wait between clicks
            }

        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Calculate months difference between two dates
     */
    private int calculateMonthsDifference(LocalDate from, LocalDate to) {
        return (to.getYear() - from.getYear()) * 12 + (to.getMonthValue() - from.getMonthValue());
    }

    /**
     * Click next month button with multiple selector fallbacks
     */
    private void clickNextMonth() {
        List<String> nextMonthSelectors = Arrays.asList(
                "xpath=//button[@aria-label='Next month']",
                "xpath=//button[contains(@class,'next')]",
                "xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]",
                "xpath=//button[@aria-label='Next month']//*[name()='svg']"
        );

        for (String selector : nextMonthSelectors) {
            try {
                Locator button = page.locator(selector);
                if (button.count() > 0 && button.first().isVisible()) {
                    button.first().click(new Locator.ClickOptions().setForce(true));
                    return;
                }
            } catch (Exception e) {
                // Continue to next selector
            }
        }

        throw new RuntimeException("Could not find next month button");
    }


    private void clickPreviousMonth() {
        List<String> prevMonthSelectors = Arrays.asList(
                "xpath=//button[@aria-label='Previous month']",
                "xpath=//button[contains(@class,'prev')]",
                "xpath=//*[name()='path' and contains(@d,'M14 8L12')]",
                "xpath=//button[@aria-label='Previous month']//*[name()='svg']"
        );

        for (String selector : prevMonthSelectors) {
            try {
                Locator button = page.locator(selector);
                if (button.count() > 0 && button.first().isVisible()) {
                    button.first().click(new Locator.ClickOptions().setForce(true));
                    return;
                }
            } catch (Exception e) {
                // Continue to next selector
            }
        }

        throw new RuntimeException("Could not find previous month button");
    }


    private void selectDateInPicker(LocalDate targetDate) {
        try {
            String dateString = targetDate.toString(); // Format: YYYY-MM-DD

            // Try multiple selector patterns for date buttons
            List<String> dateSelectors = Arrays.asList(
                    "xpath=//button[@aria-label='" + dateString + "']",
                    "xpath=//button[contains(@aria-label,'" + targetDate.getDayOfMonth() + "')]",
                    "xpath=//td[@aria-label='" + dateString + "']//button",
                    "xpath=//div[@aria-label='" + dateString + "']",
                    "xpath=//button[text()='" + targetDate.getDayOfMonth() + "']"
            );

            for (String selector : dateSelectors) {
                try {
                    Locator dateButton = page.locator(selector);
                    if (dateButton.count() > 0 && dateButton.first().isVisible()) {

                        dateButton.first().click();
                        page.waitForTimeout(1000);
                        return;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to select date with selector: " + selector, e);
                }
            }

            throw new RuntimeException("Could not find date button for: " + targetDate);

        } catch (Exception e) {
            System.err.println("Error selecting date: " + e.getMessage());
            throw e;
        }
    }

    private void closeDatePicker() {
        try {
            // Click outside the date picker to close it
            page.click("body");
            page.waitForTimeout(1000);

            // Verify date picker is closed
            if (isDatePickerOpen()) {
                // Try pressing Escape key
                page.keyboard().press("Escape");
                page.waitForTimeout(1000);
            }


        } catch (Exception e) {
            System.err.println("Warning: Could not close date picker: " + e.getMessage());
        }
    }

    protected void staticDateSelection() {
        retryOperation(() -> {


            openDatePicker();
            LocalDate specificStartDate = LocalDate.of(2026, Month.JUNE, 5);
            LocalDate specificEndDate = specificStartDate.plusDays(2); // Changed from 10 to 2 days


            navigateToDateAndSelect(specificStartDate, "start");
            lastSelectedStartDate = specificStartDate;

            if (specificEndDate.getMonth() == specificStartDate.getMonth()) {

                selectDateInPicker(specificEndDate);
            } else {
                navigateToDateAndSelect(specificEndDate, "end");
            }
            lastSelectedEndDate = specificEndDate;

            closeDatePicker();

        }, 3);
    }


    protected void debugDatePicker() {
        try {

            openDatePicker();

            List<String> containerSelectors = Arrays.asList(
                    "xpath=//div[contains(@class,'react-datepicker')]",
                    "xpath=//div[contains(@class,'datepicker')]",
                    "xpath=//div[@role='dialog']"
            );

            for (String selector : containerSelectors) {
                int count = page.locator(selector).count();
                if (count > 0) {
                    throw new RuntimeException("Failed to find date picker container: " + selector);
                }
            }

            // Check for navigation buttons
            List<String> navSelectors = Arrays.asList(
                    "xpath=//button[@aria-label='Next month']",
                    "xpath=//button[@aria-label='Previous month']",
                    "xpath=//button[contains(@class,'next')]",
                    "xpath=//button[contains(@class,'prev')]"
            );

            for (String selector : navSelectors) {
                int count = page.locator(selector).count();
                if (count > 0) {
                    throw new RuntimeException("Failed to find navigation button: " + selector);
                }
            }

            // Check for month/year display
            List<String> headerSelectors = Arrays.asList(
                    "xpath=//div[contains(@class,'current-month')]",
                    "xpath=//div[contains(@class,'header')]",
                    "xpath=//span[contains(@class,'month')]"
            );

            for (String selector : headerSelectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        String text = page.locator(selector).first().innerText();
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to get month/year header text", e);
                }
            }


            String dateButtonSelector = "xpath=//button[@aria-label]";
            int count = page.locator(dateButtonSelector).count();
            if (count > 0) {

                for (int i = 0; i < Math.min(10, count); i++) {
                    try {
                        String ariaLabel = page.locator(dateButtonSelector).nth(i).getAttribute("aria-label");
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to get aria-label for date button", e);
                    }
                }
            }


        } catch (Exception e) {
            throw new RuntimeException("Failed to debug date picker: " + e.getMessage(), e);
        }
    }

    protected void dateSelectionWithDebug() {
        try {
            debugDatePicker();
            dateSelection();
        } catch (Exception e) {
            throw new RuntimeException("Failed to select dates with debug: " + e.getMessage(), e);

        }
    }

    void addOffsiteLocation() {
        retryOperation(() -> {
            page.locator("xpath=//p[normalize-space()='Add / Edit Offsite Location']").click();
            page.locator("xpath=//p[normalize-space()='Submit']").click();
            page.keyboard().press("Enter");
            page.waitForTimeout(4000);
            page.locator("xpath=//input[@id='locationName--0']").fill("Test RB Location");
            retryOperation(() -> {
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").click();
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").fill("America/L");
                page.keyboard().press("Backspace");
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").fill("America/L");
                page.getByTestId("ListBox::ListBoxItem::1").click();
            }, 1);
            page.locator("xpath=//input[@id='addressLookup2--0']").fill("700 Ritchie Rd, Davenport, FL, USA");
            page.locator("xpath=//*[contains(text(),'700 Ritchie Rd, Davenport, FL, USA')]").first().click();
            page.waitForTimeout(2000);
            page.locator("xpath=//input[@id='phoneNumber--0']").fill("9885346424");
            page.locator("xpath=//input[@id='email--0']").fill("rbcom");
            Assertions.assertEquals("Please enter a valid email address.", page.locator("xpath=//div[contains(text(),'Please enter a valid email address.')]").innerText());
            page.waitForTimeout(4000);
            page.keyboard().press("Backspace");
            page.locator("xpath=//input[@id='email--0']").fill("<EMAIL>");
            page.locator("xpath=//p[normalize-space()='Submit']").click();
            page.keyboard().press("Enter");
            Locator addEditLink = page.locator("xpath=//p[normalize-space()='Add / Edit Offsite Location']");
            waitForElementVisible(page, addEditLink);
            page.waitForTimeout(2000);
        }, 1);
    }

    void createNewAuction() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForSelector("xpath=//*[contains(text(),'Create New')]");
            page.locator("xpath=//*[contains(text(),'Create New')]").click();
        }, 1);
    }

    /**
     * Enhanced rollforward auction creation with new tab handling
     */
    void createNewAuctionRollOver() {
        retryOperation(() -> {
            System.out.println("=== Starting Rollforward Auction Creation ===");

            // Click Create Auction
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForTimeout(1000);

            // Click Roll Forward - this may open a new tab
            page.waitForSelector("xpath=//*[contains(text(),'Roll Forward')]");

            // Store current page count before clicking
            int initialPageCount = page.context().pages().size();
            System.out.println("Initial page count: " + initialPageCount);

            page.locator("xpath=//*[contains(text(),'Roll Forward')]").click();
            page.waitForTimeout(3000); // Wait for potential new tab

            // Check if new tab opened
            int newPageCount = page.context().pages().size();
            System.out.println("New page count: " + newPageCount);

            if (newPageCount > initialPageCount) {
                System.out.println("✅ New tab detected - switching to rollforward tab");
                handleRollforwardNewTab();
            } else {
                System.out.println("ℹ️ No new tab - continuing in current tab");
            }

            System.out.println("✅ Rollforward auction creation initiated");
        }, 2);
    }

    /**
     * Handle rollforward functionality when it opens in a new tab
     */
    private void handleRollforwardNewTab() {
        try {
            // Get all pages (tabs)
            var pages = page.context().pages();

            // Find the rollforward tab (usually the newest one)
            Page rollforwardPage = null;
            for (int i = pages.size() - 1; i >= 0; i--) {
                Page currentPage = pages.get(i);
                try {
                    // Wait a bit for the page to load
                    currentPage.waitForTimeout(2000);

                    // Check if this page contains rollforward elements
                    if (isRollforwardPage(currentPage)) {
                        rollforwardPage = currentPage;
                        break;
                    }
                } catch (Exception e) {
                    System.out.println("Page " + i + " not ready or not rollforward page");
                }
            }

            if (rollforwardPage != null) {
                System.out.println("✅ Found rollforward page - switching context");

                // Switch to the rollforward page
                this.page = rollforwardPage;

                // Wait for rollforward page to be fully loaded
                waitForRollforwardPageToLoad();

                System.out.println("✅ Successfully switched to rollforward tab");
            } else {
                throw new RuntimeException("Could not find rollforward page in any tab");
            }

        } catch (Exception e) {
            System.err.println("❌ Failed to handle rollforward new tab: " + e.getMessage());
            throw new RuntimeException("Failed to switch to rollforward tab", e);
        }
    }

    /**
     * Check if a page is the rollforward page
     */
    private boolean isRollforwardPage(Page pageToCheck) {
        try {
            // Check for rollforward-specific elements
            List<String> rollforwardIndicators = Arrays.asList(
                "xpath=//h1[contains(text(),'Roll Forward')]",
                "xpath=//h2[contains(text(),'Roll Forward')]",
                "xpath=//div[contains(text(),'Roll Forward')]",
                "xpath=//span[contains(text(),'Roll Forward')]",
                "xpath=//button[contains(text(),'Roll Forward')]",
                "xpath=//*[contains(@class,'rollforward')]",
                "xpath=//*[contains(@id,'rollforward')]",
                "xpath=//table//th[contains(text(),'Select')]",
                "xpath=//div[@data-row-index]",
                "xpath=//input[@type='checkbox']"
            );

            for (String selector : rollforwardIndicators) {
                try {
                    if (pageToCheck.locator(selector).count() > 0) {
                        System.out.println("Found rollforward indicator: " + selector);
                        return true;
                    }
                } catch (Exception e) {
                    // Continue checking other selectors
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Wait for rollforward page to load completely
     */
    private void waitForRollforwardPageToLoad() {
        try {
            System.out.println("Waiting for rollforward page to load...");

            // Wait for common rollforward page elements
            List<String> loadingIndicators = Arrays.asList(
                "xpath=//table",
                "xpath=//div[@data-row-index]",
                "xpath=//input[@type='checkbox']",
                "xpath=//div[contains(@class,'table')]",
                "xpath=//div[contains(@class,'grid')]"
            );

            boolean pageLoaded = false;
            for (String selector : loadingIndicators) {
                try {
                    page.waitForSelector(selector, new Page.WaitForSelectorOptions().setTimeout(10000));
                    System.out.println("✅ Found loading indicator: " + selector);
                    pageLoaded = true;
                    break;
                } catch (Exception e) {
                    // Continue to next selector
                }
            }

            if (!pageLoaded) {
                System.out.println("⚠️ No specific loading indicators found, using generic wait");
            }

            // Additional wait for dynamic content
            page.waitForTimeout(3000);

            System.out.println("✅ Rollforward page loaded");

        } catch (Exception e) {
            System.err.println("❌ Failed to wait for rollforward page: " + e.getMessage());
            throw new RuntimeException("Rollforward page failed to load", e);
        }
    }

    void createNewCopyAuction() {

        retryOperation(() -> {
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForSelector("xpath=//*[contains(text(),'Copy Auction')]");
            page.locator("xpath=//*[contains(text(),'Copy Auction')]").first().click();
        }, 1);
    }


    protected boolean successMessage() {
        try {

            checkForErrorMessages();
            retryOperation(() -> {


                try {
                    List<String> successSelectors = Arrays.asList(
                            "xpath=//*[contains(text(),'Event Created Successfully')]",
                            "xpath=//*[contains(text(),'Event created successfully')]",
                            "xpath=//*[contains(text(),'Successfully created')]",
                            "xpath=//*[contains(text(),'Created successfully')]"
                    );

                    Locator successElement = null;
                    String foundSelector = null;

                    for (String selector : successSelectors) {
                        try {
                            page.waitForSelector(selector, new Page.WaitForSelectorOptions().setTimeout(5000));
                            successElement = page.locator(selector);
                            if (successElement.count() > 0 && successElement.first().isVisible()) {
                                foundSelector = selector;
                                break;
                            }
                        } catch (Exception e) {
                            throw new RuntimeException("Failed to find success message with selector: " + selector, e);
                        }
                    }

                    if (successElement == null || foundSelector == null) {
                        throw new RuntimeException("Success message not found with any selector");
                    }


                    if (!successElement.first().isVisible()) {
                        throw new RuntimeException("Success message element found but not visible");
                    }

                    page.waitForTimeout(2000);
                    String message = successElement.first().innerText();
                    String saleNumber = message.replaceAll("\\D+", "");
                    page.waitForSelector("xpath=//input[@id='searchAuction--0']",
                            new Page.WaitForSelectorOptions().setTimeout(10000));

                } catch (Exception e) {
                    checkForErrorMessages();
                    throw e; // Re-throw to trigger retry
                }
            }, 3);
            return true;

        } catch (Exception e) {
            checkForErrorMessages();
            return false; // Success message not found
        }
    }

    private void checkForErrorMessages() {
        try {

            List<String> errorSelectors = Arrays.asList(
                    "xpath=//*[contains(text(),'Error: Failed to Create Event')]",
                    "xpath=//*[contains(text(),'Failed to Create Event')]",
                    "xpath=//*[contains(text(),'Error')]",
                    "xpath=//*[contains(text(),'failed')]",
                    "xpath=//*[contains(text(),'Failed')]",
                    "xpath=//div[contains(@class,'error')]",
                    "xpath=//div[contains(@class,'alert-danger')]",
                    "xpath=//*[@role='alert']"
            );

            for (String selector : errorSelectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        Locator errorElement = page.locator(selector).first();
                        if (errorElement.isVisible()) {
                            String errorText = errorElement.innerText();

                            if (errorText.toLowerCase().contains("failed to create event")
                                    || errorText.toLowerCase().contains("error: failed to create")
                                    || errorText.toLowerCase().contains("creation failed")
                                    || errorText.toLowerCase().contains("unable to create")
                                    || errorText.toLowerCase().contains("event creation error")) {


                                throw new SuccessMessageNotFoundException("Event creation failed: " + errorText + " - Test should be retried");
                            }
                        }
                    }
                } catch (SuccessMessageNotFoundException e) {
                    throw e;
                } catch (Exception ignored) {
                    throw new RuntimeException("Failed to check for error messages", ignored);
                }
            }

        } catch (SuccessMessageNotFoundException e) {
            throw e; // Re-throw our custom exception
        } catch (Exception e) {
            throw new RuntimeException("Failed to check for error messages", e);
        }
    }


    protected void successMessageRequired() {
        if (!successMessage()) {
            throw new RuntimeException("Required success message was not found - test should be retried");
        }
    }


    protected void debugSuccessElements() {
        try {


            // Check for various success message patterns
            List<String> debugSelectors = Arrays.asList(
                    "xpath=//*[contains(text(),'Event Created Successfully')]",
                    "xpath=//*[contains(text(),'Event created successfully')]",
                    "xpath=//*[contains(text(),'Successfully')]",
                    "xpath=//*[contains(text(),'Created')]",
                    "xpath=//*[contains(text(),'Success')]",
                    "xpath=//*[contains(text(),'Event')]"
            );

            for (String selector : debugSelectors) {
                try {
                    int count = page.locator(selector).count();
                    if (count > 0) {
                        for (int i = 0; i < count; i++) {
                            Locator element = page.locator(selector).nth(i);
                            if (element.isVisible()) {
                                String text = element.innerText();

                            }
                        }
                    }
                } catch (Exception e) {

                }
            }


        } catch (Exception e) {
            throw new RuntimeException("Failed to debug success elements", e);
        }
    }


    protected void validateSuccessMessageWithDebug() {
        try {


            debugSuccessElements();

            if (!successMessage()) {

                throw new SuccessMessageNotFoundException("Success message not found even after debug check");
            }


        } catch (Exception e) {

            throw e;
        }
    }

    protected void errorMessage() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//*[contains(text(),'Error: Failed to Create Event.')]");
            page.locator("xpath=//*[contains(text(),'Error: Failed to Create Event.')]").isVisible();
            page.waitForTimeout(5000);
            String message = page.locator("xpath=//*[contains(text(),'Error: Failed to Create Event.')]").innerText();
            Assertions.assertTrue(message.contains("Error: Failed to Create Event. Query did not return a unique result: 6 results were returned"));
        }, 1);
    }

    void hostSiteSelection(String siteName) {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='sites2--0']");
            page.locator("xpath=//input[@id='sites2--0']").click();
            page.locator("xpath=//input[@id='sites2--0']").fill(siteName);
            page.waitForTimeout(2000);
            try {
                if (page.getByTestId("ListBox::ListBoxItem::0").isVisible()) {
                    page.getByTestId("ListBox::ListBoxItem::0").click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {

                if (page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().isVisible()) {
                    page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                if (page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'" + siteName + "')]").first().isVisible()) {
                    page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'" + siteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                page.evaluate("() => {"
                        + "const options = Array.from(document.querySelectorAll('*'));"
                        + "const option = options.find(el => el.textContent && el.textContent.includes('" + siteName + "'));"
                        + "if (option) { option.click(); }"
                        + "}");
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
        }, 1);
    }

    void hostSiteUpdate(String siteName) {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='hostSite--0']");
            page.locator("xpath=//input[@id='hostSite--0']").clear();
            page.locator("xpath=//input[@id='hostSite--0']").click();
            page.locator("xpath=//input[@id='hostSite--0']").fill(siteName);
            page.waitForTimeout(2000);
            try {
                if (page.getByTestId("ListBox::ListBoxItem::0").isVisible()) {
                    page.getByTestId("ListBox::ListBoxItem::0").click();
                    page.click("body");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {

                if (page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().isVisible()) {
                    page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                if (page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'\"+siteName+\"')]").first().isVisible()) {
                    page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'\"+siteName+\"')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                page.evaluate("() => {"
                        + "const options = Array.from(document.querySelectorAll('*'));"
                        + "const option = options.find(el => el.textContent && el.textContent.includes('\"+siteName+\"'));"
                        + "if (option) { option.click(); }"
                        + "}");
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
        }, 1);
    }

    void advertisedName() {
        retryOperation(() -> {
            int id = secRandom.nextInt();
            page.locator("xpath=//input[@id='primaryClassification--0']").click();
            page.locator("xpath=//*[contains(text(),'Agriculture')]").click();
            page.locator("xpath=//input[@id='advertisedName--0']").fill("Noonan" + id);
        }, 1);
    }

    void additionalClassification() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='additionalClassification--0']");
            page.locator("xpath=//input[@id='additionalClassification--0']").click();
            page.locator("xpath=//input[@id='additionalClassification--0']").fill("Agriculture");
            page.locator("xpath=//*[contains(text(),'Agriculture')]").click();
            page.click("body");
        }, 1);
    }

    void brandSelection() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='brandSelect--0']").click();
            page.locator("xpath=//input[@id='brandSelect--0']").fill("RBA");
            page.locator("xpath=//*[contains(text(),'RBA')]").click();
            page.click("body");
        }, 1);
    }

    void legalEntity() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='legalEntity--0']");
            page.locator("xpath=//input[@id='legalEntity--0']").waitFor();
            page.locator("xpath=//input[@id='legalEntity--0']").click(new Locator.ClickOptions().setForce(true));
            page.locator("xpath=//input[@id='legalEntity--0']").fill("RR.B. Services SARL");
            page.locator("xpath=//*[contains(text(),'RR.B. Services SARL')]").click();
            page.click("body");
        }, 1);
    }

    void businessUnit() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='singleBusinessUnit--0']");
            page.locator("xpath=//input[@id='singleBusinessUnit--0']").click();
            page.locator("xpath=//input[@id='singleBusinessUnit--0']").fill("550 Melbourne");
            page.locator("xpath=//*[contains(text(),'550 Melbourne')]").click();
            page.click("body");
        }, 1);
    }

    void currencySelection() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='singleCurrency--0']").click();
            page.locator("xpath=//*[contains(text(),'USD - U.S.Dollar')]").click();
        }, 1);
    }

    void siteConfigration() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='eventSiteConfiguration--0']");
            page.waitForSelector("xpath=//input[@id='eventSiteConfiguration--0']").click();
            page.locator("xpath=//input[@id='eventSiteConfiguration--0']").fill("Austin Off-site Template");
            page.locator("xpath=//*[contains(text(),'Austin Off-site Template')]").click();
            page.click("body");

        }, 1);
    }

    void auctionlocationTypePermanent() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='auctionNumber--0']");
            page.waitForSelector("xpath=//input[@id='auctionLocationType2--0']");
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Permanent");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Permanent')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Permanent')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Permanent')]").first().click();
        }, 1);
    }

    void auctionlocationTypeOffSite() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Off-Site");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Off-Site')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Off-Site')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Off-Site')]").first().click();
        }, 1);
    }

    void auctionlocationTypeOntheFarm() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("On the Farm");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'On the Farm')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'On the Farm')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'On the Farm')]").first().click();
        }, 1);
    }

    void auctionlocationTypeRegionalAuctionSite() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Regional Auction Site");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Regional Auction Site')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Regional Auction Site')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Regional Auction Site')]").first().click();
        }, 1);
    }

    void manageAuctionAssertions() {
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        Assertions.assertEquals("Auction Details", page.locator("xpath=//h4[normalize-space()='Auction Details']").innerText());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Noonan, ND, USA - Apr 3, 2019", page.locator("xpath=//input[@id='hostSite--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("New", page.locator("xpath=//input[@id='operationalStatus--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("America/Los_Angeles", page.locator("xpath=//input[@id='updatedTimezone--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Permanent", page.locator("xpath=//input[@id='updatedLocationType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Agriculture", page.locator("xpath=//input[@id='updatedPrimaryType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RBA", page.locator("xpath=//input[@id='updatedBrand--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RR.B. Services SARL", page.locator("xpath=//input[@id='legalEntityUpdated--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("USD - U.S.Dollar", page.locator("xpath=//input[@id='updatedCurrency--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("550 Melbourne", page.locator("xpath=//input[@id='updatedBusinessUnit--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Austin Off-site Template", page.locator("xpath=//input[@id='SiteConfigurationUpdated--0']").inputValue());

    }

    void manageCopyAuctionAssertions() {

        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        Assertions.assertEquals("Auction Details", page.locator("xpath=//h4[normalize-space()='Auction Details']").innerText());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Sacramento", page.locator("xpath=//input[@id='hostSite--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("New", page.locator("xpath=//input[@id='operationalStatus--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("America/Los_Angeles", page.locator("xpath=//input[@id='updatedTimezone--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Permanent", page.locator("xpath=//input[@id='updatedLocationType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Industrial", page.locator("xpath=//input[@id='updatedPrimaryType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RBA", page.locator("xpath=//input[@id='updatedBrand--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Ritchie Bros. Auctioneers (America) Inc.", page.locator("xpath=//input[@id='legalEntityUpdated--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("USD - U.S.Dollar", page.locator("xpath=//input[@id='updatedCurrency--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("272 Northern California", page.locator("xpath=//input[@id='updatedBusinessUnit--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Chicago On-site Template", page.locator("xpath=//input[@id='SiteConfigurationUpdated--0']").inputValue());
    }

    protected void ensureSuccessOrRetry() {
        if (!successMessage()) {
            throw new SuccessMessageNotFoundException("Required success message was not found - test should be retried");
        }
    }


    protected void validateSuccessMessage() {
        try {
            if (!successMessage()) {

                String currentUrl = page.url();
                String pageTitle = page.title();

                String errorMessage = String.format(
                        "Success message validation failed. Current URL: %s, Page Title: %s. "
                                + "Expected to find 'Event Created Successfully' message but it was not present.",
                        currentUrl, pageTitle
                );

                throw new SuccessMessageNotFoundException(errorMessage);
            }
        } catch (SuccessMessageNotFoundException e) {
            throw e;
        } catch (Exception e) {

            throw new SuccessMessageNotFoundException("Unexpected error during success message validation: " + e.getMessage(), e);
        }
    }


    protected void executeTestWithRetryOnSuccessFailure(Runnable testLogic, String testName, int maxRetries) {
        int attempt = 1;
        boolean success = false;
        Exception lastException = null;

        while (attempt <= maxRetries && !success) {
            try {
                resetTestState();
                testLogic.run();
                success = true;


            } catch (Exception e) {
                lastException = e;

                boolean isRetryable = e.getMessage() != null && (
                        e.getMessage().toLowerCase().contains("success message")
                                || e.getMessage().toLowerCase().contains("event created successfully")
                                || e.getMessage().toLowerCase().contains("failed to create event")
                                || e.getMessage().toLowerCase().contains("event creation failed")
                                || e instanceof SuccessMessageNotFoundException
                );

                if (isRetryable && attempt < maxRetries) {

                    try {
                        Thread.sleep(5000); // Wait before retry
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Test retry interrupted", ie);
                    }
                } else if (!isRetryable) {
                    throw e; // Non-retryable error, fail immediately
                }

                attempt++;
            }
        }

        if (!success && lastException != null) {
            throw new RuntimeException("Test " + testName + " failed after " + maxRetries + " attempts", lastException);
        }
    }


    protected static void resetStaticDateFields() {
        lastSelectedStartDate = null;
        lastSelectedEndDate = null;

    }


    protected void resetTestState() {
        try {

            resetStaticDateFields();

            if (page != null) {
                page.navigate(baseUrl);
                page.waitForTimeout(3000);
            }

        } catch (Exception e) {
            throw new RuntimeException("Failed to reset test state", e);
        }
    }

    /**
     * Enhanced checkbox checking for rollforward with multiple fallback selectors
     */
    protected void checkboxChecked() {
        retryOperation(() -> {
            System.out.println("=== Starting Rollforward Checkbox Selection ===");

            // Wait for rollforward table/grid to be present
            waitForRollforwardTableToLoad();

            // Try multiple checkbox selector patterns
            List<String> checkboxSelectors = getRollforwardCheckboxSelectors();

            boolean checkboxSelected = false;
            for (String selector : checkboxSelectors) {
                try {
                    System.out.println("Trying rollforward checkbox selector: " + selector);

                    if (page.locator(selector).count() > 0) {
                        Locator checkbox = page.locator(selector).first();

                        if (checkbox.isVisible()) {
                            System.out.println("✅ Found visible rollforward checkbox with selector: " + selector);

                            // Check current state
                            boolean isChecked = isCheckboxChecked(checkbox);
                            System.out.println("Rollforward checkbox current state: " + (isChecked ? "checked" : "unchecked"));

                            if (!isChecked) {
                                // Try different interaction methods
                                if (tryCheckboxInteraction(checkbox)) {
                                    System.out.println("✅ Rollforward checkbox selected successfully");
                                    checkboxSelected = true;
                                    break;
                                }
                            } else {
                                System.out.println("ℹ️ Rollforward checkbox already selected");
                                checkboxSelected = true;
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    System.out.println("❌ Rollforward selector failed: " + selector + " - " + e.getMessage());
                    // Continue to next selector
                }
            }

            if (!checkboxSelected) {
                throw new RuntimeException("Could not find or select rollforward checkbox");
            }

            System.out.println("✅ Rollforward checkbox selection completed");
        }, 3);
    }

    /**
     * Wait for rollforward table/grid to load
     */
    private void waitForRollforwardTableToLoad() {
        try {
            System.out.println("Waiting for rollforward table to load...");

            // Try multiple table/grid selectors
            List<String> tableSelectors = Arrays.asList(
                "xpath=//table",
                "xpath=//div[contains(@class,'table')]",
                "xpath=//div[contains(@class,'grid')]",
                "xpath=//div[@data-row-index]",
                "xpath=//div[contains(@class,'retool')]//table",
                "xpath=//div[contains(@class,'retool')]//div[@data-row-index]"
            );

            boolean tableLoaded = false;
            for (String selector : tableSelectors) {
                try {
                    page.waitForSelector(selector, new Page.WaitForSelectorOptions().setTimeout(10000));
                    System.out.println("✅ Found rollforward table with selector: " + selector);
                    tableLoaded = true;
                    break;
                } catch (Exception e) {
                    // Continue to next selector
                }
            }

            if (!tableLoaded) {
                System.out.println("⚠️ No specific table found, using generic wait");
            }

            // Additional wait for dynamic content
            page.waitForTimeout(2000);

            System.out.println("✅ Rollforward table loaded");

        } catch (Exception e) {
            System.err.println("❌ Failed to wait for rollforward table: " + e.getMessage());
            throw new RuntimeException("Rollforward table failed to load", e);
        }
    }

    /**
     * Get comprehensive list of rollforward checkbox selectors
     */
    private List<String> getRollforwardCheckboxSelectors() {
        return Arrays.asList(
            // Original specific selector
            "xpath=//div[@data-row-index='-1' and @data-column-id='RetoolInternal-715c6648-82c3-442b-977b-9c48da1fb7a8-RowSelectionColumnId']",

            // Generic header row selectors
            "xpath=//div[@data-row-index='-1']//input[@type='checkbox']",
            "xpath=//div[@data-row-index='-1']//input",
            "xpath=//div[@data-row-index='-1']//span[contains(@class,'checkbox')]",
            "xpath=//div[@data-row-index='-1']//div[contains(@class,'checkbox')]",
            "xpath=//div[@data-row-index='-1']",

            // Table header selectors
            "xpath=//thead//input[@type='checkbox']",
            "xpath=//th//input[@type='checkbox']",
            "xpath=//table//tr[1]//input[@type='checkbox']",
            "xpath=//table//tr[1]//td[1]//input",
            "xpath=//table//tr[1]//th[1]//input",

            // Generic checkbox selectors in table context
            "xpath=//table//input[@type='checkbox'][1]",
            "xpath=//div[contains(@class,'table')]//input[@type='checkbox'][1]",
            "xpath=//div[contains(@class,'grid')]//input[@type='checkbox'][1]",

            // Retool-specific selectors
            "xpath=//div[contains(@class,'retool')]//input[@type='checkbox'][1]",
            "xpath=//div[contains(@data-column-id,'RowSelection')]//input",
            "xpath=//div[contains(@data-column-id,'RowSelection')]",

            // First visible checkbox on page
            "xpath=//input[@type='checkbox'][1]",
            "xpath=(//input[@type='checkbox'])[1]"
        );
    }

    /**
     * Try different methods to interact with checkbox
     */
    private boolean tryCheckboxInteraction(Locator checkbox) {
        try {
            // Method 1: Standard click
            try {
                checkbox.click();
                page.waitForTimeout(1000);
                if (isCheckboxChecked(checkbox)) {
                    System.out.println("✅ Checkbox selected with standard click");
                    return true;
                }
            } catch (Exception e) {
                System.out.println("Standard click failed: " + e.getMessage());
            }

            // Method 2: Force click
            try {
                checkbox.click(new Locator.ClickOptions().setForce(true));
                page.waitForTimeout(1000);
                if (isCheckboxChecked(checkbox)) {
                    System.out.println("✅ Checkbox selected with force click");
                    return true;
                }
            } catch (Exception e) {
                System.out.println("Force click failed: " + e.getMessage());
            }

            // Method 3: Check method (for input elements)
            try {
                checkbox.check();
                page.waitForTimeout(1000);
                if (isCheckboxChecked(checkbox)) {
                    System.out.println("✅ Checkbox selected with check method");
                    return true;
                }
            } catch (Exception e) {
                System.out.println("Check method failed: " + e.getMessage());
            }

            // Method 4: JavaScript click
            try {
                checkbox.evaluate("element => element.click()");
                page.waitForTimeout(1000);
                if (isCheckboxChecked(checkbox)) {
                    System.out.println("✅ Checkbox selected with JavaScript click");
                    return true;
                }
            } catch (Exception e) {
                System.out.println("JavaScript click failed: " + e.getMessage());
            }

            return false;

        } catch (Exception e) {
            System.err.println("All checkbox interaction methods failed: " + e.getMessage());
            return false;
        }
    }

    // ===== ROLLFORWARD DEBUG METHODS =====

    /**
     * Debug method to analyze rollforward page structure and checkboxes
     */
    protected void debugRollforwardCheckboxes() {
        try {
            System.out.println("=== DEBUG: Rollforward Checkbox Analysis ===");

            // Check current page context
            System.out.println("Current page URL: " + page.url());
            System.out.println("Current page title: " + page.title());

            // Wait for rollforward table
            waitForRollforwardTableToLoad();

            // Check for table structures
            System.out.println("--- Table Structures ---");
            List<String> tableSelectors = Arrays.asList(
                "xpath=//table",
                "xpath=//div[contains(@class,'table')]",
                "xpath=//div[contains(@class,'grid')]",
                "xpath=//div[@data-row-index]"
            );

            for (String selector : tableSelectors) {
                int count = page.locator(selector).count();
                if (count > 0) {
                    System.out.println("✅ Found " + count + " elements with: " + selector);
                }
            }

            // Check for all checkboxes on page
            System.out.println("--- All Checkboxes ---");
            int totalCheckboxes = page.locator("xpath=//input[@type='checkbox']").count();
            System.out.println("✅ Found " + totalCheckboxes + " total checkboxes on page");

            // Show first few checkboxes with their attributes
            for (int i = 0; i < Math.min(5, totalCheckboxes); i++) {
                try {
                    Locator checkbox = page.locator("xpath=//input[@type='checkbox']").nth(i);
                    String id = checkbox.getAttribute("id");
                    String className = checkbox.getAttribute("class");
                    boolean isVisible = checkbox.isVisible();
                    boolean isEnabled = checkbox.isEnabled();

                    System.out.println("   Checkbox " + i + ":");
                    System.out.println("     ID: " + (id != null ? id : "none"));
                    System.out.println("     Class: " + (className != null ? className : "none"));
                    System.out.println("     Visible: " + isVisible);
                    System.out.println("     Enabled: " + isEnabled);
                } catch (Exception e) {
                    System.out.println("   Checkbox " + i + ": Error getting details - " + e.getMessage());
                }
            }

            System.out.println("=== DEBUG: End of Rollforward Analysis ===");

        } catch (Exception e) {
            System.err.println("Error during rollforward debug: " + e.getMessage());
        }
    }

    /**
     * Enhanced rollforward test with comprehensive debugging
     */
    protected void rollforwardWithDebug() {
        try {
            System.out.println("=== Starting Rollforward with Debug ===");

            // Create rollforward auction
            createNewAuctionRollOver();

            // Debug the rollforward page
            debugRollforwardCheckboxes();

            // Try to check checkbox
            checkboxChecked();

            System.out.println("✅ Rollforward with debug completed");

        } catch (Exception e) {
            System.err.println("❌ Rollforward with debug failed: " + e.getMessage());

            // Take screenshot for debugging
            takeScreenshotOnFailure("rollforward_debug_failure");

            throw e;
        }
    }
}
