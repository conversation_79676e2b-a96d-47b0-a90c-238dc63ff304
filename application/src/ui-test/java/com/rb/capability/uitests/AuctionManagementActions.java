package com.rb.capability.uitests;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.options.MouseButton;
import com.rb.capability.base.BaseTest;
import org.junit.jupiter.api.Assertions;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.Month;
import java.util.Arrays;
import java.util.List;


public class AuctionManagementActions extends BaseTest {
    static SecureRandom secRandom = new SecureRandom();
    static int dateSelectionCounter = 0; // Counter to increment dates each time dateSelection is called

    void loginAuctionManagement() {
        retryOperation(() -> {
            page.navigate(baseUrl);
            page.waitForURL(baseUrl);
            page.waitForSelector("xpath=//p[normalize-space()='Sign in with SSO']");
            page.locator("xpath=//p[normalize-space()='Sign in with SSO']").click();
            page.waitForSelector("xpath=//h3[normalize-space()='Welcome, <PERSON><PERSON><PERSON>']");
            page.waitForSelector("xpath=//h5[normalize-space()='Auction Management Dashboard v2']");
            page.locator("xpath=//h5[normalize-space()='Auction Management Dashboard v2']").click();
            page.waitForTimeout(5000);
        }, 1);
    }

    void retryOperation(Runnable operation, int maxRetries) {
        int attempts = 0;
        boolean success = false;
        Exception lastException = null;

        while (attempts < maxRetries && !success) {
            try {
                operation.run();
                success = true;
            } catch (Exception e) {
                lastException = e;
                attempts++;
                page.waitForTimeout(1000);
            }
        }

        if (!success && lastException != null) {
            throw new RuntimeException("Operation failed after " + maxRetries + " attempts", lastException);
        }
    }

    void submitbuttonClick() {
        retryOperation(() -> {
            Locator submitButton = page.locator("xpath=//p[normalize-space()='Submit']");
            waitForElementVisible(page, submitButton);
            waitForElementEnabled(page, submitButton);
            page.waitForTimeout(2000);
            try {
                submitButton.click(new Locator.ClickOptions().setForce(true));
                page.waitForTimeout(5000);
                page.keyboard().press("Enter");
            } catch (Exception e) {

                page.evaluate("document.querySelector('button.nIczNC.NGAsrY.Fh7Sak').click()");
                page.waitForTimeout(5000);
                page.keyboard().press("Enter");
            }
        }, 1);
    }

    void searchAndSelectAuction(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='searchAuction--0']");
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
            page.waitForTimeout(2000); // Wait for search results to load
            Locator resultClick = page.getByTestId("ListBox::ListBoxItem::0").locator("div").first();
            waitForElementVisible(page, resultClick);
            resultClick.click();
        }, 1);
    }

    void searchAuctionNumber(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath= //input[@id='auction_number--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
        }, 1);
    }

    void searchCopyAuctionNumber(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='searchAuction2--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
            page.waitForTimeout(2000); // Wait for search results to load
            Locator resultClick = page.getByTestId("ListBox::ListBoxItem::0").locator("div").first();
            waitForElementVisible(page, resultClick);
            resultClick.click();
        }, 1);
    }

    void searchAuctionName(String auctionName) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='auction_number--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionName);

        }, 1);
    }

    void searchServerName(String serverName) {
        retryOperation(() -> {
            Locator searchServerField = page.locator("xpath=//input[@id='table1SearchFilter2--0']");
            locateElementAndFillWithValue(page, searchServerField);
            searchServerField.fill(serverName);
            page.waitForTimeout(2000); // Wait for search results to load

        }, 1);
    }

    void createNewAuctionButton() {
        retryOperation(() -> {

            page.evaluate("() => { if (document.activeElement) document.activeElement.blur(); }");
            page.waitForTimeout(500);

            try {
                boolean clicked = (boolean) page.evaluate("() => { "
                        + "  try {"
                        + "    const button = document.querySelector('[data-testid=\"RetoolGrid:formButton2\"] button'); "
                        + "    if (button) { "
                        + "      console.log('Found button by data-testid, clicking...'); "
                        + "      button.scrollIntoView({behavior: 'auto', block: 'center'}); "
                        + "      button.focus(); "
                        + "      button.dispatchEvent(new MouseEvent('mousedown', {bubbles: true, cancelable: true})); "
                        + "      button.dispatchEvent(new MouseEvent('mouseup', {bubbles: true, cancelable: true})); "
                        + "      button.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true})); "
                        + "      button.click(); "
                        + "      return true; "
                        + "    } "
                        + "    return false;"
                        + "  } catch(e) { "
                        + "    console.error('Direct click failed:', e); "
                        + "    return false; "
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            try {
                boolean clicked = (boolean) page.evaluate("() => { "
                        + "  try {"
                        + "    const formButton = document.getElementById('formButton2--0'); "
                        + "    if (formButton) { "
                        + "      console.log('Found button container, clicking child button...'); "
                        + "      const button = formButton.querySelector('button'); "
                        + "      if (button) { "
                        + "        button.scrollIntoView({behavior: 'auto', block: 'center'}); "
                        + "        button.focus(); "
                        + "        button.dispatchEvent(new MouseEvent('mousedown', {bubbles: true, cancelable: true})); "
                        + "        button.dispatchEvent(new MouseEvent('mouseup', {bubbles: true, cancelable: true})); "
                        + "        button.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true})); "
                        + "        button.click(); "
                        + "        return true; "
                        + "      } "
                        + "    } "
                        + "    return false;"
                        + "  } catch(e) { "
                        + "    console.error('Container click failed:', e); "
                        + "    return false; "
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            try {
                boolean clicked = (boolean) page.evaluate("() => {"
                        + "  try {"
                        + "    // Find by text content"
                        + "    let buttons = Array.from(document.querySelectorAll('button, [role=\"button\"]'));"
                        + "    let button = buttons.find(b => b.textContent && b.textContent.includes('Create Auction'));"
                        + "    if (button) {"
                        + "      button.click();"
                        + "      return true;"
                        + "    }"
                        + "    return false;"
                        + "  } catch(e) {"
                        + "    console.error('Simple click failed:', e);"
                        + "    return false;"
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            List<String> selectors = Arrays.asList(
                    "[data-testid='RetoolGrid:formButton2'] button",
                    "button:has-text('Create Auction')",
                    "#formButton2--0 button",
                    "//button[contains(., 'Create Auction')]",
                    "//p[contains(text(),'Create Auction')]/.."
            );

            for (String selector : selectors) {
                try {
                    Locator button = page.locator(selector).first();
                    if (button.count() > 0) {
                        button.click(new Locator.ClickOptions().setForce(true).setButton(MouseButton.LEFT).setTimeout(5000));
                        page.waitForTimeout(1000);
                        page.keyboard().press("Enter");
                        return;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to click button with text: Create Auction");
                }
            }


            try {
                page.evaluate("() => {"
                        + "  document.querySelectorAll('form').forEach(form => {"
                        + "    try { form.submit(); } catch(e) { console.error('Form submit failed:', e); }"
                        + "  });"
                        + "}");
                page.waitForTimeout(1000);
                page.keyboard().press("Enter");
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }
            throw new RuntimeException("Failed to click button with text: Create Auction");
        }, 1);
    }

    void privateTreaty() {
        retryOperation(() -> {
            Locator privateTreatyButton = page.locator("xpath=//input[@id='switchGroup1--0-Private Treaty']");
            waitForElementVisible(page, privateTreatyButton);
            waitForElementEnabled(page, privateTreatyButton);
            privateTreatyButton.click();
        }, 1);
    }


    void dateCopySelection() {
        retryOperation(() -> {
            // Increment the counter each time this method is called
            dateSelectionCounter++;

            page.waitForSelector("xpath=//input[@id='auction_dates_copy--0']");
            page.locator("xpath=//input[@id='auction_dates_copy--0']").elementHandle().isVisible();
            page.locator("xpath=//input[@id='auction_dates_copy--0']").click();
            page.waitForTimeout(1000);
            Locator nextMonthButton = page.locator("xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]");
            Locator nextMonthButton1 = page.locator("xpath=//button[@aria-label='Next month']//*[name()='svg']");

            for (int i = 0; i < 11; i++) {
                if (nextMonthButton.isVisible()) {
                    nextMonthButton.click(new Locator.ClickOptions().setForce(true));
                } else if (nextMonthButton1.isVisible()) {
                    nextMonthButton1.click(new Locator.ClickOptions().setForce(true));
                }
            }

            // Calculate dates based on counter - each call increments the day
            LocalDate baseDate = LocalDate.of(2026, Month.JUNE, 10);
            LocalDate specificDate = baseDate.plusDays(dateSelectionCounter - 1); // -1 because first call should be day 5
            String formattedDate = specificDate.toString();

            System.out.println("Date copy selection call #" + dateSelectionCounter + " - Selecting date: " + formattedDate);

            page.locator("xpath=//button[@aria-label='" + formattedDate + "']").click();

            // Second date is 10 days after the first selected date
            LocalDate tenDaysAfter = specificDate.plusDays(10);
            String formattedEndDate = tenDaysAfter.toString();

            System.out.println("End date: " + formattedEndDate);

            page.locator("xpath=//button[@aria-label='" + formattedEndDate + "']").click();
            page.click("body");
            page.waitForTimeout(1000);
        }, 1);
    }

    /**
     * Select dates for auction with random date logic
     */
    protected void dateSelection() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='auctionDates2--0']");
            page.locator("xpath=//input[@id='auctionDates2--0']").elementHandle().isVisible();
            page.locator("xpath=//input[@id='auctionDates2--0']").click();
            page.waitForTimeout(1000);
            Locator nextMonthButton = page.locator("xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]");
            Locator nextMonthButton1 = page.locator("xpath=//button[@aria-label='Next month']//*[name()='svg']");

            for (int i = 0; i < 11; i++) {
                if (nextMonthButton.isVisible()) {
                    nextMonthButton.click(new Locator.ClickOptions().setForce(true));
                }
                if (nextMonthButton1.isVisible()) {
                    nextMonthButton1.click(new Locator.ClickOptions().setForce(true));
                }
            }

            // Generate random dates for auction
            LocalDate baseDate = LocalDate.of(2026, Month.JUNE, 1);

            // Generate random number of days to add (1-28 to stay within June)
            int randomDays = secRandom.nextInt(28) + 1; // Random between 1-28
            LocalDate specificDate = baseDate.plusDays(randomDays);
            String formattedDate = specificDate.toString();

            System.out.println("Date selection - Randomly selected date: " + formattedDate);

            page.locator("xpath=//button[@aria-label='" + formattedDate + "']").click();

            // Second date is 10 days after the first selected date
            LocalDate tenDaysAfter = specificDate.plusDays(10);
            String formattedEndDate = tenDaysAfter.toString();

            System.out.println("End date: " + formattedEndDate);

            page.locator("xpath=//button[@aria-label='" + formattedEndDate + "']").click();
            page.click("body");
        }, 1);
    }

    void addOffsiteLocation() {
        retryOperation(() -> {
            page.locator("xpath=//p[normalize-space()='Add / Edit Offsite Location']").click();
            page.locator("xpath=//p[normalize-space()='Submit']").click();
            page.keyboard().press("Enter");
            page.waitForTimeout(4000);
            page.locator("xpath=//input[@id='locationName--0']").fill("Test RB Location");
            retryOperation(() -> {
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").click();
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").fill("America/L");
                page.keyboard().press("Backspace");
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").fill("America/L");
                page.getByTestId("ListBox::ListBoxItem::1").click();
            }, 1);
            page.locator("xpath=//input[@id='addressLookup2--0']").fill("700 Ritchie Rd, Davenport, FL, USA");
            page.locator("xpath=//*[contains(text(),'700 Ritchie Rd, Davenport, FL, USA')]").first().click();
            page.waitForTimeout(2000);
            page.locator("xpath=//input[@id='phoneNumber--0']").fill("9885346424");
            page.locator("xpath=//input[@id='email--0']").fill("rbcom");
            Assertions.assertEquals("Please enter a valid email address.", page.locator("xpath=//div[contains(text(),'Please enter a valid email address.')]").innerText());
            page.waitForTimeout(4000);
            page.keyboard().press("Backspace");
            page.locator("xpath=//input[@id='email--0']").fill("<EMAIL>");
            page.locator("xpath=//p[normalize-space()='Submit']").click();
            page.keyboard().press("Enter");
            Locator addEditLink = page.locator("xpath=//p[normalize-space()='Add / Edit Offsite Location']");
            waitForElementVisible(page, addEditLink);
            page.waitForTimeout(2000);
        }, 1);
    }

    void createNewAuction() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForSelector("xpath=//*[contains(text(),'Create New')]");
            page.locator("xpath=//*[contains(text(),'Create New')]").click();
        }, 1);
    }

    void createNewCopyAuction() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForSelector("xpath=//*[contains(text(),'Copy Auction')]");
            page.locator("xpath=//*[contains(text(),'Copy Auction')]").first().click();
        }, 1);
    }

    void successMessage() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//*[contains(text(),'Event Created Successfully')]");
            page.locator("xpath=//*[contains(text(),'Event Created Successfully')]").isVisible();
            page.waitForTimeout(5000);
            page.waitForSelector("xpath=//input[@id='searchAuction--0']");
        }, 1);
    }

    void hostSiteSelection(String SiteName) {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='sites2--0']");
            page.locator("xpath=//input[@id='sites2--0']").click();
            page.locator("xpath=//input[@id='sites2--0']").fill(SiteName);
            page.waitForTimeout(2000);
            try {
                if (page.getByTestId("ListBox::ListBoxItem::0").isVisible()) {
                    page.getByTestId("ListBox::ListBoxItem::0").click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {

                if (page.locator("xpath=//*[contains(text(),'" + SiteName + "')]").first().isVisible()) {
                    page.locator("xpath=//*[contains(text(),'" + SiteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                if (page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'" + SiteName + "')]").first().isVisible()) {
                    page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'" + SiteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                page.evaluate("() => {"
                        + "const options = Array.from(document.querySelectorAll('*'));"
                        + "const option = options.find(el => el.textContent && el.textContent.includes('" + SiteName + "'));"
                        + "if (option) { option.click(); }"
                        + "}");
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
        }, 1);
    }

    void hostSiteUpdate(String SiteName) {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='hostSite--0']");
            page.locator("xpath=//input[@id='hostSite--0']").clear();
            page.locator("xpath=//input[@id='hostSite--0']").click();
            page.locator("xpath=//input[@id='hostSite--0']").fill(SiteName);
            page.waitForTimeout(2000);
            try {
                if (page.getByTestId("ListBox::ListBoxItem::0").isVisible()) {
                    page.getByTestId("ListBox::ListBoxItem::0").click();
                    page.click("body");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {

                if (page.locator("xpath=//*[contains(text(),'" + SiteName + "')]").first().isVisible()) {
                    page.locator("xpath=//*[contains(text(),'" + SiteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                if (page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'\"+SiteName+\"')]").first().isVisible()) {
                    page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'\"+SiteName+\"')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                page.evaluate("() => {"
                        + "const options = Array.from(document.querySelectorAll('*'));"
                        + "const option = options.find(el => el.textContent && el.textContent.includes('\"+SiteName+\"'));"
                        + "if (option) { option.click(); }"
                        + "}");
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
        }, 1);
    }

    void advertisedName() {
        retryOperation(() -> {
            int id = secRandom.nextInt();
            page.locator("xpath=//input[@id='primaryClassification--0']").click();
            page.locator("xpath=//*[contains(text(),'Agriculture')]").click();
            page.locator("xpath=//input[@id='advertisedName--0']").fill("Noonan" + id);
        }, 1);
    }

    void additionalClassification() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='additionalClassification--0']");
            page.locator("xpath=//input[@id='additionalClassification--0']").click();
            page.locator("xpath=//input[@id='additionalClassification--0']").fill("Agriculture");
            page.locator("xpath=//*[contains(text(),'Agriculture')]").click();
            page.click("body");
        }, 1);
    }

    void brandSelection() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='brandSelect--0']").click();
            page.locator("xpath=//input[@id='brandSelect--0']").fill("RBA");
            page.locator("xpath=//*[contains(text(),'RBA')]").click();
            page.click("body");
        }, 1);
    }

    void legalEntity() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='legalEntity--0']");
            page.locator("xpath=//input[@id='legalEntity--0']").waitFor();
            page.locator("xpath=//input[@id='legalEntity--0']").click(new Locator.ClickOptions().setForce(true));
            page.locator("xpath=//input[@id='legalEntity--0']").fill("RR.B. Services SARL");
            page.locator("xpath=//*[contains(text(),'RR.B. Services SARL')]").click();
            page.click("body");
        }, 1);
    }

    void businessUnit() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='singleBusinessUnit--0']");
            page.locator("xpath=//input[@id='singleBusinessUnit--0']").click();
            page.locator("xpath=//input[@id='singleBusinessUnit--0']").fill("550 Melbourne");
            page.locator("xpath=//*[contains(text(),'550 Melbourne')]").click();
            page.click("body");
        }, 1);
    }

    void currencySelection() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='singleCurrency--0']").click();
            page.locator("xpath=//*[contains(text(),'USD - U.S.Dollar')]").click();
        }, 1);
    }

    void siteConfigration() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='eventSiteConfiguration--0']");
            page.waitForSelector("xpath=//input[@id='eventSiteConfiguration--0']").click();
            page.locator("xpath=//input[@id='eventSiteConfiguration--0']").fill("Austin Off-site Template");
            page.locator("xpath=//*[contains(text(),'Austin Off-site Template')]").click();
            page.click("body");

        }, 1);
    }

    void auctionlocationTypePermanent() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='auctionNumber--0']");
            page.waitForSelector("xpath=//input[@id='auctionLocationType2--0']");
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Permanent");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Permanent')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Permanent')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Permanent')]").first().click();
        }, 1);
    }

    void auctionlocationTypeOffSite() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Off-Site");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Off-Site')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Off-Site')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Off-Site')]").first().click();
        }, 1);
    }

    void auctionlocationTypeOntheFarm() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("On the Farm");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'On the Farm')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'On the Farm')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'On the Farm')]").first().click();
        }, 1);
    }

    void auctionlocationTypeRegionalAuctionSite() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Regional Auction Site");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Regional Auction Site')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Regional Auction Site')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Regional Auction Site')]").first().click();
        }, 1);
    }

    void manageAuctionAssertions() {
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        Assertions.assertEquals("Auction Details", page.locator("xpath=//h4[normalize-space()='Auction Details']").innerText());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Noonan, ND, USA - Apr 3, 2019", page.locator("xpath=//input[@id='hostSite--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("New", page.locator("xpath=//input[@id='operationalStatus--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("America/Los_Angeles", page.locator("xpath=//input[@id='updatedTimezone--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Permanent", page.locator("xpath=//input[@id='updatedLocationType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Agriculture", page.locator("xpath=//input[@id='updatedPrimaryType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RBA", page.locator("xpath=//input[@id='updatedBrand--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RR.B. Services SARL", page.locator("xpath=//input[@id='legalEntityUpdated--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("USD - U.S.Dollar", page.locator("xpath=//input[@id='updatedCurrency--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("550 Melbourne", page.locator("xpath=//input[@id='updatedBusinessUnit--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Austin Off-site Template", page.locator("xpath=//input[@id='SiteConfigurationUpdated--0']").inputValue());

    }

    void manageCopyAuctionAssertions() {
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        Assertions.assertEquals("Auction Details", page.locator("xpath=//h4[normalize-space()='Auction Details']").innerText());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Sacramento", page.locator("xpath=//input[@id='hostSite--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("New", page.locator("xpath=//input[@id='operationalStatus--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("America/Los_Angeles", page.locator("xpath=//input[@id='updatedTimezone--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Permanent", page.locator("xpath=//input[@id='updatedLocationType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Industrial", page.locator("xpath=//input[@id='updatedPrimaryType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RBA", page.locator("xpath=//input[@id='updatedBrand--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Ritchie Bros. Auctioneers (America) Inc.", page.locator("xpath=//input[@id='legalEntityUpdated--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("USD - U.S.Dollar", page.locator("xpath=//input[@id='updatedCurrency--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("272 Northern California", page.locator("xpath=//input[@id='updatedBusinessUnit--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Chicago On-site Template", page.locator("xpath=//input[@id='SiteConfigurationUpdated--0']").inputValue());

    }
}
