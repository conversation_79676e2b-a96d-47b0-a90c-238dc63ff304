package com.rb.capability.uitests;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.MouseButton;
import com.rb.capability.base.BaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.Month;
import java.util.Arrays;
import java.util.List;


public class AuctionManagementActions extends BaseTest {

    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @ExtendWith(SuccessMessageRetryExtension.class)
    public @interface RetryOnSuccessMessageFailure {
        int maxRetries() default 3;

        long delayMs() default 5000;


    }


    public static class SuccessMessageRetryExtension implements TestExecutionExceptionHandler {


        @Override
        public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {

            if (isSuccessMessageFailure(throwable)) {

                RetryOnSuccessMessageFailure annotation = context.getRequiredTestMethod()
                        .getAnnotation(RetryOnSuccessMessageFailure.class);

                if (annotation != null) {
                    int maxRetries = annotation.maxRetries();
                    long delayMs = annotation.delayMs();

                    ExtensionContext.Store store = context.getStore(ExtensionContext.Namespace.create(context.getRequiredTestMethod()));
                    Integer currentRetryCount = store.get("retryCount", Integer.class);
                    if (currentRetryCount == null) {
                        currentRetryCount = 0;
                    }

                    if (currentRetryCount < maxRetries) {
                        store.put("retryCount", currentRetryCount + 1);
                        resetTestState(context);

                        try {
                            Thread.sleep(delayMs);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("Test retry interrupted", e);
                        }
                        return;
                    }
                }
            }

            throw throwable;
        }


        private boolean isSuccessMessageFailure(Throwable throwable) {

            if (throwable == null) {
                return false;
            }
            String message = throwable.getMessage();
            if (message == null) {
                return false;
            }


            return message.toLowerCase().contains("success message")
                    || message.toLowerCase().contains("event created successfully")
                    || message.toLowerCase().contains("success message not found")
                    || message.toLowerCase().contains("required success message")
                    || message.toLowerCase().contains("test incomplete")
                    || message.toLowerCase().contains("failed to create event")
                    || message.toLowerCase().contains("event creation failed");
        }


        private void resetTestState(ExtensionContext context) {
            try {
                Object testInstance = context.getRequiredTestInstance();
                if (testInstance instanceof AuctionManagementActions) {
                    AuctionManagementActions actions = (AuctionManagementActions) testInstance;
                    actions.resetTestState();
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to reset test state before retry", e);
            }
        }
    }


    public static class SuccessMessageNotFoundException extends RuntimeException {
        public SuccessMessageNotFoundException(String message) {
            super(message);
        }

        public SuccessMessageNotFoundException(String message, Throwable cause) {
            super(message, cause);
        }
    }


    protected static final SecureRandom secRandom = new SecureRandom();
    static LocalDate lastSelectedStartDate;
    static LocalDate lastSelectedEndDate;

    protected static String getFormattedDateForAssertion(LocalDate date) {
        if (date == null) {
            return "Jun 05, 2026";
        }
        return date.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy"));
    }

    protected static String getLastSelectedStartDateFormatted() {
        return getFormattedDateForAssertion(lastSelectedStartDate);
    }

    protected String findFirstAuctionInResults() {
        try {

            page.waitForTimeout(2000);
            List<String> selectors = Arrays.asList(
                    "xpath=//span[contains(@class, 'auction-number') or contains(text(), '202')]",
                    "xpath=//div[@role='row']//span[matches(text(), '^\\d{7}$')]",
                    "xpath=//span[matches(text(), '^202\\d{4}$')]",
                    "xpath=//div[contains(@class, 'result')]//span[matches(text(), '^\\d{7}$')]"
            );

            for (String selector : selectors) {
                try {
                    Locator auctionElements = page.locator(selector);
                    if (auctionElements.count() > 0) {
                        String auctionNumber = auctionElements.first().innerText().trim();
                        if (auctionNumber.matches("\\d{7}")) { // 7-digit auction number
                            return auctionNumber;
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to find auction in results");
                }
            }

            try {
                Locator allSpans = page.locator("xpath=//span");
                int count = allSpans.count();
                for (int i = 0; i < count; i++) {
                    String text = allSpans.nth(i).innerText().trim();
                    if (text.matches("\\d{7}")) {
                        return text;
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to find auction in results");
            }

            return null;
        } catch (Exception e) {
            throw new RuntimeException("Failed to find auction in results");
        }
    }

    protected void auctionNotes(String notes) {
        retryOperation(() -> {
            Locator notesField = page.locator("xpath=//textarea[@id='auctionNotesCreate--0']");
            waitForElementEnabled(page, notesField);
            waitForElementVisible(page, notesField);
            notesField.clear();
            notesField.fill(notes);

        }, 1);
    }

    protected void clickOnAuctionInResults(String auctionNumber) {
        retryOperation(() -> {

            Locator auctionSpan = page.locator("xpath=//span[normalize-space()='" + auctionNumber + "']");
            waitForElementVisible(page, auctionSpan);
            auctionSpan.hover();
            page.waitForTimeout(1000);
            List<String> editButtonSelectors = Arrays.asList(
                    "xpath=//*[name()='path' and contains(@d,'M11,.25H3a')]",
                    "xpath=//button[@aria-label='Edit']",
                    "xpath=//button[contains(@class, 'edit')]",
                    "xpath=//*[name()='svg' and contains(@class, 'edit')]",
                    "xpath=//div[contains(@class, 'edit-icon')]"
            );

            boolean clicked = false;
            for (String selector : editButtonSelectors) {
                try {
                    Locator editButton = page.locator(selector);
                    if (editButton.isVisible()) {
                        editButton.click();
                        clicked = true;
                        break;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to click on auction in results");
                }
            }

            if (!clicked) {

                auctionSpan.click();
            }

            page.waitForTimeout(2000);
        }, 2);
    }


    protected void closeModalIfPresent() {
        try {

            List<String> closeSelectors = Arrays.asList(
                    "xpath=//button[@aria-label='Close']",
                    "xpath=//button[contains(@class, 'close')]",
                    "xpath=//*[name()='svg' and contains(@class, 'close')]",
                    "xpath=//div[@class='_icon_c3auw_29']//*[name()='svg']",
                    "xpath=//button[@aria-label='Close']//div[@class='_icon_c3auw_29']//*[name()='svg']"
            );

            for (String selector : closeSelectors) {
                try {
                    Locator closeButton = page.locator(selector);
                    if (closeButton.isVisible()) {
                        closeButton.click();
                        page.waitForTimeout(1000);
                        return;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to close modal");
                }
            }
            page.keyboard().press("Escape");
            page.waitForTimeout(500);

        } catch (Exception e) {
            throw new RuntimeException("Failed to close modal");
        }
    }

    protected void loginAuctionManagement() {
        retryOperation(() -> {
            page.navigate(baseUrl);
            page.waitForURL(baseUrl);
            waitForElementVisible(page, page.locator("xpath=//p[normalize-space()='Sign in with SSO']"));
            if (page.locator("xpath=//p[normalize-space()='Sign in with SSO']").isVisible()) {
                page.locator("xpath=//p[normalize-space()='Sign in with SSO']").click();
            }

            page.waitForSelector("xpath=//h3[normalize-space()='Welcome, Sasank Kumar']");
            page.waitForSelector("xpath=//h5[normalize-space()='Auction Management Dashboard v2']");
            page.locator("xpath=//h5[normalize-space()='Auction Management Dashboard v2']").click();
            page.waitForTimeout(5000);
        }, 1);
    }

    void retryOperation(Runnable operation, int maxRetries) {
        int attempts = 0;
        boolean success = false;
        Exception lastException = null;

        while (attempts < maxRetries && !success) {
            try {
                operation.run();
                success = true;
            } catch (Exception e) {
                lastException = e;
                attempts++;
                page.waitForTimeout(1000);
            }
        }

        if (!success && lastException != null) {
            throw new RuntimeException("Operation failed after " + maxRetries + " attempts", lastException);
        }
    }

    void submitbuttonClick() {
        retryOperation(() -> {
            Locator submitButton = page.locator("xpath=//p[normalize-space()='Submit']");
            waitForElementVisible(page, submitButton);
            waitForElementEnabled(page, submitButton);
            page.waitForTimeout(2000);
            try {
                submitButton.click(new Locator.ClickOptions().setForce(true));
                page.waitForTimeout(5000);
                page.keyboard().press("Enter");
            } catch (Exception e) {

                page.evaluate("document.querySelector('button.nIczNC.NGAsrY.Fh7Sak').click()");
                page.waitForTimeout(5000);
                page.keyboard().press("Enter");
            }
        }, 1);
    }

    void searchAndSelectAuction(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='searchAuction--0']");
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
            page.waitForTimeout(2000); // Wait for search results to load
            Locator resultClick = page.getByTestId("ListBox::ListBoxItem::0").locator("div").first();
            waitForElementVisible(page, resultClick);
            resultClick.click();
        }, 1);
    }

    void searchAuctionNumber(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath= //input[@id='auction_number--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
        }, 1);
    }

    void searchCopyAuctionNumber(String auctionNumber) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='searchAuction2--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionNumber);
            page.waitForTimeout(2000); // Wait for search results to load
            Locator resultClick = page.getByTestId("ListBox::ListBoxItem::0").locator("div").first();
            waitForElementVisible(page, resultClick);
            resultClick.click();
        }, 1);
    }

    void searchAuctionName(String auctionName) {
        retryOperation(() -> {
            Locator searchField = page.locator("xpath=//input[@id='auction_number--0']");
            waitForElementVisible(page, searchField);
            locateElementAndFillWithValue(page, searchField);
            searchField.fill(auctionName);

        }, 1);
    }

    void searchServerName(String serverName) {
        retryOperation(() -> {
            Locator searchServerField = page.locator("xpath=//input[@id='table1SearchFilter2--0']");
            locateElementAndFillWithValue(page, searchServerField);
            searchServerField.fill(serverName);
            page.waitForTimeout(2000);

        }, 1);
    }

    void createNewAuctionButton() {
        retryOperation(() -> {

            page.evaluate("() => { if (document.activeElement) document.activeElement.blur(); }");
            page.waitForTimeout(500);

            try {
                boolean clicked = (boolean) page.evaluate("() => { "
                        + "  try {"
                        + "    const button = document.querySelector('[data-testid=\"RetoolGrid:formButton2\"] button'); "
                        + "    if (button) { "
                        + "      console.log('Found button by data-testid, clicking...'); "
                        + "      button.scrollIntoView({behavior: 'auto', block: 'center'}); "
                        + "      button.focus(); "
                        + "      button.dispatchEvent(new MouseEvent('mousedown', {bubbles: true, cancelable: true})); "
                        + "      button.dispatchEvent(new MouseEvent('mouseup', {bubbles: true, cancelable: true})); "
                        + "      button.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true})); "
                        + "      button.click(); "
                        + "      return true; "
                        + "    } "
                        + "    return false;"
                        + "  } catch(e) { "
                        + "    console.error('Direct click failed:', e); "
                        + "    return false; "
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            try {
                boolean clicked = (boolean) page.evaluate("() => { "
                        + "  try {"
                        + "    const formButton = document.getElementById('formButton2--0'); "
                        + "    if (formButton) { "
                        + "      console.log('Found button container, clicking child button...'); "
                        + "      const button = formButton.querySelector('button'); "
                        + "      if (button) { "
                        + "        button.scrollIntoView({behavior: 'auto', block: 'center'}); "
                        + "        button.focus(); "
                        + "        button.dispatchEvent(new MouseEvent('mousedown', {bubbles: true, cancelable: true})); "
                        + "        button.dispatchEvent(new MouseEvent('mouseup', {bubbles: true, cancelable: true})); "
                        + "        button.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true})); "
                        + "        button.click(); "
                        + "        return true; "
                        + "      } "
                        + "    } "
                        + "    return false;"
                        + "  } catch(e) { "
                        + "    console.error('Container click failed:', e); "
                        + "    return false; "
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            try {
                boolean clicked = (boolean) page.evaluate("() => {"
                        + "  try {"
                        + "    // Find by text content"
                        + "    let buttons = Array.from(document.querySelectorAll('button, [role=\"button\"]'));"
                        + "    let button = buttons.find(b => b.textContent && b.textContent.includes('Create Auction'));"
                        + "    if (button) {"
                        + "      button.click();"
                        + "      return true;"
                        + "    }"
                        + "    return false;"
                        + "  } catch(e) {"
                        + "    console.error('Simple click failed:', e);"
                        + "    return false;"
                        + "  }"
                        + "}");

                if (clicked) {
                    page.waitForTimeout(2000);
                    page.keyboard().press("Enter");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }


            List<String> selectors = Arrays.asList(
                    "[data-testid='RetoolGrid:formButton2'] button",
                    "button:has-text('Create Auction')",
                    "#formButton2--0 button",
                    "//button[contains(., 'Create Auction')]",
                    "//p[contains(text(),'Create Auction')]/.."
            );

            for (String selector : selectors) {
                try {
                    Locator button = page.locator(selector).first();
                    if (button.count() > 0) {
                        button.click(new Locator.ClickOptions().setForce(true).setButton(MouseButton.LEFT).setTimeout(5000));
                        page.waitForTimeout(1000);
                        page.keyboard().press("Enter");
                        return;
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to click button with text: Create Auction");
                }
            }


            try {
                page.evaluate("() => {"
                        + "  document.querySelectorAll('form').forEach(form => {"
                        + "    try { form.submit(); } catch(e) { console.error('Form submit failed:', e); }"
                        + "  });"
                        + "}");
                page.waitForTimeout(1000);
                page.keyboard().press("Enter");
            } catch (Exception e) {
                throw new RuntimeException("Failed to click button with text: Create Auction");
            }
            throw new RuntimeException("Failed to click button with text: Create Auction");
        }, 1);
    }

    void privateTreaty() {
        retryOperation(() -> {
            Locator privateTreatyButton = page.locator("xpath=//input[@id='switchGroup1--0-Private Treaty']");
            waitForElementVisible(page, privateTreatyButton);
            waitForElementEnabled(page, privateTreatyButton);
            privateTreatyButton.click();
        }, 1);
    }

    void skipDuplicateCheck() {

        Locator skipDuplicateCheckButton = page.locator("xpath=//span[normalize-space()='Skip Duplicate Check']");
        waitForElementVisible(page, skipDuplicateCheckButton);
        waitForElementEnabled(page, skipDuplicateCheckButton);
        skipDuplicateCheckButton.click();
    }

    protected void dateCopySelection() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='auction_dates_copy--0']");
            page.locator("xpath=//input[@id='auction_dates_copy--0']").elementHandle().isVisible();
            page.locator("xpath=//input[@id='auction_dates_copy--0']").click();
            page.waitForTimeout(1000);
            Locator nextMonthButton = page.locator("xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]");
            Locator nextMonthButton1 = page.locator("xpath=//button[@aria-label='Next month']//*[name()='svg']");

            for (int i = 0; i < 11; i++) {
                if (nextMonthButton.isVisible()) {
                    nextMonthButton.click(new Locator.ClickOptions().setForce(true));
                } else if (nextMonthButton1.isVisible()) {
                    nextMonthButton1.click(new Locator.ClickOptions().setForce(true));
                }
            }

            LocalDate currentDate = LocalDate.now();
            LocalDate baseDate = currentDate.plusYears(1);
            int randomDays = secRandom.nextInt(30) + 1;
            LocalDate specificDate = baseDate.plusDays(randomDays);
            String formattedDate = specificDate.toString();
            lastSelectedStartDate = specificDate;
            page.locator("xpath=//button[@aria-label='" + formattedDate + "']").click();
            LocalDate tenDaysAfter = specificDate.plusDays(10);
            String formattedEndDate = tenDaysAfter.toString();
            lastSelectedEndDate = tenDaysAfter;


            if (tenDaysAfter.getMonth() != specificDate.getMonth()) {
                Locator nextMonthButtonForEnd = page.locator("xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]");
                Locator nextMonthButton1ForEnd = page.locator("xpath=//button[@aria-label='Next month']//*[name()='svg']");

                if (nextMonthButtonForEnd.isVisible()) {
                    nextMonthButtonForEnd.click(new Locator.ClickOptions().setForce(true));
                } else if (nextMonthButton1ForEnd.isVisible()) {
                    nextMonthButton1ForEnd.click(new Locator.ClickOptions().setForce(true));
                }
                page.waitForTimeout(1000); // Wait for month transition
            }

            page.locator("xpath=//button[@aria-label='" + formattedEndDate + "']").click();
            page.click("body");
            page.waitForTimeout(1000);
        }, 1);
    }


    /**
     * Enhanced date selection with robust navigation and error handling
     */
    protected void dateSelection() {
        retryOperation(() -> {
            System.out.println("=== Starting Enhanced Date Selection ===");

            // Open date picker
            openDatePicker();

            // Generate target dates
            LocalDate currentDate = LocalDate.now();
            LocalDate targetStartDate = generateRandomFutureDate(currentDate);
            LocalDate targetEndDate = targetStartDate.plusDays(10);

            System.out.println("Target start date: " + targetStartDate);
            System.out.println("Target end date: " + targetEndDate);

            // Navigate to target month and select start date
            navigateToDateAndSelect(targetStartDate, "start");
            lastSelectedStartDate = targetStartDate;

            // Navigate to end date month if different and select end date
            navigateToDateAndSelect(targetEndDate, "end");
            lastSelectedEndDate = targetEndDate;

            // Close date picker
            closeDatePicker();

            System.out.println("✅ Date selection completed successfully");
        }, 3); // Increased retry count for date selection
    }

    /**
     * Open the date picker with enhanced error handling
     */
    private void openDatePicker() {
        try {
            System.out.println("Opening date picker...");

            // Wait for date input field
            page.waitForSelector("xpath=//input[@id='auctionDates2--0']",
                new Page.WaitForSelectorOptions().setTimeout(10000));

            // Verify field is visible and enabled
            Locator dateInput = page.locator("xpath=//input[@id='auctionDates2--0']");
            if (!dateInput.isVisible()) {
                throw new RuntimeException("Date input field is not visible");
            }

            // Click to open date picker
            dateInput.click();
            page.waitForTimeout(2000); // Wait for date picker to open

            // Verify date picker is open
            if (!isDatePickerOpen()) {
                throw new RuntimeException("Date picker failed to open");
            }

            System.out.println("✅ Date picker opened successfully");

        } catch (Exception e) {
            System.err.println("❌ Failed to open date picker: " + e.getMessage());
            throw new RuntimeException("Failed to open date picker", e);
        }
    }

    /**
     * Check if date picker is currently open
     */
    private boolean isDatePickerOpen() {
        try {
            // Check for common date picker elements
            List<String> datePickerSelectors = Arrays.asList(
                "xpath=//div[contains(@class,'react-datepicker')]",
                "xpath=//div[contains(@class,'datepicker')]",
                "xpath=//div[@role='dialog']//button[@aria-label]",
                "xpath=//button[@aria-label='Next month']",
                "xpath=//button[@aria-label='Previous month']"
            );

            for (String selector : datePickerSelectors) {
                if (page.locator(selector).count() > 0 && page.locator(selector).first().isVisible()) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Generate a random future date for testing
     */
    private LocalDate generateRandomFutureDate(LocalDate currentDate) {
        // Generate date 6-18 months in the future to avoid current date issues
        int monthsToAdd = 6 + secRandom.nextInt(12); // 6-18 months
        LocalDate baseDate = currentDate.plusMonths(monthsToAdd);

        // Add random days (1-28 to avoid month-end issues)
        int randomDays = 1 + secRandom.nextInt(28);
        return baseDate.withDayOfMonth(1).plusDays(randomDays - 1);
    }

    /**
     * Navigate to specific date and select it
     */
    private void navigateToDateAndSelect(LocalDate targetDate, String dateType) {
        try {
            System.out.println("Navigating to " + dateType + " date: " + targetDate);

            // Get current month/year from date picker
            LocalDate currentPickerDate = getCurrentPickerDate();
            System.out.println("Current picker date: " + currentPickerDate);

            // Navigate to target month/year
            navigateToTargetMonth(currentPickerDate, targetDate);

            // Select the specific date
            selectDateInPicker(targetDate);

            System.out.println("✅ Successfully selected " + dateType + " date: " + targetDate);

        } catch (Exception e) {
            System.err.println("❌ Failed to navigate to " + dateType + " date: " + e.getMessage());
            throw new RuntimeException("Failed to select " + dateType + " date: " + targetDate, e);
        }
    }

    /**
     * Get current month/year displayed in date picker
     */
    private LocalDate getCurrentPickerDate() {
        try {
            // Try different selectors for month/year display
            List<String> monthYearSelectors = Arrays.asList(
                "xpath=//div[contains(@class,'react-datepicker__current-month')]",
                "xpath=//div[contains(@class,'datepicker-header')]",
                "xpath=//span[contains(@class,'month')]",
                "xpath=//button[contains(@class,'month')]"
            );

            for (String selector : monthYearSelectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        String monthYearText = page.locator(selector).first().innerText();
                        return parseMonthYearText(monthYearText);
                    }
                } catch (Exception e) {
                    // Continue to next selector
                }
            }

            // Fallback: assume current date
            System.out.println("⚠️ Could not determine picker date, using current date");
            return LocalDate.now();

        } catch (Exception e) {
            System.err.println("Error getting current picker date: " + e.getMessage());
            return LocalDate.now();
        }
    }

    /**
     * Parse month/year text from date picker header
     */
    private LocalDate parseMonthYearText(String monthYearText) {
        try {
            // Common formats: "June 2026", "Jun 2026", "2026-06", etc.
            String[] parts = monthYearText.trim().split("\\s+");

            if (parts.length >= 2) {
                String monthPart = parts[0];
                String yearPart = parts[1];

                int year = Integer.parseInt(yearPart);
                int month = parseMonthName(monthPart);

                return LocalDate.of(year, month, 1);
            }

            // Fallback
            return LocalDate.now();

        } catch (Exception e) {
            System.err.println("Error parsing month/year text: " + monthYearText);
            return LocalDate.now();
        }
    }

    /**
     * Parse month name to number
     */
    private int parseMonthName(String monthName) {
        String month = monthName.toLowerCase();
        switch (month) {
            case "january": case "jan": return 1;
            case "february": case "feb": return 2;
            case "march": case "mar": return 3;
            case "april": case "apr": return 4;
            case "may": return 5;
            case "june": case "jun": return 6;
            case "july": case "jul": return 7;
            case "august": case "aug": return 8;
            case "september": case "sep": return 9;
            case "october": case "oct": return 10;
            case "november": case "nov": return 11;
            case "december": case "dec": return 12;
            default: return LocalDate.now().getMonthValue();
        }
    }

    /**
     * Navigate to target month/year in date picker
     */
    private void navigateToTargetMonth(LocalDate currentDate, LocalDate targetDate) {
        try {
            int monthsToNavigate = calculateMonthsDifference(currentDate, targetDate);
            System.out.println("Need to navigate " + monthsToNavigate + " months");

            if (monthsToNavigate == 0) {
                System.out.println("Already in target month");
                return;
            }

            boolean navigateForward = monthsToNavigate > 0;
            int absoluteMonths = Math.abs(monthsToNavigate);

            for (int i = 0; i < absoluteMonths && i < 24; i++) { // Safety limit
                if (navigateForward) {
                    clickNextMonth();
                } else {
                    clickPreviousMonth();
                }
                page.waitForTimeout(500); // Wait between clicks
            }

        } catch (Exception e) {
            System.err.println("Error navigating to target month: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Calculate months difference between two dates
     */
    private int calculateMonthsDifference(LocalDate from, LocalDate to) {
        return (to.getYear() - from.getYear()) * 12 + (to.getMonthValue() - from.getMonthValue());
    }

    /**
     * Click next month button with multiple selector fallbacks
     */
    private void clickNextMonth() {
        List<String> nextMonthSelectors = Arrays.asList(
            "xpath=//button[@aria-label='Next month']",
            "xpath=//button[contains(@class,'next')]",
            "xpath=//*[name()='path' and contains(@d,'M10 8L12.1')]",
            "xpath=//button[@aria-label='Next month']//*[name()='svg']"
        );

        for (String selector : nextMonthSelectors) {
            try {
                Locator button = page.locator(selector);
                if (button.count() > 0 && button.first().isVisible()) {
                    button.first().click(new Locator.ClickOptions().setForce(true));
                    return;
                }
            } catch (Exception e) {
                // Continue to next selector
            }
        }

        throw new RuntimeException("Could not find next month button");
    }

    /**
     * Click previous month button with multiple selector fallbacks
     */
    private void clickPreviousMonth() {
        List<String> prevMonthSelectors = Arrays.asList(
            "xpath=//button[@aria-label='Previous month']",
            "xpath=//button[contains(@class,'prev')]",
            "xpath=//*[name()='path' and contains(@d,'M14 8L12')]",
            "xpath=//button[@aria-label='Previous month']//*[name()='svg']"
        );

        for (String selector : prevMonthSelectors) {
            try {
                Locator button = page.locator(selector);
                if (button.count() > 0 && button.first().isVisible()) {
                    button.first().click(new Locator.ClickOptions().setForce(true));
                    return;
                }
            } catch (Exception e) {
                // Continue to next selector
            }
        }

        throw new RuntimeException("Could not find previous month button");
    }

    /**
     * Select specific date in the current month view
     */
    private void selectDateInPicker(LocalDate targetDate) {
        try {
            String dateString = targetDate.toString(); // Format: YYYY-MM-DD

            // Try multiple selector patterns for date buttons
            List<String> dateSelectors = Arrays.asList(
                "xpath=//button[@aria-label='" + dateString + "']",
                "xpath=//button[contains(@aria-label,'" + targetDate.getDayOfMonth() + "')]",
                "xpath=//td[@aria-label='" + dateString + "']//button",
                "xpath=//div[@aria-label='" + dateString + "']",
                "xpath=//button[text()='" + targetDate.getDayOfMonth() + "']"
            );

            for (String selector : dateSelectors) {
                try {
                    Locator dateButton = page.locator(selector);
                    if (dateButton.count() > 0 && dateButton.first().isVisible()) {
                        System.out.println("Found date using selector: " + selector);
                        dateButton.first().click();
                        page.waitForTimeout(1000);
                        return;
                    }
                } catch (Exception e) {
                    // Continue to next selector
                }
            }

            throw new RuntimeException("Could not find date button for: " + targetDate);

        } catch (Exception e) {
            System.err.println("Error selecting date: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Close the date picker
     */
    private void closeDatePicker() {
        try {
            // Click outside the date picker to close it
            page.click("body");
            page.waitForTimeout(1000);

            // Verify date picker is closed
            if (isDatePickerOpen()) {
                // Try pressing Escape key
                page.keyboard().press("Escape");
                page.waitForTimeout(1000);
            }

            System.out.println("✅ Date picker closed");

        } catch (Exception e) {
            System.err.println("Warning: Could not close date picker: " + e.getMessage());
        }
    }

    protected void staticDateSelection() {
        retryOperation(() -> {
            System.out.println("=== Starting Static Date Selection ===");

            // Open date picker
            openDatePicker();

            // Use fixed dates for consistent testing
            LocalDate specificStartDate = LocalDate.of(2026, Month.JUNE, 5);
            LocalDate specificEndDate = specificStartDate.plusDays(10);

            System.out.println("Target start date: " + specificStartDate);
            System.out.println("Target end date: " + specificEndDate);

            // Navigate to target month and select dates
            navigateToDateAndSelect(specificStartDate, "start");
            lastSelectedStartDate = specificStartDate;

            navigateToDateAndSelect(specificEndDate, "end");
            lastSelectedEndDate = specificEndDate;

            // Close date picker
            closeDatePicker();

            System.out.println("✅ Static date selection completed successfully");
        }, 3);
    }

    /**
     * Debug method to inspect date picker state and available elements
     */
    protected void debugDatePicker() {
        try {
            System.out.println("=== DEBUG: Date Picker Analysis ===");

            // Open date picker
            openDatePicker();

            // Check for date picker container
            List<String> containerSelectors = Arrays.asList(
                "xpath=//div[contains(@class,'react-datepicker')]",
                "xpath=//div[contains(@class,'datepicker')]",
                "xpath=//div[@role='dialog']"
            );

            for (String selector : containerSelectors) {
                int count = page.locator(selector).count();
                if (count > 0) {
                    System.out.println("✅ Found date picker container: " + selector + " (count: " + count + ")");
                }
            }

            // Check for navigation buttons
            List<String> navSelectors = Arrays.asList(
                "xpath=//button[@aria-label='Next month']",
                "xpath=//button[@aria-label='Previous month']",
                "xpath=//button[contains(@class,'next')]",
                "xpath=//button[contains(@class,'prev')]"
            );

            for (String selector : navSelectors) {
                int count = page.locator(selector).count();
                if (count > 0) {
                    System.out.println("✅ Found navigation button: " + selector + " (count: " + count + ")");
                }
            }

            // Check for month/year display
            List<String> headerSelectors = Arrays.asList(
                "xpath=//div[contains(@class,'current-month')]",
                "xpath=//div[contains(@class,'header')]",
                "xpath=//span[contains(@class,'month')]"
            );

            for (String selector : headerSelectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        String text = page.locator(selector).first().innerText();
                        System.out.println("✅ Found month/year header: " + selector + " - Text: '" + text + "'");
                    }
                } catch (Exception e) {
                    // Continue
                }
            }

            // Check for date buttons (show first few examples)
            String dateButtonSelector = "xpath=//button[@aria-label]";
            int count = page.locator(dateButtonSelector).count();
            if (count > 0) {
                System.out.println("✅ Found " + count + " date buttons");

                // Show first few aria-labels as examples
                for (int i = 0; i < Math.min(10, count); i++) {
                    try {
                        String ariaLabel = page.locator(dateButtonSelector).nth(i).getAttribute("aria-label");
                        System.out.println("   - Button " + i + ": aria-label='" + ariaLabel + "'");
                    } catch (Exception e) {
                        // Continue
                    }
                }
            }

            System.out.println("=== DEBUG: End of Date Picker Analysis ===");

        } catch (Exception e) {
            System.err.println("Error during date picker debug: " + e.getMessage());
        }
    }

    /**
     * Enhanced date selection with debug information
     */
    protected void dateSelectionWithDebug() {
        try {
            System.out.println("=== Starting Date Selection with Debug ===");

            // First, debug the date picker
            debugDatePicker();

            // Then try normal date selection
            dateSelection();

        } catch (Exception e) {
            System.err.println("❌ Date selection with debug failed: " + e.getMessage());

            // Take screenshot for debugging
            takeScreenshotOnFailure("date_selection_debug_failure");

            throw e;
        }
    }

    void addOffsiteLocation() {
        retryOperation(() -> {
            page.locator("xpath=//p[normalize-space()='Add / Edit Offsite Location']").click();
            page.locator("xpath=//p[normalize-space()='Submit']").click();
            page.keyboard().press("Enter");
            page.waitForTimeout(4000);
            page.locator("xpath=//input[@id='locationName--0']").fill("Test RB Location");
            retryOperation(() -> {
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").click();
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").fill("America/L");
                page.keyboard().press("Backspace");
                page.getByTestId("timeZone3--0").getByTestId("Widgets::SelectInput_input").fill("America/L");
                page.getByTestId("ListBox::ListBoxItem::1").click();
            }, 1);
            page.locator("xpath=//input[@id='addressLookup2--0']").fill("700 Ritchie Rd, Davenport, FL, USA");
            page.locator("xpath=//*[contains(text(),'700 Ritchie Rd, Davenport, FL, USA')]").first().click();
            page.waitForTimeout(2000);
            page.locator("xpath=//input[@id='phoneNumber--0']").fill("9885346424");
            page.locator("xpath=//input[@id='email--0']").fill("rbcom");
            Assertions.assertEquals("Please enter a valid email address.", page.locator("xpath=//div[contains(text(),'Please enter a valid email address.')]").innerText());
            page.waitForTimeout(4000);
            page.keyboard().press("Backspace");
            page.locator("xpath=//input[@id='email--0']").fill("<EMAIL>");
            page.locator("xpath=//p[normalize-space()='Submit']").click();
            page.keyboard().press("Enter");
            Locator addEditLink = page.locator("xpath=//p[normalize-space()='Add / Edit Offsite Location']");
            waitForElementVisible(page, addEditLink);
            page.waitForTimeout(2000);
        }, 1);
    }

    void createNewAuction() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForSelector("xpath=//*[contains(text(),'Create New')]");
            page.locator("xpath=//*[contains(text(),'Create New')]").click();
        }, 1);
    }

    void createNewCopyAuction() {

        retryOperation(() -> {
            page.waitForSelector("xpath=//p[normalize-space()='Create Auction']");
            page.locator("xpath=//p[normalize-space()='Create Auction']").click();
            page.waitForSelector("xpath=//*[contains(text(),'Copy Auction')]");
            page.locator("xpath=//*[contains(text(),'Copy Auction')]").first().click();
        }, 1);
    }


    protected boolean successMessage() {
        try {

            checkForErrorMessages();
            retryOperation(() -> {
                System.out.println("Waiting for success message...");

                try {
                    List<String> successSelectors = Arrays.asList(
                            "xpath=//*[contains(text(),'Event Created Successfully')]",
                            "xpath=//*[contains(text(),'Event created successfully')]",
                            "xpath=//*[contains(text(),'Successfully created')]",
                            "xpath=//*[contains(text(),'Created successfully')]"
                    );

                    Locator successElement = null;
                    String foundSelector = null;

                    for (String selector : successSelectors) {
                        try {
                            page.waitForSelector(selector, new Page.WaitForSelectorOptions().setTimeout(5000));
                            successElement = page.locator(selector);
                            if (successElement.count() > 0 && successElement.first().isVisible()) {
                                foundSelector = selector;
                                break;
                            }
                        } catch (Exception e) {
                            throw new RuntimeException("Failed to find success message with selector: " + selector, e);
                        }
                    }

                    if (successElement == null || foundSelector == null) {
                        throw new RuntimeException("Success message not found with any selector");
                    }


                    if (!successElement.first().isVisible()) {
                        throw new RuntimeException("Success message element found but not visible");
                    }

                    page.waitForTimeout(2000);
                    String message = successElement.first().innerText();
                    String saleNumber = message.replaceAll("\\D+", "");
                    page.waitForSelector("xpath=//input[@id='searchAuction--0']",
                            new Page.WaitForSelectorOptions().setTimeout(10000));

                } catch (Exception e) {
                    checkForErrorMessages();
                    throw e; // Re-throw to trigger retry
                }
            }, 3);
            return true;

        } catch (Exception e) {
            checkForErrorMessages();
            return false; // Success message not found
        }
    }

    private void checkForErrorMessages() {
        try {

            List<String> errorSelectors = Arrays.asList(
                    "xpath=//*[contains(text(),'Error: Failed to Create Event')]",
                    "xpath=//*[contains(text(),'Failed to Create Event')]",
                    "xpath=//*[contains(text(),'Error')]",
                    "xpath=//*[contains(text(),'failed')]",
                    "xpath=//*[contains(text(),'Failed')]",
                    "xpath=//div[contains(@class,'error')]",
                    "xpath=//div[contains(@class,'alert-danger')]",
                    "xpath=//*[@role='alert']"
            );

            for (String selector : errorSelectors) {
                try {
                    if (page.locator(selector).count() > 0) {
                        Locator errorElement = page.locator(selector).first();
                        if (errorElement.isVisible()) {
                            String errorText = errorElement.innerText();

                            if (errorText.toLowerCase().contains("failed to create event")
                                    || errorText.toLowerCase().contains("error: failed to create")
                                    || errorText.toLowerCase().contains("creation failed")
                                    || errorText.toLowerCase().contains("unable to create")
                                    || errorText.toLowerCase().contains("event creation error")) {


                                throw new SuccessMessageNotFoundException("Event creation failed: " + errorText + " - Test should be retried");
                            }
                        }
                    }
                } catch (SuccessMessageNotFoundException e) {
                    throw e;
                } catch (Exception ignored) {
                    throw new RuntimeException("Failed to check for error messages", ignored);
                }
            }

        } catch (SuccessMessageNotFoundException e) {
            throw e; // Re-throw our custom exception
        } catch (Exception e) {
            throw new RuntimeException("Failed to check for error messages", e);
        }
    }


    protected void successMessageRequired() {
        if (!successMessage()) {
            throw new RuntimeException("Required success message was not found - test should be retried");
        }
    }


    protected void debugSuccessElements() {
        try {
            System.out.println("=== DEBUG: Checking for success-related elements ===");

            // Check for various success message patterns
            List<String> debugSelectors = Arrays.asList(
                    "xpath=//*[contains(text(),'Event Created Successfully')]",
                    "xpath=//*[contains(text(),'Event created successfully')]",
                    "xpath=//*[contains(text(),'Successfully')]",
                    "xpath=//*[contains(text(),'Created')]",
                    "xpath=//*[contains(text(),'Success')]",
                    "xpath=//*[contains(text(),'Event')]"
            );

            for (String selector : debugSelectors) {
                try {
                    int count = page.locator(selector).count();
                    if (count > 0) {
                        for (int i = 0; i < count; i++) {
                            Locator element = page.locator(selector).nth(i);
                            if (element.isVisible()) {
                                String text = element.innerText();
                                System.out.println("✅ Found element with selector '" + selector + "': " + text);
                            }
                        }
                    }
                } catch (Exception e) {
                    // Continue to next selector
                }
            }

            System.out.println("=== DEBUG: End of success elements check ===");

        } catch (Exception e) {
            System.err.println("Error during debug: " + e.getMessage());
        }
    }


    protected void validateSuccessMessageWithDebug() {
        try {
            System.out.println("=== Starting Success Message Validation with Debug ===");

            debugSuccessElements();

            if (!successMessage()) {

                throw new SuccessMessageNotFoundException("Success message not found even after debug check");
            }


        } catch (Exception e) {

            throw e;
        }
    }

    protected void errorMessage() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//*[contains(text(),'Error: Failed to Create Event.')]");
            page.locator("xpath=//*[contains(text(),'Error: Failed to Create Event.')]").isVisible();
            page.waitForTimeout(5000);
            String message = page.locator("xpath=//*[contains(text(),'Error: Failed to Create Event.')]").innerText();
            Assertions.assertTrue(message.contains("Error: Failed to Create Event. Query did not return a unique result: 6 results were returned"));
        }, 1);
    }

    void hostSiteSelection(String siteName) {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='sites2--0']");
            page.locator("xpath=//input[@id='sites2--0']").click();
            page.locator("xpath=//input[@id='sites2--0']").fill(siteName);
            page.waitForTimeout(2000);
            try {
                if (page.getByTestId("ListBox::ListBoxItem::0").isVisible()) {
                    page.getByTestId("ListBox::ListBoxItem::0").click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {

                if (page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().isVisible()) {
                    page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                if (page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'" + siteName + "')]").first().isVisible()) {
                    page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'" + siteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                page.evaluate("() => {"
                        + "const options = Array.from(document.querySelectorAll('*'));"
                        + "const option = options.find(el => el.textContent && el.textContent.includes('" + siteName + "'));"
                        + "if (option) { option.click(); }"
                        + "}");
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
        }, 1);
    }

    void hostSiteUpdate(String siteName) {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='hostSite--0']");
            page.locator("xpath=//input[@id='hostSite--0']").clear();
            page.locator("xpath=//input[@id='hostSite--0']").click();
            page.locator("xpath=//input[@id='hostSite--0']").fill(siteName);
            page.waitForTimeout(2000);
            try {
                if (page.getByTestId("ListBox::ListBoxItem::0").isVisible()) {
                    page.getByTestId("ListBox::ListBoxItem::0").click();
                    page.click("body");
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {

                if (page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().isVisible()) {
                    page.locator("xpath=//*[contains(text(),'" + siteName + "')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                if (page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'\"+siteName+\"')]").first().isVisible()) {
                    page.locator("xpath=//div[contains(@class,'hoToL4')]//*[contains(text(),'\"+siteName+\"')]").first().click();
                    return;
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
            try {
                page.evaluate("() => {"
                        + "const options = Array.from(document.querySelectorAll('*'));"
                        + "const option = options.find(el => el.textContent && el.textContent.includes('\"+siteName+\"'));"
                        + "if (option) { option.click(); }"
                        + "}");
            } catch (Exception e) {
                throw new RuntimeException("Failed to select host site");
            }
        }, 1);
    }

    void advertisedName() {
        retryOperation(() -> {
            int id = secRandom.nextInt();
            page.locator("xpath=//input[@id='primaryClassification--0']").click();
            page.locator("xpath=//*[contains(text(),'Agriculture')]").click();
            page.locator("xpath=//input[@id='advertisedName--0']").fill("Noonan" + id);
        }, 1);
    }

    void additionalClassification() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='additionalClassification--0']");
            page.locator("xpath=//input[@id='additionalClassification--0']").click();
            page.locator("xpath=//input[@id='additionalClassification--0']").fill("Agriculture");
            page.locator("xpath=//*[contains(text(),'Agriculture')]").click();
            page.click("body");
        }, 1);
    }

    void brandSelection() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='brandSelect--0']").click();
            page.locator("xpath=//input[@id='brandSelect--0']").fill("RBA");
            page.locator("xpath=//*[contains(text(),'RBA')]").click();
            page.click("body");
        }, 1);
    }

    void legalEntity() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='legalEntity--0']");
            page.locator("xpath=//input[@id='legalEntity--0']").waitFor();
            page.locator("xpath=//input[@id='legalEntity--0']").click(new Locator.ClickOptions().setForce(true));
            page.locator("xpath=//input[@id='legalEntity--0']").fill("RR.B. Services SARL");
            page.locator("xpath=//*[contains(text(),'RR.B. Services SARL')]").click();
            page.click("body");
        }, 1);
    }

    void businessUnit() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='singleBusinessUnit--0']");
            page.locator("xpath=//input[@id='singleBusinessUnit--0']").click();
            page.locator("xpath=//input[@id='singleBusinessUnit--0']").fill("550 Melbourne");
            page.locator("xpath=//*[contains(text(),'550 Melbourne')]").click();
            page.click("body");
        }, 1);
    }

    void currencySelection() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='singleCurrency--0']").click();
            page.locator("xpath=//*[contains(text(),'USD - U.S.Dollar')]").click();
        }, 1);
    }

    void siteConfigration() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='eventSiteConfiguration--0']");
            page.waitForSelector("xpath=//input[@id='eventSiteConfiguration--0']").click();
            page.locator("xpath=//input[@id='eventSiteConfiguration--0']").fill("Austin Off-site Template");
            page.locator("xpath=//*[contains(text(),'Austin Off-site Template')]").click();
            page.click("body");

        }, 1);
    }

    void auctionlocationTypePermanent() {
        retryOperation(() -> {
            page.waitForSelector("xpath=//input[@id='auctionNumber--0']");
            page.waitForSelector("xpath=//input[@id='auctionLocationType2--0']");
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Permanent");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Permanent')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Permanent')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Permanent')]").first().click();
        }, 1);
    }

    void auctionlocationTypeOffSite() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Off-Site");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Off-Site')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Off-Site')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Off-Site')]").first().click();
        }, 1);
    }

    void auctionlocationTypeOntheFarm() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("On the Farm");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'On the Farm')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'On the Farm')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'On the Farm')]").first().click();
        }, 1);
    }

    void auctionlocationTypeRegionalAuctionSite() {
        retryOperation(() -> {
            page.locator("xpath=//input[@id='auctionLocationType2--0']").click();
            page.locator("xpath=//input[@id='auctionLocationType2--0']").fill("Regional Auction Site");
            if (page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Regional Auction Site')]").isVisible()) {
                page.locator("xpath=//div[@class='hoToL4 hSQnTb yQQ1IE']//*[contains(text(),'Regional Auction Site')]").first().click();
            }
            page.locator("xpath=//*[contains(text(),'Regional Auction Site')]").first().click();
        }, 1);
    }

    void manageAuctionAssertions() {
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        Assertions.assertEquals("Auction Details", page.locator("xpath=//h4[normalize-space()='Auction Details']").innerText());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Noonan, ND, USA - Apr 3, 2019", page.locator("xpath=//input[@id='hostSite--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("New", page.locator("xpath=//input[@id='operationalStatus--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("America/Los_Angeles", page.locator("xpath=//input[@id='updatedTimezone--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Permanent", page.locator("xpath=//input[@id='updatedLocationType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Agriculture", page.locator("xpath=//input[@id='updatedPrimaryType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RBA", page.locator("xpath=//input[@id='updatedBrand--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RR.B. Services SARL", page.locator("xpath=//input[@id='legalEntityUpdated--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("USD - U.S.Dollar", page.locator("xpath=//input[@id='updatedCurrency--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("550 Melbourne", page.locator("xpath=//input[@id='updatedBusinessUnit--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Austin Off-site Template", page.locator("xpath=//input[@id='SiteConfigurationUpdated--0']").inputValue());

    }

    void manageCopyAuctionAssertions() {

        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.locator("xpath=//div[@role='row'][1]/div[@role='gridcell'][2]").first().innerText();
        page.waitForSelector("xpath=//h4[normalize-space()='Auction Details']");
        page.waitForTimeout(1000);
        Assertions.assertEquals("Auction Details", page.locator("xpath=//h4[normalize-space()='Auction Details']").innerText());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Sacramento", page.locator("xpath=//input[@id='hostSite--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("New", page.locator("xpath=//input[@id='operationalStatus--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("America/Los_Angeles", page.locator("xpath=//input[@id='updatedTimezone--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Permanent", page.locator("xpath=//input[@id='updatedLocationType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Industrial", page.locator("xpath=//input[@id='updatedPrimaryType--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("RBA", page.locator("xpath=//input[@id='updatedBrand--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Ritchie Bros. Auctioneers (America) Inc.", page.locator("xpath=//input[@id='legalEntityUpdated--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("USD - U.S.Dollar", page.locator("xpath=//input[@id='updatedCurrency--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("272 Northern California", page.locator("xpath=//input[@id='updatedBusinessUnit--0']").inputValue());
        page.waitForTimeout(1000);
        Assertions.assertEquals("Chicago On-site Template", page.locator("xpath=//input[@id='SiteConfigurationUpdated--0']").inputValue());
    }

    protected void ensureSuccessOrRetry() {
        if (!successMessage()) {
            throw new SuccessMessageNotFoundException("Required success message was not found - test should be retried");
        }
    }


    protected void validateSuccessMessage() {
        try {
            if (!successMessage()) {

                String currentUrl = page.url();
                String pageTitle = page.title();

                String errorMessage = String.format(
                        "Success message validation failed. Current URL: %s, Page Title: %s. "
                                + "Expected to find 'Event Created Successfully' message but it was not present.",
                        currentUrl, pageTitle
                );

                throw new SuccessMessageNotFoundException(errorMessage);
            }
        } catch (SuccessMessageNotFoundException e) {
            throw e;
        } catch (Exception e) {

            throw new SuccessMessageNotFoundException("Unexpected error during success message validation: " + e.getMessage(), e);
        }
    }


    protected void executeTestWithRetryOnSuccessFailure(Runnable testLogic, String testName, int maxRetries) {
        int attempt = 1;
        boolean success = false;
        Exception lastException = null;

        while (attempt <= maxRetries && !success) {
            try {
                resetTestState();
                testLogic.run();
                success = true;


            } catch (Exception e) {
                lastException = e;

                boolean isRetryable = e.getMessage() != null && (
                        e.getMessage().toLowerCase().contains("success message")
                                || e.getMessage().toLowerCase().contains("event created successfully")
                                || e.getMessage().toLowerCase().contains("failed to create event")
                                || e.getMessage().toLowerCase().contains("event creation failed")
                                || e instanceof SuccessMessageNotFoundException
                );

                if (isRetryable && attempt < maxRetries) {

                    try {
                        Thread.sleep(5000); // Wait before retry
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Test retry interrupted", ie);
                    }
                } else if (!isRetryable) {
                    throw e; // Non-retryable error, fail immediately
                }

                attempt++;
            }
        }

        if (!success && lastException != null) {
            throw new RuntimeException("Test " + testName + " failed after " + maxRetries + " attempts", lastException);
        }
    }


    protected static void resetStaticDateFields() {
        lastSelectedStartDate = null;
        lastSelectedEndDate = null;
        System.out.println("Static date fields reset completed");
    }


    protected void resetTestState() {
        try {

            resetStaticDateFields();

            if (page != null) {
                page.navigate(baseUrl);
                page.waitForTimeout(3000);
            }

        } catch (Exception e) {
            throw new RuntimeException("Failed to reset test state", e);
        }
    }


}
