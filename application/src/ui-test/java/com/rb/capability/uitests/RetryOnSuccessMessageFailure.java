package com.rb.capability.uitests;

import org.junit.jupiter.api.extension.ExtendWith;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark tests that should be retried if success message validation fails
 * 
 * Usage:
 * @RetryOnSuccessMessageFailure(maxRetries = 3)
 * @Test
 * void myTest() {
 *     // test logic
 *     successMessage(); // or ensureSuccessOrRetry()
 * }
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@ExtendWith(SuccessMessageRetryExtension.class)
public @interface RetryOnSuccessMessageFailure {
    
    /**
     * Maximum number of retry attempts
     * @return max retry count (default: 3)
     */
    int maxRetries() default 3;
    
    /**
     * Delay between retries in milliseconds
     * @return delay in milliseconds (default: 5000)
     */
    long delayMs() default 5000;
    
    /**
     * Whether to take screenshots on failure for debugging
     * @return true to take screenshots (default: true)
     */
    boolean takeScreenshots() default true;
}
