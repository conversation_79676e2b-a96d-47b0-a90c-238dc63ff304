package com.rb.capability.base;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.*;
import org.junit.jupiter.api.*;
import org.junit.platform.commons.logging.Logger;
import org.junit.platform.commons.logging.LoggerFactory;

import java.nio.file.Paths;


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class BaseTest {
    protected static final Logger log = LoggerFactory.getLogger(BaseTest.class);
    protected Playwright playwright;
    protected Browser browser;
    protected BrowserContext browserContext;
    protected Page page = null;
    protected String baseUrl;

    @BeforeAll
    public void launchBrowser() throws InterruptedException {

        //  if (System.getProperty("dev").equals("dev")) {
        baseUrl = "https://auction-management.tmx.dev.ritchiebros.com/";
        //   } else if (System.getProperty("dev").equals("qa")) {
        //       baseUrl = "https://auction-management.tmx.qa.ritchiebros.com/";
        //    } else {
        //       log.wait(500);
        //   }
        playwright = Playwright.create();
        browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(false));
    }

    @BeforeEach
    public void createBrowserContext() {

        browserContext = browser.newContext(new Browser.NewContextOptions().setStorageStatePath(Paths.get("src/ui-test/resources/storageState.json")));
        page = browserContext.newPage();
        page.setDefaultTimeout(100000);
    }

    public static void waitForElementVisible(Page page, Locator locator) {
        for (int i = 0; i < 30; i++) {
            if (locator.isVisible()) {
                break;
            }
            page.waitForTimeout(2000);
        }
    }

    public static void waitForElementEnabled(Page page, Locator locator) {
        for (int i = 0; i < 50; i++) {
            if (locator.isEnabled()) {
                break;
            }
            page.waitForTimeout(2000);
        }
    }

    public static void locateElementAndFillWithValue(Page page, Locator locator) {
        for (int i = 0; i < 50; i++) {
            if (locator.isEnabled()) {
                break;
            }
            page.waitForTimeout(2000);
        }
    }

    @AfterEach
    public void closePage() {
        page.close();
    }

    @AfterAll
    public void closeBrowser() {
        browserContext.close();
    }
}