package com.rb.capability.auctionmanagement.contract.provider;

import au.com.dius.pact.provider.junit5.HttpTestTarget;
import au.com.dius.pact.provider.junit5.PactVerificationContext;
import au.com.dius.pact.provider.junit5.PactVerificationInvocationContextProvider;
import au.com.dius.pact.provider.junitsupport.Provider;
import au.com.dius.pact.provider.junitsupport.State;
import au.com.dius.pact.provider.junitsupport.loader.PactBroker;
import au.com.dius.pact.provider.junitsupport.loader.PactBrokerAuth;
import com.rb.capability.auctionmanagement.client.EventLocationType;
import com.rb.capability.auctionmanagement.client.PlacesServiceClient;
import com.rb.capability.auctionmanagement.domain.enums.ParticipationMethod;
import com.rb.capability.auctionmanagement.domain.enums.SellingFormat;
import com.rb.capability.auctionmanagement.infra.jpa.entity.EventEntity;
import com.rb.capability.auctionmanagement.infra.jpa.repository.EventRepository;
import com.rb.capability.auctionmanagement.resource.response.BusinessUnit;
import com.rb.capability.auctionmanagement.resource.response.LocationResponse;
import com.rb.capability.auctionmanagement.resource.response.SiteResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestTemplate;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Provider contract test for the Event Details API
 * This test verifies that the provider satisfies the contract defined by consumers
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Provider("auction-management-service")
@PactBroker(
    url = "${pact.broker.url:http://localhost:9292}",
    authentication = @PactBrokerAuth(token = "${pact.broker.token:}")
)
public class EventDetailsProviderContractTest {

    @LocalServerPort
    private int port;

    @MockBean
    private EventRepository eventRepository;

    @MockBean
    private PlacesServiceClient placesServiceClient;

    @BeforeEach
    void setUp(PactVerificationContext context) {
        context.setTarget(new HttpTestTarget("localhost", port));
    }

    @TestTemplate
    @ExtendWith(PactVerificationInvocationContextProvider.class)
    void pactVerificationTestTemplate(PactVerificationContext context) {
        context.verifyInteraction();
    }

    /**
     * Provider state: an event exists with the specified GUID
     */
    @State("an event exists with GUID 123e4567-e89b-12d3-a456-************")
    void eventExistsWithGuid() {
        // Create mock event entity
        EventEntity mockEvent = createMockEventEntity();
        
        // Mock repository response
        when(eventRepository.findByEventGuid("123e4567-e89b-12d3-a456-************"))
            .thenReturn(Optional.of(mockEvent));
        
        // Mock places service responses
        mockPlacesServiceResponses();
    }

    /**
     * Provider state: no event exists with the specified GUID
     */
    @State("no event exists with GUID 999e4567-e89b-12d3-a456-************")
    void noEventExistsWithGuid() {
        // Mock repository to return empty
        when(eventRepository.findByEventGuid("999e4567-e89b-12d3-a456-************"))
            .thenReturn(Optional.empty());
    }

    /**
     * Create a mock EventEntity for testing
     */
    private EventEntity createMockEventEntity() {
        EventEntity event = new EventEntity();
        
        // Set basic event properties
        event.setEventGuid("123e4567-e89b-12d3-a456-************");
        event.setSaleNumber("AUC-2024-001");
        event.setSourceAuctionNumber("SRC-2024-001");
        event.setName("Industrial Equipment Auction");
        event.setEventAdvertisedName("Edmonton Industrial Equipment Sale");
        event.setNumberOfDays(2);
        event.setBrand("RBA");
        event.setTimezone("America/Edmonton");
        event.setLegalEntityId("legal-entity-123");
        
        // Set dates
        event.setEventStartDate(Timestamp.valueOf(LocalDateTime.of(2024, 6, 15, 9, 0, 0)));
        event.setEventEndDate(Timestamp.valueOf(LocalDateTime.of(2024, 6, 16, 17, 0, 0)));
        event.setCreatedDate(Timestamp.valueOf(LocalDateTime.of(2024, 5, 1, 10, 30, 0)));
        event.setLastModifiedDate(Timestamp.valueOf(LocalDateTime.of(2024, 5, 15, 14, 20, 0)));
        event.setRegistrationAutoOpenDate(Timestamp.valueOf(LocalDateTime.of(2024, 6, 1, 0, 0, 0)));
        event.setInitialAuctionSyncDate(Timestamp.valueOf(LocalDateTime.of(2024, 5, 1, 10, 30, 0)));
        
        // Set other properties
        event.setCurrency(Arrays.asList("USD"));
        event.setPrimaryClassification("Industrial");
        event.setSchedulingStatus("Published");
        event.setStatus("Active");
        event.setSellingFormats(Arrays.asList(SellingFormat.LIVE_AUCTION));
        event.setWaysToParticipate(Arrays.asList(ParticipationMethod.ONSITE));
        event.setRegistrationOpen(true);
        event.setTalBidOpen(false);
        event.setPriorityBidOpen(false);
        event.setOlrEnabled(true);
        event.setLastModifierName("John Doe");
        event.setIsRbMarketplaceSale(false);
        event.setAllowIpInspection(true);
        event.setAdditionalClassification(Arrays.asList("Heavy Equipment"));
        event.setLocationType(EventLocationType.PERMANENT);
        event.setCreatedBy("system-user");
        event.setPrivateTreaty(false);
        event.setSiteConfiguration("Standard Configuration");
        event.setNotes("Special handling required for oversized equipment");
        event.setRolledOver(false);
        event.setDoNotUse(false);
        
        // Set location and site IDs
        event.setLocationId("loc-123");
        event.setSiteId("site-456");
        
        return event;
    }

    /**
     * Mock the places service responses
     */
    private void mockPlacesServiceResponses() {
        // Mock location response
        LocationResponse mockLocation = new LocationResponse(
            "loc-123",
            "Edmonton Yard",
            "123 Industrial Ave, Edmonton, AB",
            "Edmonton",
            "Alberta",
            "Canada",
            "T5J 0A1",
            null, // latitude
            null  // longitude
        );
        
        when(placesServiceClient.getLocationByLocationGuid("loc-123"))
            .thenReturn(mockLocation);
        
        // Mock site response
        SiteResponse mockSite = new SiteResponse(
            "site-456",
            "Main Auction Site",
            "PERMANENT",
            null, // address
            null, // city
            null, // province
            null, // country
            null, // postalCode
            null, // latitude
            null, // longitude
            null  // timezone
        );
        
        when(placesServiceClient.getSiteByGuid("site-456"))
            .thenReturn(mockSite);
    }

    /**
     * Additional state setup for different test scenarios
     */
    @State("event service is available")
    void eventServiceIsAvailable() {
        // Ensure all required services are properly mocked and available
        mockPlacesServiceResponses();
    }
}
