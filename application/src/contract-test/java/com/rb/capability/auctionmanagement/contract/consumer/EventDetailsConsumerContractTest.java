package com.rb.capability.auctionmanagement.contract.consumer;

import au.com.dius.pact.consumer.MockServer;
import au.com.dius.pact.consumer.dsl.PactDslWithProvider;
import au.com.dius.pact.consumer.junit5.PactConsumerTestExt;
import au.com.dius.pact.consumer.junit5.PactTestFor;
import au.com.dius.pact.core.model.RequestResponsePact;
import au.com.dius.pact.core.model.annotations.Pact;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static au.com.dius.pact.consumer.dsl.LambdaDsl.newJsonBody;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Consumer contract test for the Event Details API
 * This test defines the contract from the consumer's perspective
 */
@ExtendWith(PactConsumerTestExt.class)
@PactTestFor(providerName = "auction-management-service")
public class EventDetailsConsumerContractTest {

    private RestTemplate restTemplate;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        restTemplate = new RestTemplate();
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * Pact definition for successful event details retrieval
     */
    @Pact(consumer = "auction-management-consumer")
    public RequestResponsePact getEventDetailsSuccessfulPact(PactDslWithProvider builder) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);

        Map<String, String> responseHeaders = new HashMap<>();
        responseHeaders.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        return builder
            .given("an event exists with GUID 123e4567-e89b-12d3-a456-************")
            .uponReceiving("a request for event details")
            .path("/events/123e4567-e89b-12d3-a456-************")
            .method("GET")
            .headers(headers)
            .willRespondWith()
            .status(200)
            .headers(responseHeaders)
            .body(newJsonBody(body -> {
                body.stringType("auction_id", "123e4567-e89b-12d3-a456-************");
                body.stringType("auction_number", "AUC-2024-001");
                body.stringType("source_auction_number", "SRC-2024-001");
                body.stringType("name", "Industrial Equipment Auction");
                body.stringType("auction_advertised_name", "Edmonton Industrial Equipment Sale");
                body.integerType("number_of_days", 2);
                body.stringType("brand", "RBA");
                body.stringType("timezone", "America/Edmonton");
                body.stringType("legalEntityId", "legal-entity-123");
                body.stringType("start_date_time", "2024-06-15T09:00:00Z");
                body.stringType("end_date_time", "2024-06-16T17:00:00Z");
                body.array("currency", currency -> currency.stringValue("USD"));
                body.stringType("primaryClassification", "Industrial");
                body.stringType("schedulingStatus", "Published");
                body.stringType("operation_status", "Active");
                body.array("auction_formats", formats -> formats.stringValue("LIVE_AUCTION"));
                body.array("waysToParticipate", ways -> ways.stringValue("ONSITE"));
                body.booleanType("registrationOpen", true);
                body.stringType("registrationAutoOpenDate", "2024-06-01T00:00:00Z");
                body.booleanType("talBidOpen", false);
                body.nullValue("talBidAutoOpenDate");
                body.booleanType("priorityBidOpen", false);
                body.nullValue("priorityBidAutoOpenDate");
                body.booleanType("olrEnabled", true);
                body.stringType("createdDate", "2024-05-01T10:30:00Z");
                body.stringType("lastModifierName", "John Doe");
                body.stringType("lastModifiedDate", "2024-05-15T14:20:00Z");

                // Location object
                body.object("location", location -> {
                    location.stringType("locationId", "loc-123");
                    location.stringType("locationName", "Edmonton Yard");
                    location.stringType("address", "123 Industrial Ave, Edmonton, AB");
                    location.stringType("city", "Edmonton");
                    location.stringType("province", "Alberta");
                    location.stringType("country", "Canada");
                    location.stringType("postalCode", "T5J 0A1");
                });

                // Site object
                body.object("site", site -> {
                    site.stringType("siteId", "site-456");
                    site.stringType("siteName", "Main Auction Site");
                    site.stringType("siteType", "PERMANENT");
                });

                body.booleanType("rb_marketplace_auction", false);

                // Business Unit object
                body.object("business_unit", businessUnit -> {
                    businessUnit.stringType("businessUnitId", "bu-789");
                    businessUnit.stringType("businessUnitName", "Western Canada");
                    businessUnit.stringType("region", "West");
                });

                body.booleanType("allowIpInspection", true);
                body.array("additionalClassification", additional -> additional.stringValue("Heavy Equipment"));
                body.stringType("locationType", "PERMANENT");
                body.stringType("createdBy", "system-user");
                body.booleanType("privateTreaty", false);
                body.stringType("siteConfiguration", "Standard Configuration");
                body.stringType("notes", "Special handling required for oversized equipment");
                body.stringType("initialAuctionSyncDate", "2024-05-01T10:30:00Z");
                body.nullValue("eventFinalizedDate");
                body.booleanType("rolledOver", false);
                body.booleanType("doNotUse", false);
            }))
            .toPact();
    }

    /**
     * Pact definition for event not found scenario
     */
    @Pact(consumer = "auction-management-consumer")
    public RequestResponsePact getEventDetailsNotFoundPact(PactDslWithProvider builder) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);

        Map<String, String> responseHeaders = new HashMap<>();
        responseHeaders.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROBLEM_JSON_VALUE);

        return builder
            .given("no event exists with GUID 999e4567-e89b-12d3-a456-************")
            .uponReceiving("a request for non-existent event details")
            .path("/events/999e4567-e89b-12d3-a456-************")
            .method("GET")
            .headers(headers)
            .willRespondWith()
            .status(404)
            .headers(responseHeaders)
            .body(newJsonBody(body -> {
                body.stringType("title", "Not Found");
                body.integerType("status", 404);
                body.stringType("detail", "Event not found with GUID: 999e4567-e89b-12d3-a456-************");
            }))
            .toPact();
    }

    /**
     * Test successful event details retrieval
     */
    @Test
    @PactTestFor(pactMethod = "getEventDetailsSuccessfulPact")
    void testGetEventDetailsSuccessful(MockServer mockServer) {
        // Arrange
        String eventGuid = "123e4567-e89b-12d3-a456-************";
        String url = mockServer.getUrl() + "/events/" + eventGuid;

        // Act
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        
        Map<String, Object> responseBody = response.getBody();
        assertEquals(eventGuid, responseBody.get("auction_id"));
        assertEquals("AUC-2024-001", responseBody.get("auction_number"));
        assertEquals("Industrial Equipment Auction", responseBody.get("name"));
        assertEquals("Edmonton Industrial Equipment Sale", responseBody.get("auction_advertised_name"));
        assertEquals("RBA", responseBody.get("brand"));
        assertEquals("Industrial", responseBody.get("primaryClassification"));
        assertEquals("Published", responseBody.get("schedulingStatus"));
        assertEquals("Active", responseBody.get("operation_status"));
        assertTrue((Boolean) responseBody.get("registrationOpen"));
        assertFalse((Boolean) responseBody.get("rb_marketplace_auction"));
        
        // Verify location object
        Map<String, Object> location = (Map<String, Object>) responseBody.get("location");
        assertNotNull(location);
        assertEquals("loc-123", location.get("locationId"));
        assertEquals("Edmonton Yard", location.get("locationName"));
        
        // Verify business unit object
        Map<String, Object> businessUnit = (Map<String, Object>) responseBody.get("business_unit");
        assertNotNull(businessUnit);
        assertEquals("bu-789", businessUnit.get("businessUnitId"));
        assertEquals("Western Canada", businessUnit.get("businessUnitName"));
    }

    /**
     * Test event not found scenario
     */
    @Test
    @PactTestFor(pactMethod = "getEventDetailsNotFoundPact")
    void testGetEventDetailsNotFound(MockServer mockServer) {
        // Arrange
        String eventGuid = "999e4567-e89b-12d3-a456-************";
        String url = mockServer.getUrl() + "/events/" + eventGuid;

        // Act & Assert
        try {
            restTemplate.getForEntity(url, Map.class);
            fail("Expected exception was not thrown");
        } catch (HttpClientErrorException e) {
            // Verify that a 404 error was returned
            assertEquals(HttpStatus.NOT_FOUND, e.getStatusCode());
            assertTrue(e.getMessage().contains("404"));
        }
    }
}
