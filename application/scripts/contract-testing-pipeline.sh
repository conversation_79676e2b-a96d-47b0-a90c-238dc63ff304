#!/bin/bash

# Bidirectional Contract Testing Pipeline Script
# This script orchestrates the complete contract testing workflow

set -e

# Configuration
PACT_BROKER_BASE_URL=${PACT_BROKER_BASE_URL:-"https://your-pactflow-broker.pactflow.io"}
PACT_BROKER_TOKEN=${PACT_BROKER_TOKEN:-""}
CONSUMER_VERSION=${CONSUMER_VERSION:-$(git rev-parse --short HEAD)}
PROVIDER_VERSION=${PROVIDER_VERSION:-$(git rev-parse --short HEAD)}
BRANCH_NAME=${BRANCH_NAME:-$(git rev-parse --abbrev-ref HEAD)}

echo "=== Bidirectional Contract Testing Pipeline ==="
echo "Consumer Version: $CONSUMER_VERSION"
echo "Provider Version: $PROVIDER_VERSION"
echo "Branch: $BRANCH_NAME"
echo "Pact Broker URL: $PACT_BROKER_BASE_URL"

# Function to check if Pactflow is accessible
check_pactflow_connectivity() {
    echo "Checking Pactflow connectivity..."
    if curl -f -H "Authorization: Bearer $PACT_BROKER_TOKEN" "$PACT_BROKER_BASE_URL" > /dev/null 2>&1; then
        echo "✅ Pactflow is accessible"
    else
        echo "❌ Cannot connect to Pactflow. Please check your configuration."
        exit 1
    fi
}

# Function to run consumer contract tests
run_consumer_tests() {
    echo "=== Running Consumer Contract Tests ==="
    
    # Run consumer tests to generate pact files
    ./gradlew consumerContractTest \
        -Dpact.rootDir=build/pacts \
        -Dconsumer.version=$CONSUMER_VERSION
    
    if [ $? -eq 0 ]; then
        echo "✅ Consumer contract tests passed"
    else
        echo "❌ Consumer contract tests failed"
        exit 1
    fi
}

# Function to publish consumer contracts
publish_consumer_contracts() {
    echo "=== Publishing Consumer Contracts to Pactflow ==="
    
    # Publish consumer contracts using Pact CLI
    docker run --rm \
        -v "$(pwd)/build/pacts:/pacts" \
        -e PACT_BROKER_BASE_URL="$PACT_BROKER_BASE_URL" \
        -e PACT_BROKER_TOKEN="$PACT_BROKER_TOKEN" \
        pactfoundation/pact-cli:latest \
        publish /pacts \
        --consumer-app-version "$CONSUMER_VERSION" \
        --tag "$BRANCH_NAME" \
        --tag "main"
    
    if [ $? -eq 0 ]; then
        echo "✅ Consumer contracts published successfully"
    else
        echo "❌ Failed to publish consumer contracts"
        exit 1
    fi
}

# Function to run provider verification tests
run_provider_verification() {
    echo "=== Running Provider Verification Tests ==="
    
    # Start the application for testing
    echo "Starting application for provider verification..."
    ./gradlew bootRun &
    APP_PID=$!
    
    # Wait for application to start
    echo "Waiting for application to start..."
    sleep 30
    
    # Run provider verification tests
    ./gradlew providerContractTest \
        -Dpact.verifier.publishResults=true \
        -Dpact.provider.version=$PROVIDER_VERSION \
        -Dpact.broker.url=$PACT_BROKER_BASE_URL \
        -Dpact.broker.token=$PACT_BROKER_TOKEN \
        -Dpact.provider.tag=$BRANCH_NAME
    
    VERIFICATION_RESULT=$?
    
    # Stop the application
    kill $APP_PID
    
    if [ $VERIFICATION_RESULT -eq 0 ]; then
        echo "✅ Provider verification tests passed"
    else
        echo "❌ Provider verification tests failed"
        exit 1
    fi
}

# Function to publish provider verification results
publish_provider_verification() {
    echo "=== Publishing Provider Verification Results ==="
    
    # Tag the provider version
    docker run --rm \
        -e PACT_BROKER_BASE_URL="$PACT_BROKER_BASE_URL" \
        -e PACT_BROKER_TOKEN="$PACT_BROKER_TOKEN" \
        pactfoundation/pact-cli:latest \
        broker create-version-tag \
        --pacticipant "auction-management-service" \
        --version "$PROVIDER_VERSION" \
        --tag "$BRANCH_NAME"
    
    if [ $? -eq 0 ]; then
        echo "✅ Provider verification results published successfully"
    else
        echo "❌ Failed to publish provider verification results"
        exit 1
    fi
}

# Function to check if deployment is safe
can_i_deploy() {
    echo "=== Checking if deployment is safe ==="
    
    # Check if consumer and provider are compatible
    docker run --rm \
        -e PACT_BROKER_BASE_URL="$PACT_BROKER_BASE_URL" \
        -e PACT_BROKER_TOKEN="$PACT_BROKER_TOKEN" \
        pactfoundation/pact-cli:latest \
        broker can-i-deploy \
        --pacticipant "auction-management-consumer" \
        --version "$CONSUMER_VERSION" \
        --pacticipant "auction-management-service" \
        --version "$PROVIDER_VERSION" \
        --to-environment "production"
    
    if [ $? -eq 0 ]; then
        echo "✅ Deployment is safe - contracts are compatible"
    else
        echo "❌ Deployment is not safe - contract compatibility issues detected"
        exit 1
    fi
}

# Function to record deployment
record_deployment() {
    echo "=== Recording deployment to production ==="
    
    docker run --rm \
        -e PACT_BROKER_BASE_URL="$PACT_BROKER_BASE_URL" \
        -e PACT_BROKER_TOKEN="$PACT_BROKER_TOKEN" \
        pactfoundation/pact-cli:latest \
        broker record-deployment \
        --pacticipant "auction-management-service" \
        --version "$PROVIDER_VERSION" \
        --environment "production"
    
    if [ $? -eq 0 ]; then
        echo "✅ Deployment recorded successfully"
    else
        echo "❌ Failed to record deployment"
        exit 1
    fi
}

# Main execution flow
main() {
    case "${1:-all}" in
        "consumer")
            check_pactflow_connectivity
            run_consumer_tests
            publish_consumer_contracts
            ;;
        "provider")
            check_pactflow_connectivity
            run_provider_verification
            publish_provider_verification
            ;;
        "can-i-deploy")
            check_pactflow_connectivity
            can_i_deploy
            ;;
        "record-deployment")
            check_pactflow_connectivity
            record_deployment
            ;;
        "all")
            check_pactflow_connectivity
            run_consumer_tests
            publish_consumer_contracts
            run_provider_verification
            publish_provider_verification
            can_i_deploy
            ;;
        *)
            echo "Usage: $0 {consumer|provider|can-i-deploy|record-deployment|all}"
            echo ""
            echo "Commands:"
            echo "  consumer           - Run consumer tests and publish contracts"
            echo "  provider           - Run provider verification and publish results"
            echo "  can-i-deploy       - Check if deployment is safe"
            echo "  record-deployment  - Record deployment to production"
            echo "  all                - Run complete contract testing pipeline"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"

echo "=== Contract Testing Pipeline Completed Successfully ==="
