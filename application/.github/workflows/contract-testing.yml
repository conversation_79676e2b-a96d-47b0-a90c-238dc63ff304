name: Bidirectional Contract Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of contract test to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - consumer
          - provider
          - can-i-deploy

env:
  PACT_BROKER_BASE_URL: ${{ secrets.PACT_BROKER_BASE_URL }}
  PACT_BROKER_TOKEN: ${{ secrets.PACT_BROKER_TOKEN }}
  CONSUMER_VERSION: ${{ github.sha }}
  PROVIDER_VERSION: ${{ github.sha }}
  BRANCH_NAME: ${{ github.ref_name }}

jobs:
  consumer-contract-tests:
    name: Consumer Contract Tests
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.test_type == 'consumer' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == '' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          
      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
            
      - name: Make gradlew executable
        run: chmod +x ./gradlew
        
      - name: Run consumer contract tests
        run: |
          ./gradlew consumerContractTest \
            -Dpact.rootDir=build/pacts \
            -Dconsumer.version=${{ env.CONSUMER_VERSION }}
            
      - name: Upload pact files
        uses: actions/upload-artifact@v3
        with:
          name: pact-files
          path: build/pacts/
          
      - name: Publish consumer contracts to Pactflow
        run: |
          docker run --rm \
            -v "$(pwd)/build/pacts:/pacts" \
            -e PACT_BROKER_BASE_URL="${{ env.PACT_BROKER_BASE_URL }}" \
            -e PACT_BROKER_TOKEN="${{ env.PACT_BROKER_TOKEN }}" \
            pactfoundation/pact-cli:latest \
            publish /pacts \
            --consumer-app-version "${{ env.CONSUMER_VERSION }}" \
            --tag "${{ env.BRANCH_NAME }}" \
            --tag "main"

  provider-verification-tests:
    name: Provider Verification Tests
    runs-on: ubuntu-latest
    needs: consumer-contract-tests
    if: ${{ github.event.inputs.test_type == 'provider' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == '' }}
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: auction_management_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          
      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
            
      - name: Make gradlew executable
        run: chmod +x ./gradlew
        
      - name: Run database migrations
        run: ./gradlew flywayMigrate -Dspring.profiles.active=test
        
      - name: Run provider verification tests
        run: |
          ./gradlew providerContractTest \
            -Dpact.verifier.publishResults=true \
            -Dpact.provider.version=${{ env.PROVIDER_VERSION }} \
            -Dpact.broker.url=${{ env.PACT_BROKER_BASE_URL }} \
            -Dpact.broker.token=${{ env.PACT_BROKER_TOKEN }} \
            -Dpact.provider.tag=${{ env.BRANCH_NAME }} \
            -Dspring.profiles.active=test
            
      - name: Publish provider verification results
        run: |
          docker run --rm \
            -e PACT_BROKER_BASE_URL="${{ env.PACT_BROKER_BASE_URL }}" \
            -e PACT_BROKER_TOKEN="${{ env.PACT_BROKER_TOKEN }}" \
            pactfoundation/pact-cli:latest \
            broker create-version-tag \
            --pacticipant "auction-management-service" \
            --version "${{ env.PROVIDER_VERSION }}" \
            --tag "${{ env.BRANCH_NAME }}"

  can-i-deploy:
    name: Can I Deploy Check
    runs-on: ubuntu-latest
    needs: [consumer-contract-tests, provider-verification-tests]
    if: ${{ github.event.inputs.test_type == 'can-i-deploy' || github.event.inputs.test_type == 'all' || github.event.inputs.test_type == '' }}
    
    steps:
      - name: Check if deployment is safe
        run: |
          docker run --rm \
            -e PACT_BROKER_BASE_URL="${{ env.PACT_BROKER_BASE_URL }}" \
            -e PACT_BROKER_TOKEN="${{ env.PACT_BROKER_TOKEN }}" \
            pactfoundation/pact-cli:latest \
            broker can-i-deploy \
            --pacticipant "auction-management-consumer" \
            --version "${{ env.CONSUMER_VERSION }}" \
            --pacticipant "auction-management-service" \
            --version "${{ env.PROVIDER_VERSION }}" \
            --to-environment "production"
            
      - name: Record deployment (if on main branch)
        if: github.ref == 'refs/heads/main'
        run: |
          docker run --rm \
            -e PACT_BROKER_BASE_URL="${{ env.PACT_BROKER_BASE_URL }}" \
            -e PACT_BROKER_TOKEN="${{ env.PACT_BROKER_TOKEN }}" \
            pactfoundation/pact-cli:latest \
            broker record-deployment \
            --pacticipant "auction-management-service" \
            --version "${{ env.PROVIDER_VERSION }}" \
            --environment "production"

  contract-testing-summary:
    name: Contract Testing Summary
    runs-on: ubuntu-latest
    needs: [consumer-contract-tests, provider-verification-tests, can-i-deploy]
    if: always()
    
    steps:
      - name: Contract Testing Results
        run: |
          echo "=== Bidirectional Contract Testing Results ==="
          echo "Consumer Tests: ${{ needs.consumer-contract-tests.result }}"
          echo "Provider Verification: ${{ needs.provider-verification-tests.result }}"
          echo "Can I Deploy: ${{ needs.can-i-deploy.result }}"
          echo "Branch: ${{ env.BRANCH_NAME }}"
          echo "Consumer Version: ${{ env.CONSUMER_VERSION }}"
          echo "Provider Version: ${{ env.PROVIDER_VERSION }}"
          
          if [[ "${{ needs.consumer-contract-tests.result }}" == "success" && 
                "${{ needs.provider-verification-tests.result }}" == "success" && 
                "${{ needs.can-i-deploy.result }}" == "success" ]]; then
            echo "✅ All contract tests passed - deployment is safe"
          else
            echo "❌ Contract testing failed - deployment is not safe"
            exit 1
          fi
