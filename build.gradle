plugins {
    id 'java'
    id 'pmd'
    id 'checkstyle'
    id 'com.github.spotbugs-base' version '5.2.5'
    id 'io.spring.dependency-management' version '1.1.7'
}

allprojects {
    apply plugin: 'java'
    apply plugin: 'pmd'
    apply plugin: 'checkstyle'
    apply plugin: 'com.github.spotbugs-base'
    apply plugin: 'io.spring.dependency-management'

    group = 'com.rbauction'
    version = '0.0.1-SNAPSHOT'
    sourceCompatibility = '21'

    repositories {
        mavenCentral()
    }

    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    dependencies {
        spotbugsPlugins 'com.h3xstream.findsecbugs:findsecbugs-plugin:1.12.0'
    }

    checkstyle {
        configFile = rootProject.resources.text.fromFile("${rootDir}/config/checkstyle/checkstyle.xml").asFile()
        showViolations = true
        ignoreFailures = false
        maxWarnings = 0
        maxErrors = 0
        toolVersion('10.0')
    }

    tasks.withType(Checkstyle) {
        reports {
            xml.required = false
            html.required = true
        }
        exclude '**/spike/**'
    }

    pmd {
        ruleSets = ["${rootDir}/config/pmd/pmd.xml"]
        consoleOutput = true
        ignoreFailures = false
        toolVersion('6.44.0')
    }

    spotbugs {
        toolVersion = '4.8.3'
        ignoreFailures = false
        showStackTraces = true
        showProgress = true
        effort = 'max'
        excludeFilter = file("${rootDir}/config/spotbugs/spotbugs-exclude.xml")
    }

    task spotbugsTask(type: com.github.spotbugs.snom.SpotBugsTask) {
        dependsOn 'compileTestJava'
        dependsOn ':application:componentTestClasses'
        dependsOn ':application:integrationTestClasses'
        dependsOn ':application:contractTestClasses'
        dependsOn ':application:uiTestClasses'
        classDirs = files("${buildDir}/classes")
        reports {
            xml.enabled(false)
            html.enabled(true)
        }
    }

    check.dependsOn(spotbugsTask)
}

