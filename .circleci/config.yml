version: 2.1

# this allows you to use CircleCI's dynamic configuration feature
setup: true

# the path-filtering orb is required to continue a pipeline based on
# the path of an updated fileset
orbs:
  path-filtering: circleci/path-filtering@1.0.0



parameters:
  target-image-tag:
    description: The target image tag to deploy
    type: string
    default: default
  performance-test:
    description: Run performance tests in non-prod environment
    type: boolean
    default: false
  performance-test-prod:
    description: Run performance tests in prod environment
    type: boolean
    default: false
  performance-test-env:
    description: Environment to run performance tests (dev/qa)
    type: string
    default: dev
  external-url:
    description: External URL for performance testing
    type: string
    default: ""


workflows:
  configure-pipeline:
    jobs:
      # For main branch - both prod and non-prod jobs
      - path-filtering/filter:
          name: check-main-branch
          filters:
            branches:
              only: /main/
            tags:
              ignore: /.*/
          mapping: |
            # Always trigger build and deploy for main branch
            .* build-and-scan true
            # Additional specific file mappings
            .circleci/.* build-and-scan true
            .snyk build-and-scan true
            build.gradle build-and-scan true
            application/.* build-and-scan true
            .damslo.yaml build-and-scan true
            .k8s-logs.damslo.yaml build-and-scan true
            helm/.* build-and-scan true
            # Prod performance test mapping
            .* run-perf-test-prod << pipeline.parameters.performance-test-prod >>
            # Non-prod performance test mapping
            .* run-perf-test << pipeline.parameters.performance-test >>
            # External URL performance test mapping
            .* external-URL "<< pipeline.parameters.external-url >>"
            # Read env parameter
            .* performance-test-env-var "<< pipeline.parameters.performance-test-env >>"
          base-revision: main
          config-path: .circleci/continue_config.yml
          tag: "3.9"

      # For feature branches - only non-prod jobs
      - path-filtering/filter:
          name: check-feature-branch
          filters:
            branches:
              only: /feature\/.*/
            tags:
              ignore: /.*/
          mapping: |
            # Non-prod performance test mapping
            .* run-perf-test << pipeline.parameters.performance-test >>
            # External URL performance test mapping
            .* external-URL "<< pipeline.parameters.external-url >>"
            # Read env parameter
            .* performance-test-env-var "<< pipeline.parameters.performance-test-env >>"
          base-revision: main
          config-path: .circleci/continue_config.yml
          tag: "3.9"
