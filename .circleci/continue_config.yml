version: 2.1

orbs:
  jq: circleci/jq@2.2.0
  helm: circleci/helm@1.2.0
  kubernetes: circleci/kubernetes@1.3.0
  slack: circleci/slack@4.9.3
  snyk: rbmarketplace/snyk@0.1.12
  secret: rbmarketplace/secret@0.0.24
  dam: rbmarketplace/default-app-metrics-orb@2
  queue: eddiewebb/queue@3.1.4
  deploy: rbmarketplace/deploy@1
  node: circleci/node@5.0.3

parameters:
  application-nonprod-context:
    description: Context containing Vault credentials for nonprod and environment variables
    type: string
    default: auction-management-nonprod
  application-prod-context:
    description: Context containing Vault credentials for prod and environment variables
    type: string
    default: auction-management-prod
  qa-approvers-context:
    description: Context containing all approvers for qa enviroment
    type: string
    default: auction-management-qa-approvers
  prod-approvers-context:
    description: Context containing all approvers for prod enviroment
    type: string
    default: auction-management-approvers
  helm-release-name:
    description: Helm release name
    type: string
    default: auction-management
  k8s-namespace-prefix:
    description: Kubernetes namespace prefix used for dev, qa and prod
    type: string
    default: auction-management
  image-name:
    description: Image name displayed on Github Packages
    type: string
    default: auction-management
  team-name:
    description: Team name used to specify Vault path
    type: string
    default: auction-management
  vault-namespace:
    description: Vault namespace
    type: string
    default: auction-management
  python-version:
    description: Vault version for all jobs
    type: string
    default: "3.10.4"
  build-and-scan:
    description: Runs the workflow responsible for building, scanning and deploying the app
    type: boolean
    default: false
  deploy-only:
    description: Runs the workflow responsible solely for deploying the app
    type: boolean
    default: false
  target-image-tag:
    description: The target image tag to deploy
    type: string
    default: default
  slack-deploy-channel-id:
    description: Slack Channel ID for deploy notifications
    type: string
    default: C06JZLNS1N0
  performance-test-prod:
    description: Run performance tests in prod environment
    type: boolean
    default: false
  performance-test-env:
    description: Environment to run performance tests (dev/qa)
    type: string
    default: dev
  performance-test-env-var:
    description: Environment to run performance tests (dev/qa)
    type: string
    default: dev
  run-perf-test:
    type: boolean
    default: false
  performance-test:
    description: Run performance tests in non-prod environment
    type: boolean
    default: false
  run-perf-test-prod:
    type: boolean
    default: false
  external-url:
    description: External URL for performance testing
    type: string
    default: ""
  external-URL:
    description: External URL for performance testing
    type: string
    default: ""

on-push-main: &on-push-main
  branches:
    only: /main/
  tags:
    ignore: /.*/

commands:
  prepare-pipeline-environment-variables:
    description: Retrieve secrets from Vault
    steps:
      - secret/set-vault-token
      - secret/export-secret-to-env:
          namespace: admin/<< pipeline.parameters.vault-namespace >>
          var-name: GITHUB_PACKAGE_TOKEN
          secret-path: secret/svc/delivery-pipeline/github
          secret-key: github_package_token
      - secret/export-secret-to-env:
          namespace: admin/shared
          var-name: SNYK_TOKEN
          secret-path: secret/svc/snyk
          secret-key: snyk-token
      - secret/export-secret-to-env:
          namespace: admin/shared
          var-name: SLACK_ACCESS_TOKEN
          secret-path: secret/svc/slack
          secret-key: slack_access_token
      - secret/export-secret-to-env:
          namespace: admin/<< pipeline.parameters.vault-namespace >>
          var-name: CC_TEST_REPORTER_ID
          secret-path: secret/svc/delivery-pipeline/code-climate/auction-management
          secret-key: CC_REPORTER_ID
      - secret/export-value-to-env:
          var-name: SLACK_DEFAULT_CHANNEL
          value: C06JZLNS1N0
      - secret/export-secret-to-env:
          namespace: admin/<< pipeline.parameters.vault-namespace >>
          var-name: SL_ANALYZE_TOKEN
          secret-path: secret/svc/delivery-pipeline/stoplight
          secret-key: stoplight_token


  prepare-deployment-environment-variables:
    description: Retrieve deployment secrets from Vault
    steps:
      - secret/set-vault-token
      - run:
          command: |
            echo $CLUSTER_ENV
            bash scripts/determine_cluster_name.sh
            echo $CLUSTER_NAME
      - secret/export-secret-to-env:
          namespace: admin/shared
          var-name: SLACK_ACCESS_TOKEN
          secret-path: secret/svc/slack
          secret-key: slack_access_token
      - secret/export-secret-to-env:
          before-secret-retrieval:
            - run:
                command: bash scripts/determine_cluster_name.sh
          namespace: admin/shared
          var-name: KUBE_CONFIG
          dynamic-secret-path: echo secret/svc/kubernetes/$CLUSTER_NAME
          secret-key: kubeconfig

  set-environment:
    description: Generate environment credentials and configuration from templates
    steps:
      - secret/set-vault-token
      - prepare-deployment-environment-variables
      - secret/export-secret-to-env:
          before-secret-retrieval:
            - run:
                command: bash scripts/determine_cluster_name.sh
          namespace: admin/shared
          var-name: KUBE_CONFIG
          dynamic-secret-path: echo secret/svc/kubernetes/$CLUSTER_NAME
          secret-key: kubeconfig
      - run:
          name: Generate kube config file
          command: |
            mkdir $HOME/.kube
            echo -e "$KUBE_CONFIG" > $HOME/.kube/config
            chmod go-r ~/.kube/config

  install-cosign:
    description: Install cosign
    parameters:
      cosign-version:
        description: Must provide the release version of cosign
        type: string
        default: v1.6.0
    steps:
      - run:
          name: Install cosign
          command: |
            wget "https://github.com/sigstore/cosign/releases/download/<< parameters.cosign-version >>/cosign-linux-amd64"
            sudo mv cosign-linux-amd64 /usr/local/bin/cosign
            sudo chmod +x /usr/local/bin/cosign

executors:
  docker-base-executor:
    docker:
      - image: cimg/base:stable
  docker-openjdk-executor:
    docker:
      - image: cimg/openjdk:21.0.2
  docker-python-executor:
    docker:
      - image: cimg/python:<< pipeline.parameters.python-version >>
  docker-node16:
    docker:
      - image: cimg/node:16.20.2

jobs:
  download-common-tools:
    executor: docker-base-executor
    parameters:
      snyk-to-html-version:
        description: Must provide the release version of snyk-to-html
        type: string
        default: v2.3.2
    steps:
      - run:
          name: Download cc-test-reporter
          command: |
            curl -L codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ./cc-test-reporter
            chmod +x ./cc-test-reporter
      - persist_to_workspace:
          root: ./
          paths:
            - cc-test-reporter

  static-analysis:
    executor: docker-openjdk-executor
    environment:
      - JACOCO_SOURCE_PATH: application/src/main/java
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - restore_cache:
          key: circleci-java-maven-{{checksum "build.gradle"}}
      - prepare-pipeline-environment-variables
      - snyk/scan:
          target-file: application/build.gradle
          monitor-on-build: true
          project: '${CIRCLE_PROJECT_REPONAME}/${CIRCLE_BRANCH}-dependency'
          policy-path: .snyk
      - snyk/scan:
          command: code test
          artifact-destination: snyk-code-report
      - run:
          name: CheckStyle & PMD & SpotBugs
          command: |
            ./gradlew check -x test -x uiTest -x componentTest -x contractTest -x integrationTest
      - store_artifacts:
          path: ~/project/application/build/reports/checkstyle
          destination: checkstyle
      - store_artifacts:
          path: ~/project/application/build/reports/pmd
          destination: pmd
      - store_artifacts:
          path: ~/project/application/build/reports/spotbugs/task.html
          destination: spotbugs-report.html
      - run:
          name: Unit test
          command: |
            ./gradlew test
      - run:
          name: Integration test
          command: |
            ./gradlew integrationTest
      - run:
          name: Component test
          command: |
            ./gradlew componentTest
      - run:
          name: Generate Jacoco report
          command: |
            ./gradlew jacocoTestReport
      - run:
          name: Save test results
          command: |
            mkdir -p ~/junit/
            find . -type f -regex ".*/build/test-results/.*xml" -exec cp {} ~/junit/ \;
          when: always
      - run:
          name: Format coverage report
          command: |
            ./cc-test-reporter format-coverage ./application/build/reports/jacoco/test/jacocoTestReport.xml \
              -t jacoco \
              -o ./codeclimate.json
      - persist_to_workspace:
          root: ./
          paths:
            - codeclimate.json
      - store_test_results:
          path: ~/junit
      - run:
          name: Jacoco Test Coverage Verification
          command: |
            ./gradlew jacocoTestCoverageVerification
      - save_cache:
          key: circleci-java-maven-{{checksum "build.gradle"}}
          paths:
            - ~/.gradle
            - ~/.m2/repository
      - slack/notify:
          event: fail
          template: basic_fail_1

  contract-test:
    executor: docker-openjdk-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - prepare-pipeline-environment-variables
      - run:
          name: run contract test
          command: |
            ./gradlew contractTest
      - slack/notify:
          event: fail
          template: basic_fail_1
  perf-testing:
    parameters:
      environment:
        type: enum
        enum: [ "dev", "qa" ,"prod" ]
      k8s-namespace:
        type: string
      external-URL:
        type: string
        default: ""
      port-forward:
        type: boolean
        default: true
    executor: docker-base-executor
    environment:
      IMAGE_NAME: << pipeline.parameters.image-name >>
      POD_NAME: k6-perf-test-tool
      CONTAINER_NAME: k6-perf-test-tool-container
      PVC_NAME: auction-management-perf-test-output-pvc
    steps:
      - checkout
      - run:
          name: Set execute permissions for scripts
          command: |
            chmod +x ./scripts/circle_ci/setup-test-pod.sh
            chmod +x ./scripts/circle_ci/cleanup-test-pod.sh
            chmod +x ./scripts/circle_ci/run-smoke-test.sh
      - restore_cache:
          key: circleci-java-maven-{{checksum "README.md"}}
      - save_cache:
          key: circleci-java-maven-{{checksum "README.md"}}
          paths:
            - ~/.m2/repository
      - prepare-pipeline-environment-variables
      - kubernetes/install-kubectl
      - set-environment
      - secret/export-kubetoken-to-env:
          environment: << parameters.environment >>
          kube-namespace: << parameters.k8s-namespace >>
          team: << pipeline.parameters.team-name >>
          var-name: HELM_KUBETOKEN
      - run:
          name: Run the smoke performance test
          command: |
            echo "setup test pod"
            KUBE_CONTEXT=$(kubectl config current-context)
            ./scripts/circle_ci/setup-test-pod.sh \
              ${HELM_KUBETOKEN} \
              ${KUBE_CONTEXT} \
              << parameters.k8s-namespace >> \
              ${PVC_NAME} \
              ${POD_NAME} \
              ${CONTAINER_NAME} \
              << parameters.environment >> \
              << parameters.external-URL >>

            echo "run smoke test"
            ./scripts/circle_ci/run-smoke-test.sh \
              /scripts/performance_tests/k6_smoke_perf_test.js \
              0 \
              0 \
              auction_management_smoke_test_fail \
              auction_management_smoke_test_success \
              ${HELM_KUBETOKEN} \
              ${KUBE_CONTEXT} \
              << parameters.k8s-namespace >> \
              ${POD_NAME} \
              ${CONTAINER_NAME} && K6_EXIT_STATUS=$? || K6_EXIT_STATUS=$?

            echo "k6 exists with code $K6_EXIT_STATUS."

            # Check if files exist in pod and show directory contents
            echo "Checking test output directory contents:"
            kubectl --token "${HELM_KUBETOKEN}" \
              --context ${KUBE_CONTEXT} \
              -n << parameters.k8s-namespace >> \
              exec ${POD_NAME} -c ${CONTAINER_NAME} -- \
              ls -la /test_output/

            # Create directory for test results
            mkdir -p ./scripts/performance_tests/

            # Copy HTML report with error handling
            echo "Copying HTML report..."
            kubectl --token "${HELM_KUBETOKEN}" \
              --context ${KUBE_CONTEXT} \
              -n << parameters.k8s-namespace >> \
              cp ${POD_NAME}:/test_output/smoke_perf_test_result.html -c ${CONTAINER_NAME} \
              ./scripts/performance_tests/smoke_perf_test_result.html \
              || echo "Warning: Failed to copy HTML report"

            # Copy TXT report with error handling
            echo "Copying TXT report..."
            kubectl --token "${HELM_KUBETOKEN}" \
              --context ${KUBE_CONTEXT} \
              -n << parameters.k8s-namespace >> \
              cp ${POD_NAME}:/test_output/smoke_perf_test_result.txt -c ${CONTAINER_NAME} \
              ./scripts/performance_tests/smoke_perf_test_result.txt \
              || echo "Warning: Failed to copy TXT report"

            # Create empty files if they don't exist
            touch ./scripts/performance_tests/smoke_perf_test_result.html
            touch ./scripts/performance_tests/smoke_perf_test_result.txt

            echo "cleanup test pod"
            ./scripts/circle_ci/cleanup-test-pod.sh \
              ${HELM_KUBETOKEN} \
              ${KUBE_CONTEXT} \
              << parameters.k8s-namespace >> \
              ${PVC_NAME} \
              ${POD_NAME}

            # Exit with the k6 status code
            exit $K6_EXIT_STATUS

      - store_artifacts:
          path: ./scripts/performance_tests/smoke_perf_test_result.html
          destination: smoke_perf_test_result.html

      - store_artifacts:
          path: ./scripts/performance_tests/smoke_perf_test_result.txt
          destination: smoke_perf_test_result.txt

      - slack/notify:
          channel: << pipeline.parameters.slack-deploy-channel-id >>
          event: fail
          template: basic_fail_1
  ui-test:
    executor: docker-openjdk-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - prepare-pipeline-environment-variables
      - restore_cache:
          key: circleci-java-playwright-maven-{{checksum "build.gradle"}}
      - run:
          name: install browser dependencies
          command: sudo apt-get update &&  sudo apt-get install -y libnss3 libnspr4 libatk1.0-0 libatk-bridge2.0-0  libcups2 libdrm2 libxkbcommon0 libatspi2.0-0 libxcomposite1 libxdamage1  libxfixes3 libxrandr2 libgbm1 libpango-1.0-0 libcairo2 libasound2
      - save_cache:
         key: circleci-java-playwright-maven-{{checksum "build.gradle"}}
         paths:
          - ~/.gradle
          - ~/.m2/repository
      - run:
          name: run ui-test
          command: |
            ./gradlew uiTest -Pdev=dev
          continue_on_fail: false
      - run:
          name: Save test results
          command: |
           mkdir -p ~/junit/
           find . -type f -regex ".*/build/test-results/.*xml" -exec cp {} ~/junit/ \;
          when: always
      - store_test_results:
          path: ~/junit
      - store_artifacts:
          path: ~/project/application/build/reports/tests/uiTests/
          destination: uiTests-testreport.html
  generate-api-docs:
    executor: docker-openjdk-executor
    steps:
      - checkout
      - prepare-pipeline-environment-variables
      - attach_workspace:
          at: ~/project
      - kubernetes/install-kubectl
      - run:
          name: Set environment name variable
          command: echo "export ENVIRONMENT=dev" >> $BASH_ENV
      - prepare-pipeline-environment-variables
      - set-environment
      - secret/export-kubetoken-to-env:
          environment: dev
          kube-namespace: auction-management-dev
          team: << pipeline.parameters.team-name >>
          var-name: HELM_KUBETOKEN
      - run:
          name: Setup Port Forwarding for Dev
          command: |
            kubectl port-forward --token "${HELM_KUBETOKEN}" -n auction-management-dev svc/auction-management 8080:8080
          background: true
      - restore_cache:
          key: circleci-java-maven-{{checksum "build.gradle"}}
      - run:
          name: generate openapi spec
          command: |
            ./gradlew generateOpenApiDocs
      - persist_to_workspace:
          root: ./
          paths:
            - application/build/openapi.json

  stoplight-publish:
    executor: docker-node16
    steps:
      - checkout
      - prepare-pipeline-environment-variables
      - attach_workspace:
          at: ~/project
      - run:
          name: "Publish api spec and docs to stoplight"
          command: npx @stoplight/cli@6.0.1426 push --ci-token $SL_ANALYZE_TOKEN -b main

  build-package:
    executor: docker-openjdk-executor
    environment:
      IMAGE_NAME: << pipeline.parameters.image-name >>
    steps:
      - checkout
      - install-cosign
      - setup_remote_docker:
          version: docker24
      - helm/install-helm-client:
          version: v3.8.1
      - restore_cache:
          key: circleci-java-maven-{{checksum "build.gradle"}}
      - save_cache:
          key: circleci-java-maven-{{checksum "build.gradle"}}
          paths:
            - ~/.gradle
            - ~/.m2/repository
      - prepare-pipeline-environment-variables
      - run:
          name: Export tag to environment
          command: echo "export TAG=$(git rev-parse --short HEAD)" >> $BASH_ENV
      - run:
          name: Build image
          command: |
            ./gradlew jibDockerBuild -Djib.to.tags=$TAG -Djib.container.labels="org.opencontainers.image.source=https://github.com/${CIRCLE_PROJECT_USERNAME}/${CIRCLE_PROJECT_REPONAME}"
      - snyk/scan:
          monitor-on-build: true
          project: '${CIRCLE_PROJECT_REPONAME}/${CIRCLE_BRANCH}-container'
          docker-image-name: ghcr.io/rbmarketplace/$IMAGE_NAME:$TAG
      - run:
          name: Push image
          command: |
            echo $GITHUB_PACKAGE_TOKEN | docker login -u username --password-stdin ghcr.io/rbmarketplace
            docker push ghcr.io/rbmarketplace/$IMAGE_NAME:$TAG
      - run:
          name: Sign image
          command: |
            export VAULT_NAMESPACE=admin/shared
            cosign sign --key hashivault://image_signing_key ghcr.io/rbmarketplace/$IMAGE_NAME:$TAG
      - run:
          name: Package and push chart
          command: |
            helm version
      - slack/notify:
          event: fail
          template: basic_fail_1

  upload-coverage:
    executor: docker-base-executor
    steps:
      - checkout
      - attach_workspace:
          at: ~/project
      - prepare-pipeline-environment-variables
      - run:
          name: Upload coverage reports to Code Climate
          command: |
            ./cc-test-reporter upload-coverage -i codeclimate.json

  pre-prod-deploy:
    executor: docker-base-executor
    steps:
      - run:
          name: Generate release notes
          command: |
            echo "generate release notes"

  semantic-release:
    executor: node/default
    parameters:
      options:
        type: string
        default: ""
    steps:
      - checkout
      - secret/set-vault-token
      - secret/export-secret-to-env:
          dynamic-secret-path: echo secret/svc/delivery-pipeline/github
          namespace: admin/<< pipeline.parameters.vault-namespace >>
          var-name: GITHUB_PACKAGE_TOKEN
          secret-key: github_package_token
      - run:
          name: Configure Git credentials
          command: |
            git config --global user.email "<EMAIL>"
            git config --global user.name "CircleCI"
            git remote set-url origin https://${GITHUB_PACKAGE_TOKEN}@github.com/RBMarketplace/auction-management.git
      - run:
          name: Run semantic-release
          command: |
            export GH_TOKEN=${GITHUB_PACKAGE_TOKEN}
            npx semantic-release --branches ${CIRCLE_BRANCH} << parameters.options >> > semantic-output.log
      - run:
          name: get cr-info
          command: |
            mkdir -p cr-info
            cat semantic-output.log
            cat semantic-output.log | grep "^# [0-9]\|^## [0-9]" > cr-info/change.log || true
            sed -n '/^### /,$p' semantic-output.log > cr-info/details.log || true
      - persist_to_workspace:
          root: .
          paths:
            - cr-info/change.log
            - cr-info/details.log

workflows:
  deploy-only:
    when:
      or:
        - not:
            equal: [ default, << pipeline.parameters.target-image-tag >> ]
        - and:
            - << pipeline.parameters.deploy-only >>
            - not: << pipeline.parameters.build-and-scan >>
    jobs:
      - deploy/helm:
          name: Deploy-dev
          environment: dev
          timeout: 3
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-dev
          helm-release-name: << pipeline.parameters.helm-release-name >>
          image-repository: ghcr.io/rbmarketplace/<< pipeline.parameters.image-name >>
          team-name: << pipeline.parameters.team-name >>
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          post-steps:
            - slack/notify:
                channel: C06JZLNS1N0
                event: fail
                template: basic_fail_1
          pre-steps:
            - secret/set-vault-token
            - secret/export-secret-to-env:
                dynamic-secret-path: echo secret/svc/slack
                namespace: admin/shared
                secret-key: slack_access_token
                var-name: SLACK_ACCESS_TOKEN
      - dam/publish:
          name: Publish-default-observability-dev
          env: dev
          config_file: "./.damslo.yaml"
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Deploy-dev
      - qa-approval:
          name: qa-approval
          type: approval
          filters: *on-push-main
          requires:
            - Deploy-dev
      - deploy/helm:
          name: Deploy-qa
          environment: qa
          timeout: 3
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-qa
          helm-release-name: << pipeline.parameters.helm-release-name >>
          image-repository: ghcr.io/rbmarketplace/<< pipeline.parameters.image-name >>
          team-name: << pipeline.parameters.team-name >>
          context:
            - << pipeline.parameters.qa-approvers-context >>
            - << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - qa-approval
          post-steps:
            - slack/notify:
                channel: C06JZLNS1N0
                event: fail
                template: basic_fail_1
          pre-steps:
            - secret/set-vault-token
            - secret/export-secret-to-env:
                dynamic-secret-path: echo secret/svc/slack
                namespace: admin/shared
                secret-key: slack_access_token
                var-name: SLACK_ACCESS_TOKEN
      - semantic-release:
          name: semantic-release dry-run
          context: << pipeline.parameters.application-nonprod-context >>
          options: --dry-run
          filters: *on-push-main
          requires:
            - Deploy-qa
      - dam/publish:
          name: Publish-default-observability-qa
          env: qa
          config_file: "./.damslo.yaml"
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Deploy-qa
      - prod-approval:
          name: prod-approval
          type: approval
          filters: *on-push-main
          requires:
            - Deploy-qa
      - deploy/helm:
          name: Deploy-prod
          environment: prod
          timeout: 3
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-prod
          helm-release-name: << pipeline.parameters.helm-release-name >>
          image-repository: ghcr.io/rbmarketplace/<< pipeline.parameters.image-name >>
          team-name: << pipeline.parameters.team-name >>
          context:
            - << pipeline.parameters.prod-approvers-context >>
            - << pipeline.parameters.application-prod-context >>
          filters: *on-push-main
          requires:
            - prod-approval
            - semantic-release dry-run
          post-steps:
            - slack/notify:
                channel: C06JZLNS1N0
                event: fail
                template: basic_fail_1
            - slack/notify:
                channel: C06JZLNS1N0
                event: pass
                template: basic_success_1
          pre-steps:
            - secret/set-vault-token
            - secret/export-secret-to-env:
                dynamic-secret-path: echo secret/svc/slack
                namespace: admin/shared
                secret-key: slack_access_token
                var-name: SLACK_ACCESS_TOKEN
      - semantic-release:
          name: semantic-release
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Deploy-prod
      - dam/publish:
          name: Publish-default-observability-prod
          env: prod
          config_file: "./.damslo.yaml"
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Deploy-prod

  build-and-deploy:
    when:
      and:
        - << pipeline.parameters.build-and-scan >>
        - equal: [ false, << pipeline.parameters.performance-test-prod >> ]
        - equal: [ false, << pipeline.parameters.performance-test >> ]
        - equal: [ "", << pipeline.parameters.external-URL >> ]
    jobs:
      - download-common-tools:
          name: Download-common-tools
          filters: *on-push-main
      - static-analysis:
          name: Static-analysis
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Download-common-tools
      - upload-coverage:
          name: Upload-coverage
          context: << pipeline.parameters.application-nonprod-context >>
          requires:
            - Static-analysis
      - contract-test:
          name: Contract-test
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Static-analysis
      - ui-test:
          name: ui-test
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Deploy-dev
      - build-package:
          name: Build-package
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Static-analysis
      - deploy/helm:
          name: Deploy-dev
          environment: dev
          timeout: 3
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-dev
          helm-release-name: << pipeline.parameters.helm-release-name >>
          image-repository: ghcr.io/rbmarketplace/<< pipeline.parameters.image-name >>
          team-name: << pipeline.parameters.team-name >>
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Contract-test
            - Build-package
          post-steps:
            - slack/notify:
                channel: C06JZLNS1N0
                event: fail
                template: basic_fail_1
          pre-steps:
            - secret/set-vault-token
            - secret/export-secret-to-env:
                dynamic-secret-path: echo secret/svc/slack
                namespace: admin/shared
                secret-key: slack_access_token
                var-name: SLACK_ACCESS_TOKEN
      - dam/publish:
          name: Publish-default-observability-dev
          context: << pipeline.parameters.application-nonprod-context >>
          env: dev
          filters: *on-push-main
          requires:
            - Deploy-dev
      - qa-approval:
          name: qa-approval
          type: approval
          filters: *on-push-main
          requires:
            - Deploy-dev
      - deploy/helm:
          name: Deploy-qa
          environment: qa
          timeout: 3
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-qa
          helm-release-name: << pipeline.parameters.helm-release-name >>
          image-repository: ghcr.io/rbmarketplace/<< pipeline.parameters.image-name >>
          team-name: << pipeline.parameters.team-name >>
          approval-job-name: qa-approval
          context:
            - << pipeline.parameters.qa-approvers-context >>
            - << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - qa-approval
          post-steps:
            - slack/notify:
                channel: C06JZLNS1N0
                event: fail
                template: basic_fail_1
          pre-steps:
            - secret/set-vault-token
            - secret/export-secret-to-env:
                dynamic-secret-path: echo secret/svc/slack
                namespace: admin/shared
                secret-key: slack_access_token
                var-name: SLACK_ACCESS_TOKEN
      - semantic-release:
          name: semantic-release dry-run
          context: << pipeline.parameters.application-nonprod-context >>
          options: --dry-run
          filters: *on-push-main
          requires:
            - Deploy-qa
      - generate-api-docs:
          name: Generate API Docs
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Deploy-dev
      - stoplight-publish:
          name: Stoplight Publish
          context: << pipeline.parameters.application-nonprod-context >>
          filters: *on-push-main
          requires:
            - Generate API Docs
      - dam/publish:
          name: Publish-default-observability-qa
          context: << pipeline.parameters.application-nonprod-context >>
          env: qa
          filters: *on-push-main
          requires:
            - Deploy-qa
      - prod-approval:
          name: prod-approval
          type: approval
          filters: *on-push-main
          requires:
            - Deploy-qa
      - deploy/helm:
          name: Deploy-prod
          environment: prod
          timeout: 3
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-prod
          helm-release-name: << pipeline.parameters.helm-release-name >>
          image-repository: ghcr.io/rbmarketplace/<< pipeline.parameters.image-name >>
          team-name: << pipeline.parameters.team-name >>
          approval-job-name: prod-approval
          context:
            - << pipeline.parameters.prod-approvers-context >>
            - << pipeline.parameters.application-prod-context >>
          filters: *on-push-main
          requires:
            - prod-approval
            - semantic-release dry-run
          post-steps:
            - slack/notify:
                channel: C06JZLNS1N0
                event: fail
                template: basic_fail_1
            - slack/notify:
                channel: C06JZLNS1N0
                event: pass
                template: basic_success_1
          pre-steps:
            - secret/set-vault-token
            - secret/export-secret-to-env:
                dynamic-secret-path: echo secret/svc/slack
                namespace: admin/shared
                secret-key: slack_access_token
                var-name: SLACK_ACCESS_TOKEN
      - semantic-release:
          name: semantic-release
          context: << pipeline.parameters.application-prod-context >>
          filters: *on-push-main
          requires:
            - Deploy-prod
      - dam/publish:
          name: Publish-default-observability-prod
          context: << pipeline.parameters.application-prod-context >>
          env: prod
          filters: *on-push-main
          requires:
            - Deploy-prod


    # Non-prod performance testing workflow for dev
  on-demand-performance-test-dev:
    when:
      and:
        - equal: [ true, << pipeline.parameters.run-perf-test >> ]
        - equal: [ "dev", << pipeline.parameters.performance-test-env-var >> ]
        - not:
            matches:
              pattern: ^https:\/\/api.*ritchiebros\.com\/auction-management\/v1$
              value: << pipeline.parameters.external-URL >>
    jobs:
      - perf-testing:
          environment: dev
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-dev
          name: Test performance in dev
          context: << pipeline.parameters.application-nonprod-context >>
          filters:
            branches:
              only: /.*/

  # Non-prod performance testing workflow for qa
  on-demand-performance-test-qa:
    when:
      and:
        - equal: [ true, << pipeline.parameters.run-perf-test >> ]
        - equal: [ "qa", << pipeline.parameters.performance-test-env-var >> ]
        - not:
            matches:
              pattern: ^https:\/\/api.*ritchiebros\.com\/auction-management\/v1$
              value: << pipeline.parameters.external-URL >>
    jobs:
      - perf-testing:
          environment: qa
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-qa
          name: Test performance in qa
          context: << pipeline.parameters.application-nonprod-context >>
          filters:
            branches:
              only: /.*/

  # Prod performance testing workflow
  on-demand-performance-test-prod:
    when:
      equal: [ true, << pipeline.parameters.run-perf-test-prod >> ]
    jobs:
      - perf-testing:
          environment: prod
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-prod
          name: Test performance in prod
          context: << pipeline.parameters.application-prod-context >>
          pre-steps:
            - run:
                name: Set environment variable
                command: |
                  echo "export ENVIRONMENT=prod" >> $BASH_ENV
                  source $BASH_ENV
            - secret/set-vault-token
          filters:
            branches:
              only: /main/

  # External URL performance testing workflow
  on-demand-performance-test-using-external-url:
    when:
      and:
        - matches:
            pattern: ^https:\/\/api.*ritchiebros\.com\/auction-management\/v1$
            value: << pipeline.parameters.external-URL >>
        - not: << pipeline.parameters.performance-test-prod >>
        - not: << pipeline.parameters.run-perf-test >>
    jobs:
      - perf-testing:
          environment: << pipeline.parameters.performance-test-env-var >>
          k8s-namespace: << pipeline.parameters.k8s-namespace-prefix >>-<< pipeline.parameters.performance-test-env-var >>
          name: Test performance using external URL << pipeline.parameters.external-URL >>
          external-URL: << pipeline.parameters.external-URL >>
          port-forward: false
          context: << pipeline.parameters.application-nonprod-context >>
          filters:
            branches:
              only:
                - /main/
                - /feature\/.*/
                - /dev\/.*/