{"blocks": [{"type": "header", "text": {"type": "plain_text", "text": "$CIRCLE_PROJECT_REPONAME Deployed to  ${SLACK_DEPLOY_ENV} :tada:", "emoji": true}}, {"type": "section", "text": {"type": "mrkdwn", "text": ":package: *Service*: $CIRCLE_PROJECT_REPONAME"}}, {"type": "section", "text": {"type": "mrkdwn", "text": ":round_pushpin: *Environment*: $SLACK_DEPLOY_ENV"}}, {"type": "section", "text": {"type": "mrkdwn", "text": ":scroll: *Commit*: $CIRCLE_SHA1"}}, {"type": "section", "text": {"type": "mrkdwn", "text": ":calendar: *When*: $(date --iso-8601=minutes)"}}, {"type": "actions", "elements": [{"type": "button", "action_id": "success_tagged_deploy_view", "text": {"type": "plain_text", "text": "View Job"}, "url": "${CIRCLE_BUILD_URL}"}]}, {"type": "divider"}]}