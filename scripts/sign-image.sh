#!/usr/bin/env bash
set -eo pipefail

export IMAGE=$1

usage() {
    cat <<EOM
    Requires image argument.
    Usage:
    $(basename $0) image
    "* image: name of the image to be signed. Format image:tag@digest"
EOM
    exit 1
}
[  $# -le 0 ] && { usage; }


export VAULT_ADDR=https://marketplace-nonprod-vault-cluster.vault.a8ac8847-786e-4c3e-b97a-34e8978986f4.aws.hashicorp.cloud:8200
export VAULT_NAMESPACE=admin/shared

if (vault token capabilities transit);  then
  echo "Already logged into vault."
else
  echo "Logging in to vault"
  vault login -no-print -method=oidc -namespace=admin role="auction-management-oidc-role"
fi

echo "Signing image $IMAGE"
cosign sign --key hashivault://image_signing_key "$IMAGE"
