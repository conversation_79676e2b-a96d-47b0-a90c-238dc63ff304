#!/bin/bash

EXCLUDE=("$(pwd)/.gradle" "$(pwd)/.idea" "$(pwd)/build" "$(pwd)/application/build" "$(pwd)/skaffold.yaml" "$(pwd)/docs/PROJECT_INITIALIZATION.md" "$(pwd)/scripts/template-update.sh")

replace_capability_base() {
  replace_capability_base_helper "$(pwd)"
}

replace_capability_base_helper() {
  local dir=$1
  if contains "${EXCLUDE[@]}" "$dir"; then
    return
  fi

  if [ -d "$dir" ]; then
    for filename in "$dir"/* "$dir"/.*
      do
        if [[ "$filename" == "$dir/." || "$filename" == "$dir/.." ]]; then
            continue
        fi

        if [[ "$filename" == *".circleci" || ( -d "$filename" && "$filename" != "$dir"/.*) ]]; then
#        if contain "$filename" ".circleci" || [[ -d "$filename" ]]; then
            replace_capability_base_helper "$filename"
        else
          if contains "${EXCLUDE[@]}" "$filename" ||
          [[ "$filename" == *.png || "$filename" == *.jar || "$filename" == *".*" || "$filename" == *"/."* && "$filename" != *".circleci"* ]]; then
              continue
          fi
          # replace the service name
          sed -i "" "s/auction-management/$SERVICE_NAME/g" "$filename"
          # replace the vault kubernetes role name, role name is based on the team name
          sed -i "" "s/role: marketplace-excavator-kubernetes-role/role: $TEAM_NAME-kubernetes-role/g" "$filename"
          # replace the vault namespace
          sed -i "" "s/secretPath: marketplace-excavator\/secret\/data\/svc/secretPath: $VAULT_NAMESPACE_NAME\/secret\/data\/svc/g" "$filename"
          sed -i "" "s/with secret \"marketplace-excavator/with secret \"$VAULT_NAMESPACE_NAME/g" "$filename"
          # replace the team name
          sed -i "" "s/marketplace-excavator/$TEAM_NAME/g" "$filename"
          # replace the k8s namespace
          sed -i "" "s/excavator/$K8S_NAMESPACE_NAME/g" "$filename"
#          echo "$filename"
        fi
      done
  fi
}

contains() {
  local list=("$@")
  local element="${list[-1]}"
  unset list[-1]

  for e in "${list[@]}"
  do
    if [ "$e" == "$element" ]; then
      return 0
    fi
  done
  return 1
}

read -p "Enter service name: " SERVICE_NAME
read -p "Enter team name: " TEAM_NAME
read -p "Enter Kubernetes namespace name: " K8S_NAMESPACE_NAME
read -p "Enter Vault namespace name: " VAULT_NAMESPACE_NAME

replace_capability_base "$SERVICE_NAME" "$K8S_NAMESPACE_NAME"
