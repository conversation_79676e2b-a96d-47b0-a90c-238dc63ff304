#!/bin/bash

n=0
m=$(($2+1))
ddmetricFail="${4}"
ddmetric="${5}"

until [ $n -ge $m ]
do
  kubectl exec \
                --token "${6}" \
                --context "${7}" \
                -it "${9}" -c "${10}" \
                -n "${8}" \
                -- k6 run "${1}" --env K6_CALLER_SOURCE="K6-PERF" --env K6_OUTPUT_PATH="/test_output/"
  if [ $? -eq 0 ]; then
      if [ -n "$5" ]; then
            #if $5 is set increment DD metric named $5
            echo "${ddmetric}:1|c" | nc -w 1 -u 127.0.0.1 8125 -v
            echo "Successful try added to DD metric ${ddmetric}"
      fi
    exit 0
  elif [ -n "$4" ]; then
    #if $4 is set increment DD metric named $4
    echo "${ddmetricFail}:1|c" | nc -w 1 -u 127.0.0.1 8125 -v
    echo "Failed try added to DD metric ${ddmetricFail}"
  fi
  n=$((n+1))
  if [ $n -le $2 ]; then
    echo "Retry running in $3 seconds, $n..."
    sleep $3
  fi
done
exit 1;