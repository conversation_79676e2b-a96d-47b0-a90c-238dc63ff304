#!/bin/bash

HELM_KUBETOKEN=$1
KUBE_CONTEXT=$2
K8S_NAMESPACE=$3
PVC_NAME=$4
POD_NAME=$5
CONTAINER_NAME=$6
ENV=$7
EXTERNAL_URL=$8

sed -e "s/auction-management-test-output-pvt/${PVC_NAME}/" ./scripts/pvc.yaml | \
kubectl apply \
    --token "${HELM_KUBETOKEN}" \
    --context "${KUBE_CONTEXT}" \
    -f - \
    -n "${K8S_NAMESPACE}"

echo "Waiting for pvc to be ready..."
timeout 30 sh -c \
    "while ! kubectl \
      --token \"${HELM_KUBETOKEN}\" \
      --context \"${KUBE_CONTEXT}\" \
      -n \"${K8S_NAMESPACE}\" \
      get \"pvc/${PVC_NAME}\" | grep  -E 'Pending|Bound' >/dev/null 2>&1 ;  do sleep 1; done"
sleep 1

sed -e "s/k6-test-tool/$POD_NAME/" -e "s/auction-management-test-output-pvt/$PVC_NAME/" ./scripts/pod_for_k6.yaml | \
kubectl apply \
    --token "${HELM_KUBETOKEN}" \
    --context "${KUBE_CONTEXT}" \
    -f - \
    -n "${K8S_NAMESPACE}"

echo "Waiting for k6 testing pod to be ready..."
timeout 30 sh -c \
    "while ! kubectl \
      --token \"${HELM_KUBETOKEN}\" \
      --context \"${KUBE_CONTEXT}\" \
      -n \"${K8S_NAMESPACE}\" \
      get \"pod/${POD_NAME}\" | grep Running >/dev/null 2>&1 ;  do sleep 1; done"
sleep 1

echo "Copying performance test scripts from ./scripts/performance_tests to ${POD_NAME} pod in container ${CONTAINER_NAME}..."
kubectl \
  --token "${HELM_KUBETOKEN}" \
  --context "${KUBE_CONTEXT}" \
  -n "${K8S_NAMESPACE}" \
  cp ./scripts/performance_tests ${POD_NAME}:/scripts/performance_tests -c ${CONTAINER_NAME}

# Create the test output directory in the pod
echo "Creating test output directory in pod..."
kubectl exec \
  --token "${HELM_KUBETOKEN}" \
  --context "${KUBE_CONTEXT}" \
  ${POD_NAME} -c ${CONTAINER_NAME} \
  -n "${K8S_NAMESPACE}" \
  -- mkdir -p /test_output

# Modify the BASE_URL based on whether external URL is provided
if [ -n "$EXTERNAL_URL" ]; then
  kubectl exec \
    --token "${HELM_KUBETOKEN}" \
    --context "${KUBE_CONTEXT}" \
    ${POD_NAME} -c ${CONTAINER_NAME} \
    -n "${K8S_NAMESPACE}" \
    -- sh -c \
    "sed -i \"s|const BASE_URL = '.*'|const BASE_URL = '${EXTERNAL_URL}'|g\" /scripts/performance_tests/k6_test_function.js"
else
  kubectl exec \
    --token "${HELM_KUBETOKEN}" \
    --context "${KUBE_CONTEXT}" \
    ${POD_NAME} -c ${CONTAINER_NAME} \
    -n "${K8S_NAMESPACE}" \
    -- sh -c \
    "sed -i \"s|const BASE_URL = '.*'|const BASE_URL = 'http://auction-management.${K8S_NAMESPACE}.svc.cluster.local:80/v1'|g\" /scripts/performance_tests/k6_test_function.js"
fi