apiVersion: v1
kind: Pod
metadata:
  name: k6-test-tool
  labels:
    app: k6-test-tool
spec:
  imagePullSecrets:
    - name: registry-credential
  securityContext:
    runAsUser: 1000
    fsGroup: 1000
  containers:
    - name: k6-test-tool-container
      image: ghcr.io/rbmarketplace/k6:0.0.1
      imagePullPolicy: Always
      stdin: true
      tty: true
      command: ["/bin/sh"]
      resources:
        requests:
          cpu: 400m
          memory: 512Mi
        limits:
          cpu: 1000m
          memory: 1Gi
      securityContext:
        readOnlyRootFilesystem: true
        allowPrivilegeEscalation: false
      volumeMounts:
        - mountPath: /scripts
          name: test-volume
        - mountPath: /test_output
          name: test-volume
  volumes:
    - name: test-volume
      persistentVolumeClaim:
        claimName: auction-management-test-output-pvt