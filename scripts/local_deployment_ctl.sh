#!/usr/bin/env bash
set -eo pipefail

export DEVELOPER_USER=$1
export SKAFFOLD_OPERATION=${2:-dev}
export APPLICATION_NAME=${3:-auction-management}
export DOCKER_REGISTRY=${4:-ghcr.io/rbmarketplace}
export HELM_CHARTS_FOLDER=${5:-./helm/}
export APPLICATION_FULL_NAME=$APPLICATION_NAME-$DEVELOPER_USER-local
export APPLICATION_SERVICE_LOCAL_NAME=$APPLICATION_NAME-local
export APPLICATION_DB_BASE_NAME=auction_management?currentSchema=$DEVELOPER_USER

usage() {
    cat <<EOM
    Usage:
    $(basename $0) user application [operation, registry, helm_charts_folder_path]
    "* user: a unique id to name your own deployment"
    "* operation: skaffold operation: dev/debug, default to dev"
    "* application: application name used to determine helm application"
    "* registry: docker registry for skaffold to use. Default to ghcr.io/rbmarketplace"
    "* helm charts folder path: relative path to the helm templates folder. Default to ./helm/"
EOM
    exit 0
}

[  $# -le 0 ] && { usage; }

clean_up() {
  test -d "$tmp_dir" && rm -fr "$tmp_dir"
}

tmp_dir=$( mktemp -d -t deploy-local-app-${APPLICATION_NAME} )
trap "clean_up $tmp_dir" EXIT

cp -i -r ${HELM_CHARTS_FOLDER} $tmp_dir

sed -e "s#version: v1#version: ${DEVELOPER_USER}#g"  \
    -e "s#{{applicationFullName}}#${APPLICATION_FULL_NAME}#g" \
    -e "s#{{serviceNameAttribute}}#${APPLICATION_SERVICE_LOCAL_NAME}#g" \
    -e "s/{{developerUser}}/${DEVELOPER_USER}/g" \
    -e "s#{{deploymentOwner}}#${DEVELOPER_USER}#g" \
    ${HELM_CHARTS_FOLDER}/values-local.yaml > $tmp_dir/values-local.yaml

sed -e "s#{{ IMAGE }}#${DOCKER_REGISTRY}/${APPLICATION_FULL_NAME}#g" \
    -e "s#{{ CHART_PATH }}#$tmp_dir#g" \
    -e "s#{{ APPLICATION_FULL_NAME }}#${APPLICATION_FULL_NAME}#g" \
    -e "s#{{ DB_NAME }}#${APPLICATION_DB_BASE_NAME}#g" \
    skaffold.yaml.template > skaffold.yaml && Skaffold $SKAFFOLD_OPERATION --cache-artifacts=false


