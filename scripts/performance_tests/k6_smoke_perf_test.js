import { htmlReport } from "https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js";
import { textSummary } from "https://jslib.k6.io/k6-summary/0.0.1/index.js";
import {
    scenario_getEventsByDateRange,
    scenario_getEventById,
    scenario_getEventBySaleNumber
} from "./k6_test_function.js";

// Configuration constants
const firstDuration = '30s';
const secondDuration = '60s';
const thirdDuration = '30s';

export function handleSummary(data) {
    // Use environment variable if set (for CI), otherwise use local path
    const outputPath = __ENV.K6_OUTPUT_PATH || './scripts/performance_tests/';

    return {
        [`${outputPath}smoke_perf_test_result.html`]: htmlReport(data),
        "stdout": textSummary(data, { indent: " ", enableColors: true }),
        [`${outputPath}smoke_perf_test_result.txt`]: textSummary(data, { indent: " ", enableColors: false }),
    };
}

export const options = {
    discardResponseBodies: false,
    summaryTrendStats: ['avg', 'min', 'med', 'max', 'p(90)', 'p(95)', 'p(99)', 'count'],
    setupTimeout: '300s',
    thresholds: {
        'http_req_duration{scenario:Scenario_getEventsByDateRange}': ['p(90) <= 500', 'p(95) <= 800', 'p(99) <= 1000'],
        'http_req_duration{scenario:Scenario_getEventById}': ['p(90) <= 500', 'p(95) <= 800', 'p(99) <= 1000'],
        'http_req_duration{scenario:Scenario_getEventBySaleNumber}': ['p(90) <= 500', 'p(95) <= 800', 'p(99) <= 1000'],

    },
    scenarios: {
        Scenario_getEventsByDateRange: {
            executor: 'ramping-arrival-rate',
            startRate: 5,
            timeUnit: '1m',
            preAllocatedVUs: 5,
            maxVUs: 50,
            startTime: "0s",
            stages: [
                { target: 60, duration: firstDuration },
                { target: 120, duration: secondDuration },
                { target: 60, duration: thirdDuration },
            ],
            exec: 'scenario_getEventsByDateRange',
        },
        Scenario_getEventById: {
            executor: 'ramping-arrival-rate',
            startRate: 5,
            timeUnit: '1m',
            preAllocatedVUs: 5,
            maxVUs: 50,
            startTime: "0s",
            stages: [
                { target: 60, duration: firstDuration },
                { target: 120, duration: secondDuration },
                { target: 60, duration: thirdDuration },
            ],
            exec: 'scenario_getEventById',
        },
        Scenario_getEventBySaleNumber: {
            executor: 'ramping-arrival-rate',
            startRate: 5,
            timeUnit: '1m',
            preAllocatedVUs: 5,
            maxVUs: 50,
            startTime: "0s",
            stages: [
                { target: 60, duration: firstDuration },
                { target: 120, duration: secondDuration },
                { target: 60, duration: thirdDuration },
            ],
            exec: 'scenario_getEventBySaleNumber',
        }
    }
};

export {
    scenario_getEventsByDateRange,
    scenario_getEventById,
    scenario_getEventBySaleNumber
};