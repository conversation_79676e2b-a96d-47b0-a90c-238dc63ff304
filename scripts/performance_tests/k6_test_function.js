import http from 'k6/http';
import { check, sleep } from 'k6';

const BASE_URL = 'https://api.dev.marketplace.ritchiebros.com/auction-management/v1';

export function scenario_getEventsByDateRange() {
    const response = http.get(`${BASE_URL}/events`, {
        params: {
            from: '2025-03-01T00:00:00.000Z',
            to: '2025-03-30T00:00:00.000Z'
        },
    timeout: '30s'
    });
    check(response, {
        'status is 200': (r) => r.status === 200,
        'response body is not empty': (r) => r.body.length > 0
    });
    sleep(1);
}

export function scenario_getEventById() {
    const eventId = '2BB91FB9-7067-2A9E-9E6D-34CB7FCB83A4';
    const response = http.get(`${BASE_URL}/events/${eventId}`)
    sleep(10);
    check(response, {
        'status is 200': (r) => r.status === 200,
        'response body is not empty': (r) => r.body.length > 0
    });
    sleep(1);
}

export function scenario_getEventBySaleNumber() {
    const saleNumber = '2025303';
    const response = http.get(`${BASE_URL}/events/auction/${saleNumber}`);
    check(response, {
        'status is 200': (r) => r.status === 200,
        'response body is not empty': (r) => r.body.length > 0
    });
    sleep(1);
}