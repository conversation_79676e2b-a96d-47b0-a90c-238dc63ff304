# capability base
[![Maintainability](https://api.codeclimate.com/v1/badges/b2af6ec7d249c4a44d15/maintainability)](https://codeclimate.com/repos/62627fe7bb555b0178006979/maintainability)
[![Test Coverage](https://api.codeclimate.com/v1/badges/b2af6ec7d249c4a44d15/test_coverage)](https://codeclimate.com/repos/62627fe7bb555b0178006979/test_coverage)

## Overview
This is a very basic pipeline workflow for deploying the application.
![CI-CD-Pipleline.png](images/CI-CD-Pipleline.png)

## Purpose
This application provides capabilities a sample service repo.

## Documents
- [How to set up a repository](docs/PROJECT_INITIALIZATION.md)
- [CONTRIBUTING](docs/CONTRIBUTING.md)
- [SUPPORT](docs/SUPPORT.md)
- [ADRs](./docs/architecture-decisions)
- [Product/Team space](https://rba.atlassian.net/jira/projects?contains=capability%20&page=1&selectedCategory=all&selectedProjectType=all&sortKey=name&sortOrder=ASC)

### Notes
- [README Layout Reference](https://coda.io/d/Marketplace-HQ_dhOZmqBht5r/README-layout_sujOf#_lue96)

### Database Dynamic Credentials
1. Copy the related annotation template to `helm/tempaltes/_helpers.tpl`
```YAML
{{- define "vaultInjector.podAnnotation" -}}
{{- if eq .Values.vaultInjector.enabled true -}}
traffic.sidecar.istio.io/excludeOutboundPorts: "8200"
vault.hashicorp.com/agent-inject: "true"
vault.hashicorp.com/auth-path: "{{ .Values.vaultInjector.authPath }}"
{{- range .Values.vaultInjector.creds }}
vault.hashicorp.com/agent-inject-secret-{{ .destPath }}: "{{ .vaultPath }}"
{{- end }}
vault.hashicorp.com/agent-inject-status: "update"
vault.hashicorp.com/role: "{{ .Values.vaultInjector.role }}"
vault.hashicorp.com/namespace: "admin"
{{- if eq .Values.vaultInjector.debug true }}
vault.hashicorp.com/log-level: "trace"
{{- end -}}
{{- end -}}
{{- end }}
```

2. Include the above template in `helm/templates/deployment.yaml`
```YAML
spec:
  template:
    metadata:
      annotations:
        {{- include "vaultInjector.podAnnotation" . | nindent 8 }}
```
3. Add the basic configs to `helm/values.yaml`
```YAML
vaultInjector:
  # whether to use database dynamic credentials
  enabled: false
  # whether to set log level to trace
  debug: false
  # the auth method name that used by Kubernetes to access Vault, Its value is probably like `auth/kubernetes_[nonprod|prod]`
  authPath: ""
  # the role name that used by Kubernetes to access Vault, Its value is probably like [YOUR_TEAM_NAME]-kubernetes-role
  role: ""
  # an array to load secrets from vault to local files in the folder /vault/secrets/
  creds: []
    # # the path of the Auth Role to enable database dynamic secrets
    # - vaultPath: [ YOUR_TEAM_NAME ]/database/creds/[YOUR_DATABASE_VAULT_ROLE_NAME]
    #   # default output path of the dynamic credential
    #   # if you need to change it, it needs to be changed in application.yaml: `spring.config.import`
    #   destPath: db-creds.properties
# Seconds to wait before moving from a TERM signal to the pod's main process to a KILL signal.
terminationGracePeriodSeconds: 30
```

4. Refer to the following image to set values for each env in `helm/values-*.yaml`
```YAML
# please make sure disable secretProviderClass
secretProviderClass:
  # the origin key is secretProviderClass.enable, current key is secretProviderClass.enabled
  enabled: false
vaultInjector:
  enabled: true
  authPath: auth/kubernetes_[nonprod|prod]
  role: [TEAM_NAME]-kubernetes-role
  creds:
    - vaultPath: [TEAM_NAME]/database/creds/[DATABASE_ROLE_NAME]
      destPath: db-creds.properties  
```

5. Copy the java implementation of rotation refresher to your repo: `com/rb/capability/common/config/RefreshUsernameAndPasswordForDb.java`
   and add dependency 'org.springframework.cloud:spring-cloud-starter-config:3.1.2' in build.gradle
6. Set up external application properties configs by configuration `spring.config.import` in `src/main/resources/bootstrap.yaml`:
```YAML
spring:
  config:
    # the config path is same as vaultInjector.creds.destPath
    import: optional:file:/vault/secrets/db-creds.properties
```
7. Set up the Spring database configs in `src/main/resources/bootstrap.yaml` for specific environment
```yaml
spring:
  datasource:
    # replace your datasource url
    url: ******************************************************************************************************************
    # username & password in ${} are the keys in the above vaultPath. They are unmodifiable
    username: ${username}
    password: ${password}
```
8. Set up Slack channel in the .circleci/config.yml
```yaml
commands:
  prepare-vault-environment-variables:
    steps:
      - secret/export-value-to-env:
          var-name: SLACK_DEFAULT_CHANNEL
       # replace the Slack channel name
          value: ${CHANNEL_NAME}
```
9. Set up slack access token in Vault
```
add slack_access_token in Vault on path: secret/svc/delivery-pipeline/slack
```
10. Set up code climate reporter id in the .circleci/config.yml
```
add CC_REPORTER_ID in Vault on path: secret/svc/delivery-pipeline/REPO_NAME/CC_REPORTER_ID
```
```yaml
commands:
  prepare-vault-environment-variables:
    steps:
      - secret/export-secret-to-env:
          namespace: admin/<< pipeline.parameters.team-name >>
          var-name: CC_TEST_REPORTER_ID
          secret-path: secret/svc/delivery-pipeline/code-climate/REPO_NAME
          # replace the REPO_NAME
          secret-key: CC_REPORTER_ID
```