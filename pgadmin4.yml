apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgadmin4
  namespace: dozer-dev
spec:
  replicas: 2
  selector:
    matchLabels:
      app: pgadmin-tool
  template:
    metadata:
      labels:
        app: pgadmin-tool
    spec:
      containers:
        - env:
            - name: PGADMIN_DEFAULT_EMAIL
              value: <EMAIL>
            - name: PGA<PERSON><PERSON>_DEFAULT_PASSWORD
              value: admin
            - name: PGADMIN_PORT
              value: "80"
            - name: SCRIPT_NAME
              value: /pgadmin4
          image: ghcr.io/rbmarketplace/pgadmin4:0.0.3
          imagePullPolicy: IfNotPresent
          name: pgadmin
          ports:
            - containerPort: 80
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
          resources:
            limits:
              cpu: "2"
              memory: 2Gi
            requests:
              cpu: 100m
              memory: 1.5Gi
          volumeMounts:
            - mountPath: /tmp
              name: empty-dir
      securityContext:
        runAsUser: 1000
      volumes:
        - emptyDir: { }
          name: empty-dir
